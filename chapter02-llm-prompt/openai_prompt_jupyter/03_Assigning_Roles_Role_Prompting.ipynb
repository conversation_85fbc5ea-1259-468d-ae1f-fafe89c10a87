{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 第三章：分配角色\n", "\n", "## 设置\n", "\n", "运行以下设置单元格来加载您的API密钥并建立`get_completion`辅助函数。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Requirement already satisfied: openai==1.61.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (1.61.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.10.0)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (2.11.5)\n", "Requirement already satisfied: sniffio in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.67.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.14.0)\n", "Requirement already satisfied: idna>=2.8 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from anyio<5,>=3.5.0->openai==1.61.0) (3.10)\n", "Requirement already satisfied: certifi in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (2025.4.26)\n", "Requirement already satisfied: httpcore==1.* in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai==1.61.0) (0.16.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.4.1)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n", "✅ OpenAI环境设置完成!\n", "🔧 OpenAI API 配置信息:\n", "  📡 配置来源: 环境变量 + 自定义API地址: https://vip.apiyi.com/v1\n", "  🤖 模型: gpt-4o\n", "  🌐 API地址: https://vip.apiyi.com/v1\n", "  🔑 API密钥: sk-R2utG...B944\n", "\n", "✅ 使用统一配置管理成功！\n"]}], "source": ["# 🔧 OpenAI环境自动配置\n", "# 此设置会自动从环境变量或IPython存储中加载配置\n", "\n", "# 安装OpenAI库\n", "%pip install openai==1.61.0\n", "\n", "# 导入Python内置的正则表达式库\n", "import re\n", "\n", "# 🚀 使用统一配置管理系统\n", "from config import setup_notebook_environment, print_config_info\n", "\n", "# 自动设置OpenAI客户端和get_completion函数\n", "# 优先级：环境变量 > IPython存储 > 默认值\n", "try:\n", "    client, get_completion = setup_notebook_environment()\n", "    print(\"✅ 使用统一配置管理成功！\")\n", "except Exception as e:\n", "    print(f\"❌ 统一配置失败，回退到传统方式: {e}\")\n", "    \n", "    # 回退到传统的配置方式\n", "    import openai\n", "    \n", "    # 从IPython存储中检索API_KEY和MODEL_NAME变量\n", "    %store -r API_KEY\n", "    %store -r MODEL_NAME\n", "\n", "    # 如果没有设置MODEL_NAME，使用默认值\n", "    try:\n", "        MODEL_NAME\n", "    except NameError:\n", "        MODEL_NAME = \"gpt-4o\"  # 默认使用gpt-4o模型\n", "\n", "    # 创建OpenAI客户端\n", "    client = openai.OpenAI(api_key=API_KEY)\n", "\n", "    def get_completion(prompt: str, system_prompt=\"\"):\n", "        \"\"\"\n", "        获取GPT的完成响应\n", "        \n", "        参数:\n", "            prompt (str): 用户提示\n", "            system_prompt (str): 系统提示（可选）\n", "        \n", "        返回:\n", "            str: GPT的响应文本\n", "        \"\"\"\n", "        # 构建消息列表\n", "        messages = []\n", "        \n", "        # 如果有系统提示，添加系统消息\n", "        if system_prompt:\n", "            messages.append({\"role\": \"system\", \"content\": system_prompt})\n", "        \n", "        # 添加用户消息\n", "        messages.append({\"role\": \"user\", \"content\": prompt})\n", "        \n", "        # 调用OpenAI API\n", "        response = client.chat.completions.create(\n", "            model=MODEL_NAME,              # 模型名称 (gpt-4o 或 deepseek-r1)\n", "            messages=messages,             # 消息列表\n", "            max_completion_tokens=2000,    # 最大token数\n", "            temperature=0.0               # 温度参数，0表示更确定性\n", "        )\n", "        return response.choices[0].message.content\n", "    \n", "    print(\"⚠️  使用传统配置方式，建议配置环境变量以获得更好体验\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "## 课程 \n", "\n", "前面我们提到，GPT除了您提供的内容外没有其他上下文信息。在这种情况下，**让GPT扮演特定角色（并提供相应的背景信息）**就变得非常重要。这种技术被称为\"角色提示工程\"。角色描述越详细具体，效果通常越好。\n", "\n", "**给GPT分配角色能够显著提升它在各个领域的表现**，无论是创意写作、编程开发还是数据分析。就像告诉人类专家\"请以资深专家的身份来思考这个问题\"会让他们表现得更专业一样，角色提示也能精确调节GPT回答的风格、语调和专业水平。\n", "\n", "**小贴士：** 角色设定既可以放在系统提示中，也可以直接写在用户消息里。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 示例\n", "\n", "下面我们来看几个对比例子。首先，当我们询问GPT对滑板运动的看法时，如果没有角色设定，它会给出**标准化的中性回答**。\n", "\n", "但是当我们让GPT扮演不同角色时，回答的风格和角度会发生明显变化，**整个回答的语调、用词和观点都会完全贴合角色特征**。\n", "\n", "**进阶技巧：** 你还可以**为角色指定具体的对话对象**。比如\"你是一只猫\"和\"你是一只正在向滑板爱好者介绍运动的猫\"会产生完全不同的效果。\n", "\n", "让我们先看看没有角色设定时的标准回答："]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🤖 标准回答：\n", "滑板运动是一种充满活力和创造力的运动，它不仅挑战身体的平衡和技巧，还激发个人的独特风格和表达。\n"]}], "source": ["# 📝 没有角色设定的基础提示\n", "# 这是一个普通问题，没有给AI设定任何特殊身份\n", "PROMPT = \"用一句话说说你对滑板运动的看法？\"\n", "\n", "# 显示GPT的标准回答\n", "print(\"🤖 标准回答：\")\n", "print(get_completion(PROMPT))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在让我们给GPT设定一些有趣的角色，看看回答会如何变化："]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🐱 小猫的观点：\n", "滑板运动就像在地上飞翔，喵，充满了自由和冒险的味道，真是让猫咪的胡须都颤抖的刺激呢！\n", "\n", "🏴‍☠️ 海盗船长的观点：\n", "滑板运动就像在甲板上迎风破浪，需勇气与技巧，才能在狂风巨浪中保持平衡，享受自由的快感！\n", "\n", "🤓 物理学教授的观点：\n", "滑板运动是一项通过控制重心和摩擦力来实现平衡与加速的动态物理挑战。\n"]}], "source": ["# 🐱 角色1：好奇的小猫\n", "SYSTEM_PROMPT_1 = \"你是一只对人类活动非常好奇的小猫，经常用猫咪的视角观察世界，说话时会带有猫咪的可爱特征。\"\n", "\n", "# 🏴‍☠️ 角色2：海盗船长\n", "SYSTEM_PROMPT_2 = \"你是一位经验丰富的海盗船长，说话豪迈粗犷，经常用航海术语，对冒险运动有独特见解。\"\n", "\n", "# 🤓 角色3：物理学教授\n", "SYSTEM_PROMPT_3 = \"你是一位严谨的物理学教授，习惯从科学角度分析事物，解释时会涉及力学原理。\"\n", "\n", "# 相同的问题\n", "PROMPT = \"用一句话说说你对滑板运动的看法？\"\n", "\n", "print(\"🐱 小猫的观点：\")\n", "print(get_completion(PROMPT, SYSTEM_PROMPT_1))\n", "\n", "print(\"\\n🏴‍☠️ 海盗船长的观点：\") \n", "print(get_completion(PROMPT, SYSTEM_PROMPT_2))\n", "\n", "print(\"\\n🤓 物理学教授的观点：\")\n", "print(get_completion(PROMPT, SYSTEM_PROMPT_3))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["你可以用角色提示来调节GPT的写作风格、说话语调，或者引导回答的复杂程度。**更重要的是，角色设定还能让GPT在数学和逻辑推理方面表现得更好。**"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 4}
{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 10: 工具使用\n", "\n", "\n", "## 设置\n", "\n", "运行以下设置单元格来加载您的 API 密钥并建立 `get_completion` 辅助函数。"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Requirement already satisfied: openai==1.61.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (1.61.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.10.0)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (2.11.5)\n", "Requirement already satisfied: sniffio in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.67.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.14.0)\n", "Requirement already satisfied: idna>=2.8 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from anyio<5,>=3.5.0->openai==1.61.0) (3.10)\n", "Requirement already satisfied: certifi in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (2025.4.26)\n", "Requirement already satisfied: httpcore==1.* in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai==1.61.0) (0.16.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.4.1)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n", "✅ OpenAI环境设置完成!\n", "🔧 OpenAI API 配置信息:\n", "  📡 配置来源: 环境变量 + 自定义API地址: https://vip.apiyi.com/v1\n", "  🤖 模型: gpt-4o\n", "  🌐 API地址: https://vip.apiyi.com/v1\n", "  🔑 API密钥: sk-R2utG...B944\n", "\n", "✅ 使用统一配置管理成功！\n"]}], "source": ["# 🔧 OpenAI环境自动配置\n", "# 此设置会自动从环境变量或IPython存储中加载配置\n", "\n", "# 安装OpenAI库\n", "%pip install openai==1.61.0\n", "\n", "# 导入Python内置的正则表达式库\n", "import re\n", "\n", "# 🚀 使用统一配置管理系统\n", "from config import setup_notebook_environment, print_config_info\n", "\n", "# 自动设置OpenAI客户端和get_completion函数\n", "# 优先级：环境变量 > IPython存储 > 默认值\n", "try:\n", "    client, get_completion = setup_notebook_environment()\n", "    print(\"✅ 使用统一配置管理成功！\")\n", "except Exception as e:\n", "    print(f\"❌ 统一配置失败，回退到传统方式: {e}\")\n", "    \n", "    # 回退到传统的配置方式\n", "    import openai\n", "    \n", "    # 从IPython存储中检索API_KEY和MODEL_NAME变量\n", "    %store -r API_KEY\n", "    %store -r MODEL_NAME\n", "\n", "    # 如果没有设置MODEL_NAME，使用默认值\n", "    try:\n", "        MODEL_NAME\n", "    except NameError:\n", "        MODEL_NAME = \"gpt-4o\"  # 默认使用gpt-4o模型\n", "\n", "    # 创建OpenAI客户端\n", "    client = openai.OpenAI(api_key=API_KEY)\n", "\n", "    def get_completion(prompt: str, system_prompt=\"\"):\n", "        \"\"\"\n", "        获取GPT的完成响应\n", "        \n", "        参数:\n", "            prompt (str): 用户提示\n", "            system_prompt (str): 系统提示（可选）\n", "        \n", "        返回:\n", "            str: GPT的响应文本\n", "        \"\"\"\n", "        # 构建消息列表\n", "        messages = []\n", "        \n", "        # 如果有系统提示，添加系统消息\n", "        if system_prompt:\n", "            messages.append({\"role\": \"system\", \"content\": system_prompt})\n", "        \n", "        # 添加用户消息\n", "        messages.append({\"role\": \"user\", \"content\": prompt})\n", "        \n", "        # 调用OpenAI API\n", "        response = client.chat.completions.create(\n", "            model=MODEL_NAME,              # 模型名称 (gpt-4o 或 deepseek-r1)\n", "            messages=messages,             # 消息列表\n", "            max_completion_tokens=2000,    # 最大token数\n", "            temperature=0.0               # 温度参数，0表示更确定性\n", "        )\n", "        return response.choices[0].message.content\n", "    \n", "    print(\"⚠️  使用传统配置方式，建议配置环境变量以获得更好体验\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "## 课程\n", "工具使用（也叫函数调用）看起来复杂，但其实很简单！它就是将工具执行结果插入到对话中，让AI能调用外部功能。\n", "\n", "**核心原理：**\n", "- 普通对话：我们直接给AI发送文本\n", "- 工具使用：我们让AI告诉我们要调用什么工具，然后把工具结果返回给AI\n", "\n", "**工作流程：**\n", "1. AI 说明要调用的工具名称和参数\n", "2. 我们的程序执行这个工具\n", "3. 把执行结果返回给AI继续对话"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 为什么需要工具使用？\n", "\n", "工具使用让AI能力大幅扩展，可以处理复杂的实际任务：\n", "\n", "**常见工具类型：**\n", "- 📊 数据库查询：执行SQL语句，获取数据\n", "- 🌐 网络请求：调用API，获取实时信息  \n", "- 📁 文件操作：读写文件，处理文档\n", "- 🧮 计算工具：数学运算，数据分析\n", "- 📨 消息发送：邮件、短信、通知"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 实现工具使用的两个关键要素\n", "\n", "**1. 系统提示词**\n", "- 告诉AI有哪些工具可用\n", "- 说明每个工具的用途和参数\n", "- 定义工具调用的格式\n", "\n", "**2. 控制逻辑**  \n", "- 解析AI的工具调用请求\n", "- 执行对应的工具函数\n", "- 将结果返回给AI继续对话"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 工具使用路线图\n", "\n", "*本课程教授我们当前的工具使用格式。但是，我们将在不久的将来更新和改进工具使用功能，包括：*\n", "* *更简化的函数定义和调用格式*\n", "* *更强健的错误处理和边缘情况覆盖*\n", "* *与我们 API 其余部分的更紧密集成*\n", "* *更好的可靠性和性能，特别是对于更复杂的工具使用任务*"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 实战示例：SQL数据库查询工具\n", "\n", "下面我们通过一个具体的SQL查询例子来学习工具使用。\n", "\n", "### 1. 系统提示词设计\n", "\n", "系统提示词需要包含：\n", "- 工具使用的基本说明\n", "- 具体工具的详细描述  \n", "- 调用格式要求\n", "\n", "**重要：** 使用 `<function_calls>` 标签是GPT专门训练的格式，建议严格遵循。"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 优化版系统提示词构建完成\n"]}], "source": ["# 构建系统提示词 - 优化版本\n", "system_prompt = \"\"\"\n", "你是一个智能数据分析助手，必须使用提供的工具来查询数据库。\n", "\n", "## 可用工具\n", "\n", "### sql_query\n", "功能：执行SQL查询语句，获取数据库数据\n", "参数：\n", "- query (字符串): 要执行的SQL查询语句\n", "\n", "## 重要规则\n", "\n", "1. 当用户询问数据相关问题时，你必须使用sql_query工具查询\n", "2. 不要直接回答，必须先调用工具获取数据\n", "3. 工具调用后立即停止生成，等待工具结果\n", "\n", "## 工具调用格式\n", "\n", "当你需要查询数据时，严格按以下格式输出：\n", "\n", "<function_calls>\n", "<invoke name=\"sql_query\">\n", "<parameter name=\"query\">SELECT * FROM users WHERE age > 25</parameter>\n", "</invoke>\n", "</function_calls>\n", "\n", "## 示例对话\n", "\n", "用户：\"查询年龄大于30的用户\"\n", "助手：我将为您查询年龄大于30的用户信息。\n", "\n", "<function_calls>\n", "<invoke name=\"sql_query\">\n", "<parameter name=\"query\">SELECT * FROM users WHERE age > 30</parameter>\n", "</invoke>\n", "</function_calls>\n", "\n", "注意：\n", "- 必须使用工具获取数据，不能凭空回答\n", "- SQL语句要准确，避免语法错误\n", "- 工具调用后不要继续生成其他内容\n", "\"\"\"\n", "\n", "print(\"✅ 优化版系统提示词构建完成\")\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 执行SQL查询: SELECT * FROM users WHERE age > 25\n", "📋 查询结果: [{'id': 1, 'name': '张三', 'age': 28, 'city': '北京'}, {'id': 2, 'name': '李四', 'age': 32, 'city': '上海'}, {'id': 4, 'name': '赵六', 'age': 30, 'city': '深圳'}]\n"]}], "source": ["# 模拟SQL查询工具\n", "def sql_query(query):\n", "    \"\"\"\n", "    模拟执行SQL查询\n", "    实际项目中这里会连接真实数据库\n", "    \"\"\"\n", "    # 模拟数据库响应\n", "    mock_data = {\n", "        \"users\": [\n", "            {\"id\": 1, \"name\": \"张三\", \"age\": 28, \"city\": \"北京\"},\n", "            {\"id\": 2, \"name\": \"李四\", \"age\": 32, \"city\": \"上海\"}, \n", "            {\"id\": 3, \"name\": \"王五\", \"age\": 25, \"city\": \"广州\"},\n", "            {\"id\": 4, \"name\": \"赵六\", \"age\": 30, \"city\": \"深圳\"}\n", "        ]\n", "    }\n", "    \n", "    print(f\"🔍 执行SQL查询: {query}\")\n", "    \n", "    # 简单模拟查询结果\n", "    if \"age > 25\" in query:\n", "        result = [user for user in mock_data[\"users\"] if user[\"age\"] > 25]\n", "    elif \"COUNT\" in query.upper():\n", "        result = [{\"count\": len(mock_data[\"users\"])}]\n", "    else:\n", "        result = mock_data[\"users\"]\n", "    \n", "    return result\n", "\n", "# 测试工具函数\n", "test_result = sql_query(\"SELECT * FROM users WHERE age > 25\")\n", "print(\"📋 查询结果:\", test_result)\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 工具调用解析函数定义完成\n"]}], "source": ["# 工具调用解析函数\n", "def parse_function_calls(response):\n", "    \"\"\"\n", "    解析AI响应中的工具调用\n", "    \"\"\"\n", "    import re\n", "    \n", "    # 查找function_calls标签\n", "    pattern = r'<function_calls>(.*?)</function_calls>'\n", "    matches = re.findall(pattern, response, re.DOTALL)\n", "    \n", "    if not matches:\n", "        return None\n", "        \n", "    # 解析工具调用内容\n", "    call_content = matches[0]\n", "    \n", "    # 提取工具名称\n", "    name_pattern = r'<invoke name=\"([^\"]+)\">'\n", "    name_match = re.search(name_pattern, call_content)\n", "    if not name_match:\n", "        return None\n", "        \n", "    tool_name = name_match.group(1)\n", "    \n", "    # 提取参数\n", "    param_pattern = r'<parameter name=\"([^\"]+)\">([^<]+)</parameter>'\n", "    param_matches = re.findall(param_pattern, call_content)\n", "    \n", "    parameters = {}\n", "    for param_name, param_value in param_matches:\n", "        parameters[param_name] = param_value.strip()\n", "    \n", "    return {\n", "        \"name\": tool_name,\n", "        \"parameters\": parameters\n", "    }\n", "\n", "print(\"✅ 工具调用解析函数定义完成\")\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### 2. 完整的工具使用流程\n", "\n", "现在我们来演示完整的工具使用过程：\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "🚀 开始工具使用演示（优化版）\n", "============================================================\n", "👤 用户提问: 帮我查询一下年龄大于25岁的用户有哪些？\n", "\n", "🤖 AI第一轮响应:\n", "原始响应: 我将为您查询年龄大于25岁的用户信息。\n", "\n", "<function_calls>\n", "<invoke name=\"sql_query\">\n", "<parameter name=\"query\">SELECT * FROM users WHERE age > 25</parameter>\n", "</invoke>\n", "</function_calls>\n", "\n", "🔧 ✅ 检测到工具调用:\n", "   📋 工具名: sql_query\n", "   📝 参数: {'query': 'SELECT * FROM users WHERE age > 25'}\n", "\n", "🔍 执行SQL查询: SELECT * FROM users WHERE age > 25\n", "✅ 工具执行结果: [{'id': 1, 'name': '张三', 'age': 28, 'city': '北京'}, {'id': 2, 'name': '李四', 'age': 32, 'city': '上海'}, {'id': 4, 'name': '赵六', 'age': 30, 'city': '深圳'}]\n", "\n", "🤖 AI最终分析:\n", "根据查询结果，我们可以看到年龄大于25岁的用户共有三位，他们的信息如下：\n", "\n", "1. **张三**\n", "   - 年龄：28岁\n", "   - 城市：北京\n", "\n", "2. **李四**\n", "   - 年龄：32岁\n", "   - 城市：上海\n", "\n", "3. **赵六**\n", "   - 年龄：30岁\n", "   - 城市：深圳\n", "\n", "### 分析：\n", "\n", "- **年龄分布**：这三位用户的年龄分别是28岁、32岁和30岁，均在28岁到32岁之间，显示出一个相对集中的年龄段。\n", "  \n", "- **城市分布**：这三位用户分别来自北京、上海和深圳，都是中国的一线城市。这可能表明年龄大于25岁的用户更倾向于居住在经济发达的大城市。\n", "\n", "- **潜在趋势**：从年龄和城市的分布来看，可能存在这样的趋势：年龄较大的用户更可能在经济发达地区工作和生活。这可能与这些城市提供的就业机会和生活条件有关。\n", "\n", "### 结论：\n", "\n", "年龄大于25岁的用户在样本中显示出集中在一线城市的趋势，且年龄分布较为集中。这可能反映了在这些城市中，较成熟的年龄段的人群比例较高，可能与城市的经济发展水平和生活质量有关。进一步的分析可以结合更多的用户数据来验证这些趋势。\n", "\n", "============================================================\n", "✅ 工具使用演示完成\n", "============================================================\n"]}], "source": ["# 优化版工具使用示例\n", "def run_tool_example():\n", "    \"\"\"\n", "    演示完整的工具使用流程 - 优化版\n", "    \"\"\"\n", "    print(\"=\" * 60)\n", "    print(\"🚀 开始工具使用演示（优化版）\")\n", "    print(\"=\" * 60)\n", "    \n", "    # 用户请求\n", "    user_query = \"帮我查询一下年龄大于25岁的用户有哪些？\"\n", "    print(f\"👤 用户提问: {user_query}\")\n", "    print()\n", "    \n", "    # 第一轮：AI调用工具\n", "    print(\"🤖 AI第一轮响应:\")\n", "    response_1 = get_completion(user_query, system_prompt)\n", "    print(f\"原始响应: {response_1}\")\n", "    print()\n", "    \n", "    # 解析工具调用\n", "    function_call = parse_function_calls(response_1)\n", "    \n", "    if function_call:\n", "        print(\"🔧 ✅ 检测到工具调用:\")\n", "        print(f\"   📋 工具名: {function_call['name']}\")\n", "        print(f\"   📝 参数: {function_call['parameters']}\")\n", "        print()\n", "        \n", "        # 执行工具\n", "        if function_call['name'] == 'sql_query':\n", "            query = function_call['parameters']['query']\n", "            result = sql_query(query)\n", "            print(f\"✅ 工具执行结果: {result}\")\n", "            print()\n", "            \n", "            # 构建包含结果的新提示\n", "            follow_up_prompt = f\"\"\"\n", "之前的查询：{user_query}\n", "\n", "工具调用结果：\n", "查询语句：{query}\n", "查询结果：{result}\n", "\n", "请根据查询结果给出详细的分析和回答。\n", "\"\"\"\n", "            \n", "            # 第二轮：AI分析结果\n", "            print(\"🤖 AI最终分析:\")\n", "            final_response = get_completion(follow_up_prompt, \"\")\n", "            print(final_response)\n", "    else:\n", "        print(\"❌ 未检测到工具调用！\")\n", "        print(\"🔍 这可能是因为：\")\n", "        print(\"   1. 系统提示词不够明确\")\n", "        print(\"   2. AI模型没有按预期格式输出\")\n", "        print(\"   3. 解析函数有问题\")\n", "        print()\n", "        \n", "        # 备用方案：直接使用更强的提示\n", "        print(\"🔄 尝试使用更明确的提示...\")\n", "        stronger_prompt = f\"\"\"\n", "用户问题：{user_query}\n", "\n", "请严格按照以下格式调用工具查询数据库：\n", "\n", "<function_calls>\n", "<invoke name=\"sql_query\">\n", "<parameter name=\"query\">SELECT * FROM users WHERE age > 25</parameter>\n", "</invoke>\n", "</function_calls>\n", "\n", "必须使用这个格式，不要添加其他内容。\n", "\"\"\"\n", "        \n", "        response_2 = get_completion(stronger_prompt, system_prompt)\n", "        print(f\"强化提示响应: {response_2}\")\n", "        \n", "        # 再次尝试解析\n", "        function_call_2 = parse_function_calls(response_2)\n", "        if function_call_2:\n", "            print(\"🎉 第二次尝试成功检测到工具调用！\")\n", "            query = function_call_2['parameters']['query']\n", "            result = sql_query(query)\n", "            print(f\"✅ 工具执行结果: {result}\")\n", "        else:\n", "            print(\"❌ 第二次尝试仍然失败\")\n", "    \n", "    print()\n", "    print(\"=\" * 60)\n", "    print(\"✅ 工具使用演示完成\")\n", "    print(\"=\" * 60)\n", "\n", "# 运行优化版示例\n", "run_tool_example()\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### 3. 关键要点总结\n", "\n", "**系统提示词要点：**\n", "- 📝 清晰描述工具功能和参数\n", "- 🎯 提供具体调用格式示例  \n", "- ⚠️ 说明使用注意事项\n", "\n", "**控制逻辑要点：**\n", "- 🔍 解析AI的工具调用请求\n", "- ⚡ 执行对应的工具函数\n", "- 🔄 返回结果给AI继续对话\n", "\n", "**最佳实践：**\n", "- 使用标准 `<function_calls>` 格式\n", "- 添加清晰的错误处理\n", "- 提供完整的使用文档\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### 3. 工具使用的关键要点\n", "\n", "**系统提示词设计：**\n", "- 📝 清晰描述每个工具的功能和参数\n", "- 🎯 给出具体的调用格式示例\n", "- ⚠️ 说明注意事项和限制\n", "\n", "**控制逻辑实现：**\n", "- 🔍 准确解析AI的工具调用请求\n", "- ⚡ 快速执行对应的工具函数  \n", "- 🔄 将结果返回给AI继续对话\n", "\n", "**最佳实践：**\n", "- 使用标准的 `<function_calls>` 格式\n", "- 工具函数要有清晰的错误处理\n", "- 系统提示词要包含完整的使用说明\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 4}
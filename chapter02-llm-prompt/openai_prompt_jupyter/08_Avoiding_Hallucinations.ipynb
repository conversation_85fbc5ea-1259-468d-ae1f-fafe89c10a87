{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 第八章：避免幻觉\n", "\n", "## 设置\n", "\n", "运行以下设置单元格来加载您的API密钥并建立`get_completion`辅助函数。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Requirement already satisfied: openai==1.61.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (1.61.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.10.0)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (2.11.5)\n", "Requirement already satisfied: sniffio in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.67.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.14.0)\n", "Requirement already satisfied: idna>=2.8 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from anyio<5,>=3.5.0->openai==1.61.0) (3.10)\n", "Requirement already satisfied: certifi in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (2025.4.26)\n", "Requirement already satisfied: httpcore==1.* in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai==1.61.0) (0.16.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.4.1)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n", "Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Requirement already satisfied: openai==1.61.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (1.61.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.10.0)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (2.11.5)\n", "Requirement already satisfied: sniffio in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.67.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.14.0)\n", "Requirement already satisfied: idna>=2.8 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from anyio<5,>=3.5.0->openai==1.61.0) (3.10)\n", "Requirement already satisfied: certifi in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (2025.4.26)\n", "Requirement already satisfied: httpcore==1.* in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai==1.61.0) (0.16.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.4.1)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n", "✅ OpenAI环境设置完成!\n", "🔧 OpenAI API 配置信息:\n", "  📡 配置来源: 环境变量 + 自定义API地址: https://vip.apiyi.com/v1\n", "  🤖 模型: gpt-4o\n", "  🌐 API地址: https://vip.apiyi.com/v1\n", "  🔑 API密钥: sk-R2utG...B944\n", "\n", "✅ 使用统一配置管理成功！\n"]}], "source": ["# 安装OpenAI库\n", "%pip install openai==1.61.0\n", "\n", "# 🔧 OpenAI环境自动配置\n", "# 此设置会自动从环境变量或IPython存储中加载配置\n", "\n", "# 安装OpenAI库\n", "%pip install openai==1.61.0\n", "\n", "# 导入Python内置的正则表达式库\n", "import re\n", "\n", "# 🚀 使用统一配置管理系统\n", "from config import setup_notebook_environment, print_config_info\n", "\n", "# 自动设置OpenAI客户端和get_completion函数\n", "# 优先级：环境变量 > IPython存储 > 默认值\n", "try:\n", "    client, get_completion = setup_notebook_environment()\n", "    print(\"✅ 使用统一配置管理成功！\")\n", "except Exception as e:\n", "    print(f\"❌ 统一配置失败，回退到传统方式: {e}\")\n", "    \n", "    # 回退到传统的配置方式\n", "    import openai\n", "    \n", "    # 从IPython存储中检索API_KEY和MODEL_NAME变量\n", "    %store -r API_KEY\n", "    %store -r MODEL_NAME\n", "\n", "    # 如果没有设置MODEL_NAME，使用默认值\n", "    try:\n", "        MODEL_NAME\n", "    except NameError:\n", "        MODEL_NAME = \"gpt-4o\"  # 默认使用gpt-4o模型\n", "\n", "    # 创建OpenAI客户端\n", "    client = openai.OpenAI(api_key=API_KEY)\n", "\n", "    def get_completion(prompt: str, system_prompt=\"\"):\n", "        \"\"\"\n", "        获取GPT的完成响应\n", "        \n", "        参数:\n", "            prompt (str): 用户提示\n", "            system_prompt (str): 系统提示（可选）\n", "        \n", "        返回:\n", "            str: GPT的响应文本\n", "        \"\"\"\n", "        # 构建消息列表\n", "        messages = []\n", "        \n", "        # 如果有系统提示，添加系统消息\n", "        if system_prompt:\n", "            messages.append({\"role\": \"system\", \"content\": system_prompt})\n", "        \n", "        # 添加用户消息\n", "        messages.append({\"role\": \"user\", \"content\": prompt})\n", "        \n", "        # 调用OpenAI API\n", "        response = client.chat.completions.create(\n", "            model=MODEL_NAME,              # 模型名称 (gpt-4o 或 deepseek-r1)\n", "            messages=messages,             # 消息列表\n", "            max_completion_tokens=2000,    # 最大token数\n", "            temperature=0.0               # 温度参数，0表示更确定性\n", "        )\n", "        return response.choices[0].message.content\n", "    \n", "    print(\"⚠️  使用传统配置方式，建议配置环境变量以获得更好体验\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "## 课程 \n", "\n", "### 什么是LLM幻觉？\n", "\n", "**LLM幻觉**（AI Hallucination）是指大语言模型生成看似合理但实际上不准确、虚假或无法验证的信息的现象。这是当前AI技术面临的一个重要挑战。\n", "\n", "### 幻觉产生的原因\n", "\n", "1. **训练数据限制**：模型基于有限的训练数据进行推理\n", "2. **概率性生成**：模型基于概率分布生成文本，可能产生不存在的\"合理\"组合\n", "3. **知识边界模糊**：模型难以准确识别自己的知识边界\n", "4. **上下文理解偏差**：对复杂问题的理解可能存在偏差\n", "\n", "### 避免幻觉的核心策略\n", "\n", "**好消息是：有多种技术可以显著减少幻觉现象**。本节将重点介绍以下策略：\n", "\n", "1. **🛡️ 承认无知策略**：给模型选择说\"不知道\"的机会\n", "2. **🔍 证据导向策略**：要求模型先寻找证据再回答\n", "3. **📝 分步推理策略**：引导模型逐步分析问题\n", "4. **✅ 可信度评估**：要求模型评估自己答案的可信度\n", "\n", "### 为什么避免幻觉很重要？\n", "\n", "- **增强用户信任**：准确的信息建立用户对AI系统的信任\n", "- **降低误导风险**：避免传播错误信息\n", "- **提高实用性**：确保AI助手在实际应用中的可靠性\n", "- **符合伦理要求**：负责任的AI开发实践\n", "\n", "下面我们通过具体示例来学习这些技术的实际应用。"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 示例1：承认无知策略 🛡️\n", "\n", "**核心思想**：明确告诉模型，如果不确定答案，可以诚实地说\"不知道\"，而不是编造信息。\n", "\n", "### 问题场景\n", "假设我们询问一个可能不存在或者模型不确定的信息。让我们比较两种提示方式的效果。\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["❌ 容易产生幻觉的提示:\n", "\n", "请告诉我关于 林黛玉倒把垂杨柳的故事。\n", "\n", "==================================================\n", "模型回答:\n", "林黛玉倒把垂杨柳的故事出自中国古典小说《红楼梦》，是其中一个非常著名的情节。林黛玉是《红楼梦》中的主要人物之一，她性格敏感、多愁善感，常常以诗词表达自己的情感。\n", "\n", "这个故事发生在大观园的一次诗社活动中。贾宝玉、林黛玉、薛宝钗等人在园中聚会，大家决定以“柳”为题作诗。林黛玉在作诗时，写下了“留得残荷听雨声”这样的句子，表现出她对生命无常的感慨和对自然景物的细腻观察。\n", "\n", "在这次活动中，林黛玉的诗被认为是“倒把垂杨柳”，意思是她的诗意境深远，超越了常规的思维方式，给人一种耳目一新的感觉。这个故事不仅展示了林黛玉的才华，也反映了她内心的孤独和对生活的独特理解。\n", "\n", "林黛玉倒把垂杨柳的故事体现了《红楼梦》中人物之间的互动和情感交流，同时也展示了作者曹雪芹对诗词艺术的深刻理解和运用。\n"]}], "source": ["# 📌 错误示例：没有给模型\"不知道\"的选项\n", "bad_prompt = \"\"\"\n", "请告诉我关于 林黛玉倒把垂杨柳的故事。\n", "\"\"\"\n", "\n", "print(\"❌ 容易产生幻觉的提示:\")\n", "print(bad_prompt)\n", "print(\"=\" * 50)\n", "\n", "response_bad = get_completion(bad_prompt)\n", "print(\"模型回答:\")\n", "print(response_bad)\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 避免幻觉的提示:\n", "\n", "请告诉我关于 林黛玉倒把垂杨柳的故事梗概和故事详情。\n", "\n", "重要提示：\n", "- 如果你对这个故事不确定或没有可靠信息，请直接说\"我不知道\"或\"我没有这个产品的确切信息\"\n", "- 不要编造或故事梗概和故事详情\n", "- 只提供你确信准确的信息\n", "\n", "==================================================\n", "模型回答:\n", "我不知道。\n"]}], "source": ["# ✅ 正确示例：明确给出\"不知道\"的选项\n", "good_prompt = \"\"\"\n", "请告诉我关于 林黛玉倒把垂杨柳的故事梗概和故事详情。\n", "\n", "重要提示：\n", "- 如果你对这个故事不确定或没有可靠信息，请直接说\"我不知道\"或\"我没有这个产品的确切信息\"\n", "- 不要编造或故事梗概和故事详情\n", "- 只提供你确信准确的信息\n", "\"\"\"\n", "\n", "print(\"✅ 避免幻觉的提示:\")\n", "print(good_prompt)\n", "print(\"=\" * 50)\n", "\n", "response_good = get_completion(good_prompt)\n", "print(\"模型回答:\")\n", "print(response_good)\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### 🎯 示例1总结\n", "\n", "**对比分析**：\n", "- **第一种提示**：容易让模型感觉\"必须\"给出答案，可能导致编造信息\n", "- **第二种提示**：明确告知模型可以承认不知道，减少幻觉风险\n", "\n", "**关键技巧**：\n", "1. 使用\"如果你不确定，请说不知道\"这样的明确指示\n", "2. 强调\"不要编造或猜测\"\n", "3. 要求\"只提供确信准确的信息\"\n", "\n", "---\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 示例2：证据导向策略 🔍\n", "\n", "**核心思想**：要求模型在回答之前先分析现有信息，基于证据进行推理，而不是直接给出结论。\n", "\n", "### 问题场景\n", "让我们以一个历史事件查询为例，看看如何通过要求模型先寻找证据来提高答案的准确性。\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["❌ 直接询问的提示:\n", "\n", "中国明朝时期是否有过与美洲的贸易往来？请给出详细说明。\n", "\n", "==================================================\n", "模型回答:\n", "关于中国明朝时期与美洲的贸易往来，历史记录中并没有明确的证据表明存在直接的贸易联系。明朝（1368-1644年）是中国历史上一个重要的朝代，以其强大的海上力量和郑和下西洋等著名航海活动而闻名。然而，这些航海活动主要集中在东南亚、南亚和东非地区，并没有明确记录显示明朝与美洲有直接的贸易往来。\n", "\n", "以下是一些相关背景信息：\n", "\n", "1. **郑和下西洋**：郑和是明朝著名的航海家，他在1405年至1433年间进行了七次大规模的航海活动。这些航海活动主要是为了宣扬明朝的国威和进行外交交流，航线主要覆盖东南亚、南亚、中东和东非地区，并没有到达美洲。\n", "\n", "2. **地理知识的限制**：在明朝时期，中国对美洲的地理知识非常有限。虽然有一些关于遥远土地的传说，但没有具体的航海技术或地理知识支持与美洲的直接联系。\n", "\n", "3. **欧洲人的到来**：美洲与亚洲的直接联系主要是在欧洲人到达美洲之后才逐渐建立起来。哥伦布在1492年到达美洲后，欧洲国家开始在美洲进行殖民活动，并逐渐建立了与亚洲的贸易联系。\n", "\n", "4. **间接影响**：虽然没有直接的贸易往来，但通过欧洲的中介，明朝时期的中国商品可能间接地进入了美洲市场。例如，中国的瓷器、丝绸等商品通过欧洲的贸易网络可能被带到美洲。\n", "\n", "综上所述，明朝时期中国与美洲没有直接的贸易往来，任何可能的联系都是通过欧洲的中介实现的。\n"]}], "source": ["# 📌 错误示例：直接要求答案，没有要求证据分析\n", "direct_prompt = \"\"\"\n", "中国明朝时期是否有过与美洲的贸易往来？请给出详细说明。\n", "\"\"\"\n", "\n", "print(\"❌ 直接询问的提示:\")\n", "print(direct_prompt)\n", "print(\"=\" * 50)\n", "\n", "response_direct = get_completion(direct_prompt)\n", "print(\"模型回答:\")\n", "print(response_direct)\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 基于证据的分步提示:\n", "\n", "关于中国明朝时期是否有过与美洲的贸易往来这个问题，请按以下步骤分析：\n", "\n", "第一步：时间线分析\n", "- 明朝的时间范围（哪一年到哪一年）\n", "- 欧洲人发现美洲的时间\n", "- 这两个时间段是否有重叠\n", "\n", "第二步：证据收集\n", "- 列出你知道的相关历史事实和证据\n", "- 明确指出哪些信息你确定，哪些不确定\n", "\n", "第三步：逻辑推理\n", "- 基于上述证据进行分析\n", "- 考虑当时的技术条件、地理因素、政治环境\n", "\n", "第四步：得出结论\n", "- 给出你的答案，并说明可信度\n", "- 如果证据不足，请明确说明\n", "\n", "请按照这个结构来回答，不要跳过任何步骤。\n", "\n", "==================================================\n", "模型回答:\n", "第一步：时间线分析\n", "- 明朝的时间范围是1368年到1644年。\n", "- 欧洲人发现美洲的时间是1492年，哥伦布首次到达美洲。\n", "- 这两个时间段有重叠，明朝的后期与美洲被发现的时间重合。\n", "\n", "第二步：证据收集\n", "- 确定的信息：\n", "  1. 明朝时期，中国的海上贸易相对活跃，郑和下西洋是一个著名的例子，但他的航行主要集中在印度洋和东非一带。\n", "  2. 欧洲人在15世纪末开始探索和殖民美洲，西班牙和葡萄牙是最早的殖民者。\n", "  3. 明朝实行海禁政策，限制对外贸易，尤其是在16世纪中期以后。\n", "- 不确定的信息：\n", "  1. 是否有直接证据表明明朝与美洲有直接贸易往来。\n", "  2. 是否有考古或文献证据支持明朝与美洲之间的联系。\n", "\n", "第三步：逻辑推理\n", "- 技术条件：明朝的航海技术在当时是先进的，但主要集中在亚洲和非洲的航线。跨太平洋航行的技术和动力可能不足。\n", "- 地理因素：太平洋的广阔和未知可能对明朝的航海活动构成障碍。\n", "- 政治环境：明朝的海禁政策限制了对外贸易和航海活动，减少了与远距离地区的直接接触可能性。\n", "\n", "第四步：得出结论\n", "- 结论：基于现有的证据和逻辑推理，明朝与美洲之间没有直接的贸易往来。虽然时间上有重叠，但技术、地理和政治因素使得这种联系不太可能。\n", "- 可信度：中等。虽然没有直接证据支持明朝与美洲的贸易，但也没有明确的证据完全排除这种可能性。由于证据不足，无法得出绝对结论。\n"]}], "source": ["# ✅ 正确示例：要求基于证据的分步分析\n", "evidence_based_prompt = \"\"\"\n", "关于中国明朝时期是否有过与美洲的贸易往来这个问题，请按以下步骤分析：\n", "\n", "第一步：时间线分析\n", "- 明朝的时间范围（哪一年到哪一年）\n", "- 欧洲人发现美洲的时间\n", "- 这两个时间段是否有重叠\n", "\n", "第二步：证据收集\n", "- 列出你知道的相关历史事实和证据\n", "- 明确指出哪些信息你确定，哪些不确定\n", "\n", "第三步：逻辑推理\n", "- 基于上述证据进行分析\n", "- 考虑当时的技术条件、地理因素、政治环境\n", "\n", "第四步：得出结论\n", "- 给出你的答案，并说明可信度\n", "- 如果证据不足，请明确说明\n", "\n", "请按照这个结构来回答，不要跳过任何步骤。\n", "\"\"\"\n", "\n", "print(\"✅ 基于证据的分步提示:\")\n", "print(evidence_based_prompt)\n", "print(\"=\" * 50)\n", "\n", "response_evidence = get_completion(evidence_based_prompt)\n", "print(\"模型回答:\")\n", "print(response_evidence)\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### 🎯 示例2总结\n", "\n", "**对比分析**：\n", "- **直接询问**：模型可能直接给出结论，缺乏推理过程的透明度\n", "- **证据导向**：通过分步骤要求，让模型展示推理过程，更容易发现潜在错误\n", "\n", "**证据导向策略的关键要素**：\n", "1. **时间线分析**：建立时间框架，避免时代错乱\n", "2. **证据收集**：要求模型明确列出已知事实\n", "3. **不确定性标注**：让模型区分确定和不确定的信息\n", "4. **逻辑推理**：基于证据进行分析，而非猜测\n", "5. **可信度评估**：要求模型评估自己答案的可靠性\n", "\n", "**适用场景**：\n", "- 历史事件查询\n", "- 科学问题分析\n", "- 复杂决策支持\n", "- 事实核查任务\n", "\n", "---\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 🚀 综合最佳实践\n", "\n", "### 避免幻觉的提示工程模板\n", "\n", "以下是一个可复用的提示模板，结合了多种避免幻觉的技术：\n", "\n", "```\n", "任务：[描述你的具体任务]\n", "\n", "分析要求：\n", "1. 首先评估你对这个问题的知识确定性\n", "2. 如果不确定或缺乏可靠信息，请明确说明\n", "3. 列出你将基于的事实和证据\n", "4. 进行逐步推理分析\n", "5. 给出结论并评估可信度（1-10分）\n", "\n", "重要提示：\n", "- 诚实承认知识边界，不要编造信息\n", "- 区分事实、推测和个人观点\n", "- 如果需要最新信息，请说明你的知识截止时间\n", "```\n", "\n", "### 🛠️ 实用技巧总结\n", "\n", "| 策略 | 关键技术 | 适用场景 |\n", "|------|----------|----------|\n", "| **承认无知** | \"如果不确定请说不知道\" | 事实查询、技术规格 |\n", "| **证据导向** | 分步推理、证据列举 | 复杂分析、历史研究 |\n", "| **可信度评估** | 要求1-10分评分 | 决策支持、风险评估 |\n", "| **知识边界** | 明确知识截止时间 | 时效性信息查询 |\n", "| **多角度验证** | 要求从不同角度分析 | 争议性话题、评价 |\n", "\n", "### ⚠️ 常见陷阱\n", "\n", "1. **过度自信表述**：避免让模型使用过于绝对的语言\n", "2. **忽略时效性**：某些信息可能已过时\n", "3. **文化偏见**：注意不同文化背景下的理解差异\n", "4. **技术局限**：模型可能在某些专业领域知识有限\n", "\n", "### 📊 检验方法\n", "\n", "**如何判断回答质量**：\n", "- ✅ 模型是否承认了不确定性？\n", "- ✅ 是否提供了推理过程？\n", "- ✅ 事实陈述是否可验证？\n", "- ✅ 结论是否合理谨慎？\n", "\n", "记住：**一个承认局限性的准确答案，远比一个看似完美的错误答案更有价值！**\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 4}
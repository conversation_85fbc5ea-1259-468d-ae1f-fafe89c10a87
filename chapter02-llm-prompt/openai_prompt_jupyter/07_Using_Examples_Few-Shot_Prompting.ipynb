{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 第七章：使用示例（少样本提示）\n", "\n", "本章将学习如何通过提供示例来改善GPT的响应质量和格式。这种技术被称为\"**少样本提示**\"（Few-Shot Prompting），是提示工程中最有效的技术之一。\n", "\n", "## 🎯 学习目标\n", "\n", "通过本章的学习，您将掌握：\n", "- 理解少样本提示的核心原理和应用场景\n", "- 学会设计有效的示例来引导模型行为\n", "- 掌握不同类型示例的使用技巧\n", "- 能够针对特定任务构建高质量的少样本提示\n", "\n", "## 💡 核心概念\n", "\n", "**少样本提示**是一种通过提供少量示例来教导模型如何执行特定任务的技术。相比于详细的文字描述，示例往往能更直观、更准确地传达我们的期望。\n", "\n", "### 为什么少样本提示如此有效？\n", "\n", "1. **直观性**：示例比抽象描述更容易理解\n", "2. **格式控制**：通过示例可以精确控制输出格式\n", "3. **一致性**：模型倾向于模仿示例的风格和结构\n", "4. **减少歧义**：示例消除了指令中的模糊性\n", "\n", "## 设置\n", "\n", "运行以下设置单元格来加载您的API密钥并建立`get_completion`辅助函数。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Requirement already satisfied: openai==1.61.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (1.61.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.10.0)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (2.11.5)\n", "Requirement already satisfied: sniffio in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.67.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.14.0)\n", "Requirement already satisfied: idna>=2.8 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from anyio<5,>=3.5.0->openai==1.61.0) (3.10)\n", "Requirement already satisfied: certifi in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (2025.4.26)\n", "Requirement already satisfied: httpcore==1.* in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai==1.61.0) (0.16.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.4.1)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n", "Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Requirement already satisfied: openai==1.61.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (1.61.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.10.0)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (2.11.5)\n", "Requirement already satisfied: sniffio in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.67.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.14.0)\n", "Requirement already satisfied: idna>=2.8 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from anyio<5,>=3.5.0->openai==1.61.0) (3.10)\n", "Requirement already satisfied: certifi in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (2025.4.26)\n", "Requirement already satisfied: httpcore==1.* in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai==1.61.0) (0.16.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.4.1)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n", "✅ OpenAI环境设置完成!\n", "🔧 OpenAI API 配置信息:\n", "  📡 配置来源: 环境变量 + 自定义API地址: https://vip.apiyi.com/v1\n", "  🤖 模型: gpt-4o\n", "  🌐 API地址: https://vip.apiyi.com/v1\n", "  🔑 API密钥: sk-R2utG...B944\n", "\n", "✅ 使用统一配置管理成功！\n"]}], "source": ["# 安装OpenAI库\n", "%pip install openai==1.61.0\n", "\n", "# 🔧 OpenAI环境自动配置\n", "# 此设置会自动从环境变量或IPython存储中加载配置\n", "\n", "# 安装OpenAI库\n", "%pip install openai==1.61.0\n", "\n", "# 导入Python内置的正则表达式库\n", "import re\n", "\n", "# 🚀 使用统一配置管理系统\n", "from config import setup_notebook_environment, print_config_info\n", "\n", "# 自动设置OpenAI客户端和get_completion函数\n", "# 优先级：环境变量 > IPython存储 > 默认值\n", "try:\n", "    client, get_completion = setup_notebook_environment()\n", "    print(\"✅ 使用统一配置管理成功！\")\n", "except Exception as e:\n", "    print(f\"❌ 统一配置失败，回退到传统方式: {e}\")\n", "    \n", "    # 回退到传统的配置方式\n", "    import openai\n", "    \n", "    # 从IPython存储中检索API_KEY和MODEL_NAME变量\n", "    %store -r API_KEY\n", "    %store -r MODEL_NAME\n", "\n", "    # 如果没有设置MODEL_NAME，使用默认值\n", "    try:\n", "        MODEL_NAME\n", "    except NameError:\n", "        MODEL_NAME = \"gpt-4o\"  # 默认使用gpt-4o模型\n", "\n", "    # 创建OpenAI客户端\n", "    client = openai.OpenAI(api_key=API_KEY)\n", "\n", "    def get_completion(prompt: str, system_prompt=\"\"):\n", "        \"\"\"\n", "        获取GPT的完成响应\n", "        \n", "        参数:\n", "            prompt (str): 用户提示\n", "            system_prompt (str): 系统提示（可选）\n", "        \n", "        返回:\n", "            str: GPT的响应文本\n", "        \"\"\"\n", "        # 构建消息列表\n", "        messages = []\n", "        \n", "        # 如果有系统提示，添加系统消息\n", "        if system_prompt:\n", "            messages.append({\"role\": \"system\", \"content\": system_prompt})\n", "        \n", "        # 添加用户消息\n", "        messages.append({\"role\": \"user\", \"content\": prompt})\n", "        \n", "        # 调用OpenAI API\n", "        response = client.chat.completions.create(\n", "            model=MODEL_NAME,              # 模型名称 (gpt-4o 或 deepseek-r1)\n", "            messages=messages,             # 消息列表\n", "            max_completion_tokens=2000,    # 最大token数\n", "            temperature=0.0               # 温度参数，0表示更确定性\n", "        )\n", "        return response.choices[0].message.content\n", "    \n", "    print(\"⚠️  使用传统配置方式，建议配置环境变量以获得更好体验\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "## 📖 理论基础\n", "\n", "**给GPT提供您希望它如何表现（或不希望它如何表现）的示例是极其有效的**，这种方法适用于：\n", "- 获得正确的答案\n", "- 以正确的格式获得答案\n", "- 控制输出的语调和风格\n", "- 规范复杂任务的执行流程\n", "\n", "### 🔢 样本数量分类\n", "\n", "您可能会遇到以下术语，\"样本\"的数量指的是提示中使用了多少个示例：\n", "\n", "| 术语 | 示例数量 | 适用场景 | 优缺点 |\n", "|------|----------|----------|--------|\n", "| **零样本提示** | 0个 | 简单任务、模型已知任务 | ✅ 简洁 ❌ 控制力弱 |\n", "| **单样本提示** | 1个 | 格式示范、简单模式 | ✅ 清晰 ❌ 可能过拟合 |\n", "| **少样本提示** | 2-5个 | 复杂任务、多样化需求 | ✅ 平衡效果 ❌ Token消耗 |\n", "| **多样本提示** | 5+个 | 复杂模式识别 | ✅ 强控制 ❌ 上下文限制 |\n", "\n", "### 🎨 示例设计原则\n", "\n", "1. **代表性**：示例应该覆盖目标任务的典型情况\n", "2. **多样性**：避免单一模式，展示不同变化\n", "3. **质量**：确保示例本身的质量和准确性\n", "4. **简洁性**：示例要简洁明了，突出关键点"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "\n", "## 🚀 实战示例一：客户评价情感分析\n", "\n", "让我们通过一个具体例子来理解少样本提示的威力。我们要构建一个客户评价情感分析系统。\n", "\n", "### 场景描述\n", "假设我们是一家电商平台，需要自动分析客户评价的情感倾向，并提取关键信息。\n", "\n", "### 零样本 vs 少样本对比\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 零样本提示 ===\n", "\n", "请分析以下客户评价的情感倾向，并提取关键信息：\n", "\n", "客户评价：物流很快，包装也很好，就是产品质量一般，价格偏贵，不太推荐购买。\n", "\n", "请提供情感分析结果。\n", "\n", "\n", "==================================================\n", "\n", "零样本结果：\n", "情感分析结果：\n", "\n", "1. 积极方面：\n", "   - 物流速度快\n", "   - 包装良好\n", "\n", "2. 消极方面：\n", "   - 产品质量一般\n", "   - 价格偏贵\n", "\n", "总体情感倾向：负面\n", "\n", "关键信息提取：\n", "- 客户对物流速度和包装表示满意。\n", "- 客户对产品质量和价格不满意。\n", "- 客户不太推荐购买该产品。\n"]}], "source": ["# 示例1：零样本提示（效果较差）\n", "zero_shot_prompt = \"\"\"\n", "请分析以下客户评价的情感倾向，并提取关键信息：\n", "\n", "客户评价：物流很快，包装也很好，就是产品质量一般，价格偏贵，不太推荐购买。\n", "\n", "请提供情感分析结果。\n", "\"\"\"\n", "\n", "print(\"=== 零样本提示 ===\")\n", "print(zero_shot_prompt)\n", "print(\"\\n\" + \"=\"*50 + \"\\n\")\n", "\n", "# 获取零样本结果\n", "zero_shot_result = get_completion(zero_shot_prompt)\n", "print(\"零样本结果：\")\n", "print(zero_shot_result)\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 少样本提示 ===\n", "\n", "你是一个专业的客户评价分析师。请按照以下格式分析客户评价：\n", "\n", "示例1：\n", "客户评价：这个产品太棒了！质量超出预期，客服态度也很好，五星推荐！\n", "分析结果：\n", "- 整体情感：积极 (9/10)\n", "- 产品质量：非常满意\n", "- 服务体验：非常满意  \n", "- 推荐度：强烈推荐\n", "- 关键词：质量超出预期、客服态度好、五星推荐\n", "\n", "示例2：\n", "客户评价：东西收到了，包装还行，但是质量真的一般，感觉不值这个价钱。\n", "分析结果：\n", "- 整体情感：消极 (3/10)\n", "- 产品质量：不满意\n", "- 服务体验：一般\n", "- 推荐度：不推荐\n", "- 关键词：质量一般、不值这个价、包装还行\n", "\n", "示例3：\n", "客户评价：物流速度可以，产品功能基本满足需求，但说明书不够详细。\n", "分析结果：\n", "- 整体情感：中性 (6/10)\n", "- 产品质量：基本满意\n", "- 服务体验：满意\n", "- 推荐度：一般\n", "- 关键词：物流速度可以、功能基本满足、说明书不详细\n", "\n", "现在请分析：\n", "客户评价：物流很快，包装也很好，就是产品质量一般，价格偏贵，不太推荐购买。\n", "分析结果：\n", "\n", "\n", "==================================================\n", "\n", "少样本结果：\n", "- 整体情感：消极 (4/10)\n", "- 产品质量：不满意\n", "- 服务体验：满意\n", "- 推荐度：不推荐\n", "- 关键词：物流很快、包装很好、质量一般、价格偏贵、不推荐购买\n"]}], "source": ["# 示例2：少样本提示（效果显著提升）\n", "few_shot_prompt = \"\"\"\n", "你是一个专业的客户评价分析师。请按照以下格式分析客户评价：\n", "\n", "示例1：\n", "客户评价：这个产品太棒了！质量超出预期，客服态度也很好，五星推荐！\n", "分析结果：\n", "- 整体情感：积极 (9/10)\n", "- 产品质量：非常满意\n", "- 服务体验：非常满意  \n", "- 推荐度：强烈推荐\n", "- 关键词：质量超出预期、客服态度好、五星推荐\n", "\n", "示例2：\n", "客户评价：东西收到了，包装还行，但是质量真的一般，感觉不值这个价钱。\n", "分析结果：\n", "- 整体情感：消极 (3/10)\n", "- 产品质量：不满意\n", "- 服务体验：一般\n", "- 推荐度：不推荐\n", "- 关键词：质量一般、不值这个价、包装还行\n", "\n", "示例3：\n", "客户评价：物流速度可以，产品功能基本满足需求，但说明书不够详细。\n", "分析结果：\n", "- 整体情感：中性 (6/10)\n", "- 产品质量：基本满意\n", "- 服务体验：满意\n", "- 推荐度：一般\n", "- 关键词：物流速度可以、功能基本满足、说明书不详细\n", "\n", "现在请分析：\n", "客户评价：物流很快，包装也很好，就是产品质量一般，价格偏贵，不太推荐购买。\n", "分析结果：\n", "\"\"\"\n", "\n", "print(\"=== 少样本提示 ===\")\n", "print(few_shot_prompt)\n", "print(\"\\n\" + \"=\"*50 + \"\\n\")\n", "\n", "# 获取少样本结果\n", "few_shot_result = get_completion(few_shot_prompt)\n", "print(\"少样本结果：\")\n", "print(few_shot_result)\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### 📊 效果对比分析\n", "\n", "通过上面的对比，我们可以清楚地看到少样本提示的优势：\n", "\n", "| 对比维度 | 零样本提示 | 少样本提示 |\n", "|----------|------------|------------|\n", "| **格式一致性** | ❌ 输出格式不固定 | ✅ 严格按照示例格式 |\n", "| **信息完整性** | ❌ 可能遗漏关键信息 | ✅ 全面覆盖各个维度 |\n", "| **分析深度** | ❌ 分析较为浅显 | ✅ 结构化深度分析 |\n", "| **可用性** | ❌ 需要后处理 | ✅ 直接可用 |\n", "\n", "### 💡 示例设计技巧\n", "\n", "1. **覆盖不同情况**：积极、消极、中性评价\n", "2. **展示期望格式**：结构化的输出格式\n", "3. **提供评分标准**：具体的量化指标\n", "4. **包含边界情况**：复杂的混合情感\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "\n", "## 🎭 实战示例二：智能代码注释生成\n", "\n", "第二个示例展示如何使用少样本提示来生成高质量的代码注释。这对于代码维护和团队协作非常重要。\n", "\n", "### 场景描述\n", "我们需要为Python函数自动生成专业的文档字符串（docstring），包括函数描述、参数说明、返回值和使用示例。\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 代码文档生成提示 ===\n", "输入函数：\n", "def merge_sorted_lists(list1, list2):\n", "    result = []\n", "    i, j = 0, 0\n", "    while i < len(list1) and j < len(list2):\n", "        if list1[i] <= list2[j]:\n", "            result.append(list1[i])\n", "            i += 1\n", "        else:\n", "            result.append(list2[j])\n", "            j += 1\n", "    result.extend(list1[i:])\n", "    result.extend(list2[j:])\n", "    return result\n", "\n", "==================================================\n", "\n", "生成的文档：\n", "```python\n", "def merge_sorted_lists(list1, list2):\n", "    \"\"\"合并两个已排序的列表。\n", "\n", "    将两个已排序的列表合并为一个新的排序列表。该函数假设输入的两个列表均已按升序排列。\n", "    使用双指针法进行合并，时间复杂度为O(n + m)，其中n和m分别为两个列表的长度。\n", "\n", "    Args:\n", "        list1 (list): 第一个已排序的列表，元素类型为int或float。\n", "        list2 (list): 第二个已排序的列表，元素类型为int或float。\n", "\n", "    Returns:\n", "        list: 合并后的排序列表，包含来自两个输入列表的所有元素。\n", "\n", "    Raises:\n", "        TypeError: 当列表元素不是数字类型时抛出异常。\n", "\n", "    Example:\n", "        >>> merge_sorted_lists([1, 3, 5], [2, 4, 6])\n", "        [1, 2, 3, 4, 5, 6]\n", "        >>> merge_sorted_lists([1, 2, 3], [])\n", "        [1, 2, 3]\n", "        >>> merge_sorted_lists([], [4, 5, 6])\n", "        [4, 5, 6]\n", "    \"\"\"\n", "    result = []\n", "    i, j = 0, 0\n", "    while i < len(list1) and j < len(list2):\n", "        if list1[i] <= list2[j]:\n", "            result.append(list1[i])\n", "            i += 1\n", "        else:\n", "            result.append(list2[j])\n", "            j += 1\n", "    result.extend(list1[i:])\n", "    result.extend(list2[j:])\n", "    return result\n", "```\n"]}], "source": ["# 少样本提示：代码注释生成\n", "code_documentation_prompt = \"\"\"\n", "你是一个专业的Python代码文档生成专家。请为给定的函数生成高质量的Google风格docstring。\n", "\n", "示例1：\n", "```python\n", "def calculate_bmi(weight, height):\n", "    return weight / (height ** 2)\n", "```\n", "\n", "生成文档：\n", "```python\n", "def calculate_bmi(weight, height):\n", "    \\\"\\\"\\\"计算身体质量指数(BMI)。\n", "    \n", "    根据体重和身高计算BMI值，用于评估身体健康状况。\n", "    BMI = 体重(kg) / 身高(m)²\n", "    \n", "    Args:\n", "        weight (float): 体重，单位为千克(kg)，必须为正数\n", "        height (float): 身高，单位为米(m)，必须为正数\n", "        \n", "    Returns:\n", "        float: BMI值，保留小数点后两位\n", "        \n", "    Raises:\n", "        ValueError: 当体重或身高为负数或零时抛出异常\n", "        \n", "    Example:\n", "        >>> calculate_bmi(70.0, 1.75)\n", "        22.86\n", "        >>> calculate_bmi(65, 1.60)\n", "        25.39\n", "    \\\"\\\"\\\"\n", "    return weight / (height ** 2)\n", "```\n", "\n", "示例2：\n", "```python\n", "def find_max_in_list(numbers):\n", "    max_num = numbers[0]\n", "    for num in numbers:\n", "        if num > max_num:\n", "            max_num = num\n", "    return max_num\n", "```\n", "\n", "生成文档：\n", "```python\n", "def find_max_in_list(numbers):\n", "    \\\"\\\"\\\"在数字列表中查找最大值。\n", "    \n", "    遍历整个列表，找到并返回其中的最大数值。\n", "    该函数使用线性搜索算法，时间复杂度为O(n)。\n", "    \n", "    Args:\n", "        numbers (list): 包含数字的列表，不能为空列表\n", "        \n", "    Returns:\n", "        int/float: 列表中的最大数值，类型与输入元素类型一致\n", "        \n", "    Raises:\n", "        IndexError: 当输入列表为空时抛出异常\n", "        TypeError: 当列表元素不是数字类型时抛出异常\n", "        \n", "    Example:\n", "        >>> find_max_in_list([1, 5, 3, 9, 2])\n", "        9\n", "        >>> find_max_in_list([-1, -5, -2])\n", "        -1\n", "    \\\"\\\"\\\"\n", "    max_num = numbers[0]\n", "    for num in numbers:\n", "        if num > max_num:\n", "            max_num = num\n", "    return max_num\n", "```\n", "\n", "现在请为以下函数生成文档：\n", "```python\n", "def merge_sorted_lists(list1, list2):\n", "    result = []\n", "    i, j = 0, 0\n", "    while i < len(list1) and j < len(list2):\n", "        if list1[i] <= list2[j]:\n", "            result.append(list1[i])\n", "            i += 1\n", "        else:\n", "            result.append(list2[j])\n", "            j += 1\n", "    result.extend(list1[i:])\n", "    result.extend(list2[j:])\n", "    return result\n", "```\n", "\n", "生成文档：\n", "\"\"\"\n", "\n", "print(\"=== 代码文档生成提示 ===\")\n", "print(\"输入函数：\")\n", "print(\"\"\"def merge_sorted_lists(list1, list2):\n", "    result = []\n", "    i, j = 0, 0\n", "    while i < len(list1) and j < len(list2):\n", "        if list1[i] <= list2[j]:\n", "            result.append(list1[i])\n", "            i += 1\n", "        else:\n", "            result.append(list2[j])\n", "            j += 1\n", "    result.extend(list1[i:])\n", "    result.extend(list2[j:])\n", "    return result\"\"\")\n", "\n", "print(\"\\n\" + \"=\"*50 + \"\\n\")\n", "\n", "# 获取代码文档生成结果\n", "code_doc_result = get_completion(code_documentation_prompt)\n", "print(\"生成的文档：\")\n", "print(code_doc_result)\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### 🎯 代码文档生成的关键要素\n", "\n", "通过上面的示例，我们可以看到高质量代码文档包含：\n", "\n", "1. **简洁描述**：一句话概括函数功能\n", "2. **详细说明**：算法思路、时间复杂度等\n", "3. **参数说明**：类型、含义、约束条件\n", "4. **返回值**：类型和描述\n", "5. **异常处理**：可能的异常情况\n", "6. **使用示例**：具体的调用例子\n", "\n", "### 💼 企业应用价值\n", "\n", "- **提升代码质量**：统一的文档格式标准\n", "- **降低维护成本**：新员工快速理解代码\n", "- **加速开发效率**：自动生成规范文档\n", "- **减少沟通成本**：清晰的接口说明\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### 2. 🔄 示例质量优化原则\n", "\n", "#### 示例选择的黄金法则：\n", "\n", "1. **多样性原则**：覆盖不同场景和边界情况\n", "2. **质量原则**：确保示例本身的准确性和专业性  \n", "3. **简洁原则**：示例要简洁明了，突出关键点\n", "4. **相关性原则**：示例与目标任务高度相关\n", "5. **渐进性原则**：从简单到复杂，逐步展示\n", "\n", "#### 常见错误避免：\n", "\n", "❌ **过度复杂的示例**：示例过于复杂，模型难以抓住重点\n", "❌ **示例不一致**：多个示例之间格式或风格不统一\n", "❌ **缺乏多样性**：所有示例都是同一类型，缺乏覆盖面\n", "❌ **示例有误**：示例本身存在错误，误导模型学习\n", "\n", "### 3. 📏 最佳实践总结\n", "\n", "| 应用场景 | 推荐示例数量 | 关键技巧 |\n", "|----------|--------------|----------|\n", "| **格式转换** | 2-3个 | 展示输入输出的对应关系 |\n", "| **文本分类** | 3-5个 | 覆盖每个类别的典型示例 |\n", "| **代码生成** | 2-4个 | 从简单到复杂的代码示例 |\n", "| **创意写作** | 3-6个 | 展示不同风格和主题 |\n", "| **数据分析** | 2-3个 | 包含不同数据类型和分析角度 |\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 4}
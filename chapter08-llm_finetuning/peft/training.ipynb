{"cells": [{"cell_type": "markdown", "metadata": {"id": "V5E5iBq0vdtb"}, "source": ["# 微调预训练模型"]}, {"cell_type": "markdown", "metadata": {"id": "RxTFzIDEvdtd"}, "source": ["使用预训练模型有许多显著的好处。它降低了计算成本，减少了碳排放，同时允许您使用最先进的模型，而无需从头开始训练一个。🤗 Transformers 提供了涉及各种任务的成千上万的预训练模型。当您使用预训练模型时，您需要在与任务相关的数据集上训练该模型。这种操作被称为微调，是一种非常强大的训练技术。在本教程中，您将使用您选择的深度学习框架来微调一个预训练模型：\n", "\n", "* 使用 🤗 Transformers 的 `Trainer` 来微调预训练模型。\n", "* 在 TensorFlow 中使用 Keras 来微调预训练模型。\n", "* 在原生 PyTorch 中微调预训练模型。\n", "\n", "<a id='data-processing'></a>"]}, {"cell_type": "markdown", "metadata": {"id": "0MsN1Socvdte"}, "source": ["## 准备数据集"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "hide_input": true, "id": "-eweVu-ivdte", "outputId": "233d4e51-a02c-4e86-8290-f732b3174a17"}, "outputs": [{"data": {"text/html": ["<iframe width=\"560\" height=\"315\" src=\"https://www.youtube.com/embed/_BZearw7f0w?rel=0&amp;controls=0&amp;showinfo=0\" frameborder=\"0\" allowfullscreen></iframe>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["#@title\n", "from IPython.display import HTML\n", "\n", "HTML('<iframe width=\"560\" height=\"315\" src=\"https://www.youtube.com/embed/_BZearw7f0w?rel=0&amp;controls=0&amp;showinfo=0\" frameborder=\"0\" allowfullscreen></iframe>')"]}, {"cell_type": "markdown", "metadata": {"id": "nn6ELxusvdtg"}, "source": ["在您进行预训练模型微调之前，需要下载一个数据集并为训练做好准备。之前的教程向您展示了如何处理训练数据，现在您有机会将这些技能付诸实践！\n", "\n", "首先，加载[Yelp评论](https://huggingface.co/datasets/yelp_review_full)数据集："]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "3hu-mq2Rvdtg", "outputId": "dfd6eed0-588f-4d6b-8615-f3f776a4726e"}, "outputs": [{"data": {"text/plain": ["{'label': 0,\n", " 'text': 'My expectations for McDonalds are t rarely high. But for one to still fail so spectacularly...that takes something special!\\\\nThe cashier took my friends\\'s order, then promptly ignored me. I had to force myself in front of a cashier who opened his register to wait on the person BEHIND me. I waited over five minutes for a gigantic order that included precisely one kid\\'s meal. After watching two people who ordered after me be handed their food, I asked where mine was. The manager started yelling at the cashiers for \\\\\"serving off their orders\\\\\" when they didn\\'t have their food. But neither cashier was anywhere near those controls, and the manager was the one serving food to customers and clearing the boards.\\\\nThe manager was rude when giving me my order. She didn\\'t make sure that I had everything ON MY RECEIPT, and never even had the decency to apologize that I felt I was getting poor service.\\\\nI\\'ve eaten at various McDonalds restaurants for over 30 years. I\\'ve worked at more than one location. I expect bad days, bad moods, and the occasional mistake. But I have yet to have a decent experience at this store. It will remain a place I avoid unless someone in my party needs to avoid illness from low blood sugar. Perhaps I should go back to the racially biased service of Steak n Shake instead!'}"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["from datasets import load_dataset\n", "\n", "dataset = load_dataset(\"yelp_review_full\")\n", "dataset[\"train\"][100]"]}, {"cell_type": "markdown", "metadata": {"id": "p7mPJCrnvdtg"}, "source": ["正如您现在所知，您需要一个`tokenizer`来处理文本，包括填充和截断操作以处理可变的序列长度。如果要一次性处理您的数据集，可以使用 🤗 Datasets 的 [`map`](https://huggingface.co/docs/datasets/process#map) 方法，将预处理函数应用于整个数据集："]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "yLZjNlu1vdth"}, "outputs": [], "source": ["from transformers import AutoTokenizer\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(\"google-bert/bert-base-cased\")\n", "\n", "\n", "def tokenize_function(examples):\n", "    return tokenizer(examples[\"text\"], padding=\"max_length\", truncation=True)\n", "\n", "\n", "tokenized_datasets = dataset.map(tokenize_function, batched=True)"]}, {"cell_type": "markdown", "metadata": {"id": "Emygllbsvdth"}, "source": ["如果愿意的话，您可以从完整数据集提取一个较小子集来进行微调，以减少训练所需的时间："]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0dRJBY68vdth"}, "outputs": [], "source": ["small_train_dataset = tokenized_datasets[\"train\"].shuffle(seed=42).select(range(1000))\n", "small_eval_dataset = tokenized_datasets[\"test\"].shuffle(seed=42).select(range(1000))"]}, {"cell_type": "markdown", "metadata": {"id": "7sOzLikVvdti"}, "source": ["<a id='trainer'></a>"]}, {"cell_type": "markdown", "metadata": {"id": "S-yfJGKZvdti"}, "source": ["## 训练"]}, {"cell_type": "markdown", "metadata": {"id": "gLzqUaDtvdti"}, "source": ["此时，您应该根据您训练所用的框架来选择对应的教程章节。您可以使用右侧的链接跳转到您想要的章节 - 如果您想隐藏某个框架对应的所有教程内容，只需使用右上角的按钮！"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "hide_input": true, "id": "SQKrfqb2vdti", "outputId": "8980a7c6-692b-4f7b-e17d-ef68f3b8e3e3"}, "outputs": [{"data": {"text/html": ["<iframe width=\"560\" height=\"315\" src=\"https://www.youtube.com/embed/nvBXf7s7vTI?rel=0&amp;controls=0&amp;showinfo=0\" frameborder=\"0\" allowfullscreen></iframe>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["#@title\n", "from IPython.display import HTML\n", "\n", "HTML('<iframe width=\"560\" height=\"315\" src=\"https://www.youtube.com/embed/nvBXf7s7vTI?rel=0&amp;controls=0&amp;showinfo=0\" frameborder=\"0\" allowfullscreen></iframe>')"]}, {"cell_type": "markdown", "metadata": {"id": "sCNrXk32vdti"}, "source": ["## 使用 PyTorch Trainer 进行训练"]}, {"cell_type": "markdown", "metadata": {"id": "X0XuDer6vdti"}, "source": ["🤗 Transformers 提供了一个专为训练 🤗 Transformers 模型而优化的 `Trainer` 类，使您无需手动编写自己的训练循环步骤而更轻松地开始训练模型。`Trainer` API 支持各种训练选项和功能，如日志记录、梯度累积和混合精度。\n", "\n", "首先加载您的模型并指定期望的标签数量。根据 Yelp Review [数据集卡片](https://huggingface.co/datasets/yelp_review_full#data-fields)，您知道有五个标签："]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "jDTUVRodvdti"}, "outputs": [], "source": ["from transformers import AutoModelForSequenceClassification\n", "\n", "model = AutoModelForSequenceClassification.from_pretrained(\"google-bert/bert-base-cased\", num_labels=5)"]}, {"cell_type": "markdown", "metadata": {"id": "HRfEGKfhvdtj"}, "source": ["<Tip>\n", "\n", "您将会看到一个警告，提到一些预训练权重未被使用，以及一些权重被随机初始化。不用担心，这是完全正常的！BERT 模型的预训练`head`被丢弃，并替换为一个随机初始化的分类`head`。您将在您的序列分类任务上微调这个新模型`head`，将预训练模型的知识转移给它。\n", "\n", "</Tip>"]}, {"cell_type": "markdown", "metadata": {"id": "fLJkBb5uvdtj"}, "source": ["### 训练超参数"]}, {"cell_type": "markdown", "metadata": {"id": "jyR4zz3xvdtj"}, "source": ["接下来，创建一个 `TrainingArguments` 类，其中包含您可以调整的所有超参数以及用于激活不同训练选项的标志。对于本教程，您可以从默认的训练[超参数](https://huggingface.co/docs/transformers/main_classes/trainer#transformers.TrainingArguments)开始，但随时可以尝试不同的设置以找到最佳设置。\n", "\n", "指定保存训练检查点的位置："]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "D_TRjdscvdtj"}, "outputs": [], "source": ["from transformers import TrainingArguments\n", "\n", "training_args = TrainingArguments(output_dir=\"test_trainer\")"]}, {"cell_type": "markdown", "metadata": {"id": "K3669Txvvdtj"}, "source": ["### 评估"]}, {"cell_type": "markdown", "metadata": {"id": "4jQcJSO8vdtj"}, "source": ["`Trainer` 在训练过程中不会自动评估模型性能。您需要向 `Trainer` 传递一个函数来计算和展示指标。[🤗 Evaluate](https://huggingface.co/docs/evaluate/index) 库提供了一个简单的 [`accuracy`](https://huggingface.co/spaces/evaluate-metric/accuracy) 函数，您可以使用 `evaluate.load` 函数加载它（有关更多信息，请参阅此[快速入门](https://huggingface.co/docs/evaluate/a_quick_tour)）："]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "8ZJcBC2Ovdtj"}, "outputs": [], "source": ["import numpy as np\n", "import evaluate\n", "\n", "metric = evaluate.load(\"accuracy\")"]}, {"cell_type": "markdown", "metadata": {"id": "SnH8e-iCvdtj"}, "source": ["在 `metric` 上调用 `compute` 来计算您的预测的准确性。在将预测传递给 `compute` 之前，您需要将预测转换为`logits`（请记住，所有 🤗 Transformers 模型都返回对`logits`）："]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "RZP5I1dkvdtj"}, "outputs": [], "source": ["def compute_metrics(eval_pred):\n", "    logits, labels = eval_pred\n", "    predictions = np.argmax(logits, axis=-1)\n", "    return metric.compute(predictions=predictions, references=labels)"]}, {"cell_type": "markdown", "metadata": {"id": "WG_caRxTvdtj"}, "source": ["如果您希望在微调过程中监视评估指标，请在您的训练参数中指定 `eval_strategy` 参数，以在每个`epoch`结束时展示评估指标："]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "kW-h3NQTvdtj"}, "outputs": [], "source": ["from transformers import TrainingArguments, Trainer\n", "\n", "training_args = TrainingArguments(output_dir=\"test_trainer\", eval_strategy=\"epoch\")"]}, {"cell_type": "markdown", "metadata": {"id": "fPWCgvz4vdtk"}, "source": ["### 训练器"]}, {"cell_type": "markdown", "metadata": {"id": "O1Jd-izdvdtk"}, "source": ["创建一个包含您的模型、训练参数、训练和测试数据集以及评估函数的 `Trainer` 对象："]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "6U-0NI-Xvdtk"}, "outputs": [], "source": ["trainer = Trainer(\n", "    model=model,\n", "    args=training_args,\n", "    train_dataset=small_train_dataset,\n", "    eval_dataset=small_eval_dataset,\n", "    compute_metrics=compute_metrics,\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "BF3GjLKjvdtk"}, "source": ["然后调用`train()`以微调模型："]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "bZw_YbAIvdtk"}, "outputs": [], "source": ["trainer.train()"]}, {"cell_type": "markdown", "metadata": {"id": "XpYwUxuwvdtk"}, "source": ["<a id='pytorch_native'></a>"]}, {"cell_type": "markdown", "metadata": {"id": "jQZ0YLPivdtk"}, "source": ["## 在原生 PyTorch 中训练"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "hide_input": true, "id": "rrRoyMQ7vdtk", "outputId": "893292a3-9208-43c7-e07f-23142df644e1"}, "outputs": [{"data": {"text/html": ["<iframe width=\"560\" height=\"315\" src=\"https://www.youtube.com/embed/Dh9CL8fyG80?rel=0&amp;controls=0&amp;showinfo=0\" frameborder=\"0\" allowfullscreen></iframe>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["#@title\n", "from IPython.display import HTML\n", "\n", "HTML('<iframe width=\"560\" height=\"315\" src=\"https://www.youtube.com/embed/Dh9CL8fyG80?rel=0&amp;controls=0&amp;showinfo=0\" frameborder=\"0\" allowfullscreen></iframe>')"]}, {"cell_type": "markdown", "metadata": {"id": "R7-lmNObvdtk"}, "source": ["`Trainer` 负责训练循环，允许您在一行代码中微调模型。对于喜欢编写自己训练循环的用户，您也可以在原生 PyTorch 中微调 🤗 Transformers 模型。\n", "\n", "现在，您可能需要重新启动您的`notebook`，或执行以下代码以释放一些内存："]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "_OsHWergvdtl"}, "outputs": [], "source": ["del model\n", "del <PERSON>\n", "torch.cuda.empty_cache()"]}, {"cell_type": "markdown", "metadata": {"id": "iwImnQQvvdtl"}, "source": ["接下来，手动处理 `tokenized_dataset` 以准备进行训练。\n", "\n", "1. 移除 text 列，因为模型不接受原始文本作为输入：\n", "\n", "    ```py\n", "    >>> tokenized_datasets = tokenized_datasets.remove_columns([\"text\"])\n", "    ```\n", "\n", "2. 将 label 列重命名为 labels，因为模型期望参数的名称为 labels：\n", "\n", "    ```py\n", "    >>> tokenized_datasets = tokenized_datasets.rename_column(\"label\", \"labels\")\n", "    ```\n", "\n", "3. 设置数据集的格式以返回 PyTorch 张量而不是`lists`：\n", "\n", "    ```py\n", "    >>> tokenized_datasets.set_format(\"torch\")\n", "    ```\n", "\n", "接着，创建一个先前展示的数据集的较小子集，以加速微调过程"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "LaRmdIBavdtp"}, "outputs": [], "source": ["small_train_dataset = tokenized_datasets[\"train\"].shuffle(seed=42).select(range(1000))\n", "small_eval_dataset = tokenized_datasets[\"test\"].shuffle(seed=42).select(range(1000))"]}, {"cell_type": "markdown", "metadata": {"id": "ylQRT7I4vdtp"}, "source": ["### Data<PERSON><PERSON>der"]}, {"cell_type": "markdown", "metadata": {"id": "LbjbAPqfvdtp"}, "source": ["您的训练和测试数据集创建一个`DataLoader`类，以便可以迭代处理数据批次"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "XH1Y43AFvdtp"}, "outputs": [], "source": ["from torch.utils.data import DataLoader\n", "\n", "train_dataloader = DataLoader(small_train_dataset, shuffle=True, batch_size=8)\n", "eval_dataloader = DataLoader(small_eval_dataset, batch_size=8)"]}, {"cell_type": "markdown", "metadata": {"id": "8agee-i1vdtp"}, "source": ["加载您的模型，并指定期望的标签数量："]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "LG1iApfUvdtp"}, "outputs": [], "source": ["from transformers import AutoModelForSequenceClassification\n", "\n", "model = AutoModelForSequenceClassification.from_pretrained(\"google-bert/bert-base-cased\", num_labels=5)"]}, {"cell_type": "markdown", "metadata": {"id": "paCTO8v9vdtp"}, "source": ["### Optimizer and learning rate scheduler"]}, {"cell_type": "markdown", "metadata": {"id": "zliaw7Mzvdtq"}, "source": ["创建一个`optimizer`和`learning rate scheduler`以进行模型微调。让我们使用 PyTorch 中的 [AdamW](https://pytorch.org/docs/stable/generated/torch.optim.AdamW.html) 优化器："]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "p0IjLSZRvdtq"}, "outputs": [], "source": ["from torch.optim import AdamW\n", "\n", "optimizer = AdamW(model.parameters(), lr=5e-5)"]}, {"cell_type": "markdown", "metadata": {"id": "-w3OJHEPvdtq"}, "source": ["创建来自 `Trainer` 的默认`learning rate scheduler`："]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "_kE5TIWyvdtq"}, "outputs": [], "source": ["from transformers import get_scheduler\n", "\n", "num_epochs = 3\n", "num_training_steps = num_epochs * len(train_dataloader)\n", "lr_scheduler = get_scheduler(\n", "    name=\"linear\", optimizer=optimizer, num_warmup_steps=0, num_training_steps=num_training_steps\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "Gkr-XAvNvdtq"}, "source": ["最后，指定 `device` 以使用 GPU（如果有的话）。否则，使用 CPU 进行训练可能需要几个小时，而不是几分钟。"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "VdzkT9qAvdtq"}, "outputs": [], "source": ["import torch\n", "\n", "device = torch.device(\"cuda\") if torch.cuda.is_available() else torch.device(\"cpu\")\n", "model.to(device)"]}, {"cell_type": "markdown", "metadata": {"id": "szP2iIElvdtq"}, "source": ["<Tip>\n", "\n", "如果没有 GPU，可以通过notebook平台如 [Colaboratory](https://colab.research.google.com/) 或 [SageMaker StudioLab](https://studiolab.sagemaker.aws/) 来免费获得云端GPU使用。\n", "\n", "</Tip>\n", "\n", "现在您已经准备好训练了！🥳"]}, {"cell_type": "markdown", "metadata": {"id": "DvPrw8FCvdtq"}, "source": ["### 训练循环"]}, {"cell_type": "markdown", "metadata": {"id": "XogQG67Bvdtq"}, "source": ["为了跟踪训练进度，使用 [tqdm](https://tqdm.github.io/) 库来添加一个进度条，显示训练步数的进展："]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "YK9LHoZrvdtq"}, "outputs": [], "source": ["from tqdm.auto import tqdm\n", "\n", "progress_bar = tqdm(range(num_training_steps))\n", "\n", "model.train()\n", "for epoch in range(num_epochs):\n", "    for batch in train_dataloader:\n", "        batch = {k: v.to(device) for k, v in batch.items()}\n", "        outputs = model(**batch)\n", "        loss = outputs.loss\n", "        loss.backward()\n", "\n", "        optimizer.step()\n", "        lr_scheduler.step()\n", "        optimizer.zero_grad()\n", "        progress_bar.update(1)"]}, {"cell_type": "markdown", "metadata": {"id": "2jipxikAvdtr"}, "source": ["### 评估"]}, {"cell_type": "markdown", "metadata": {"id": "qAB4agvIvdtr"}, "source": ["就像您在 `Trainer` 中添加了一个评估函数一样，当您编写自己的训练循环时，您需要做同样的事情。但与在每个`epoch`结束时计算和展示指标不同，这一次您将使用 `add_batch` 累积所有批次，并在最后计算指标。"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "A4J1BZa3vdtr"}, "outputs": [], "source": ["import evaluate\n", "\n", "metric = evaluate.load(\"accuracy\")\n", "model.eval()\n", "for batch in eval_dataloader:\n", "    batch = {k: v.to(device) for k, v in batch.items()}\n", "    with torch.no_grad():\n", "        outputs = model(**batch)\n", "\n", "    logits = outputs.logits\n", "    predictions = torch.argmax(logits, dim=-1)\n", "    metric.add_batch(predictions=predictions, references=batch[\"labels\"])\n", "\n", "metric.compute()"]}, {"cell_type": "markdown", "metadata": {"id": "-Jsa01A4vdtr"}, "source": ["<a id='additional-resources'></a>"]}, {"cell_type": "markdown", "metadata": {"id": "sUCy1YdVvdtr"}, "source": ["## 附加资源"]}, {"cell_type": "markdown", "metadata": {"id": "nByxZvuLvdtr"}, "source": ["更多微调例子可参考如下链接：\n", "\n", "- [🤗 Transformers 示例](https://github.com/huggingface/transformers/tree/main/examples) 包含用于在 PyTorch 和 TensorFlow 中训练常见自然语言处理任务的脚本。\n", "\n", "- [🤗 Transformers 笔记](https://huggingface.co/docs/transformers/main/zh/notebooks) 包含针对特定任务在 PyTorch 和 TensorFlow 中微调模型的各种`notebook`。"]}], "metadata": {"colab": {"provenance": []}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}
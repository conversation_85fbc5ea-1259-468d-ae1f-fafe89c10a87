{
 "cells": [
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Transformers 安装\n",
    "! pip install transformers datasets evaluate accelerate\n",
    "# 如果要从源码安装而不是最新发布版本，请注释上面的命令并取消注释下面的命令。\n",
    "# ! pip install git+https://github.com/huggingface/transformers.git"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# 文本生成"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "文本生成是大语言模型（LLMs）最受欢迎的应用。大语言模型经过训练，能够根据给定的初始文本（提示词）以及其自身生成的输出，生成下一个词（token），直到达到预定义的长度或遇到序列结束（`EOS`）token。\n",
    "\n",
    "在 Transformers 中，[generate()](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationMixin.generate) API 负责处理文本生成，它适用于所有具有生成能力的模型。本指南将向您展示使用 [generate()](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationMixin.generate) 进行文本生成的基础知识以及一些需要避免的常见陷阱。\n",
    "\n",
    "> [!TIP]\n",
    "> 您也可以直接从命令行与模型对话。([参考文档](https://huggingface.co/docs/transformers/main/en/./conversations.md#transformers-cli))\n",
    "> ```shell\n",
    "> transformers chat Qwen/Qwen2.5-0.5B-Instruct\n",
    "> ```"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 默认生成"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "在开始之前，建议安装 [bitsandbytes](https://hf.co/docs/bitsandbytes/index) 来量化超大模型以减少内存使用。\n",
    "\n",
    "```bash\n",
    "!pip install -U transformers bitsandbytes\n",
    "```\n",
    "除了基于 CUDA 的 GPU 外，Bitsandbytes 还支持多种后端。请参考多后端安装[指南](https://huggingface.co/docs/bitsandbytes/main/en/installation#multi-backend)了解更多信息。\n",
    "\n",
    "使用 [from_pretrained()](https://huggingface.co/docs/transformers/main/en/main_classes/model#transformers.PreTrainedModel.from_pretrained) 加载大语言模型，并添加以下两个参数来减少内存需求。\n",
    "\n",
    "- `device_map=\"auto\"` 启用 Accelerate 的[大模型推理](https://huggingface.co/docs/transformers/main/en/./models#big-model-inference)功能，用于自动初始化模型骨架并在所有可用设备上加载和分发模型权重，从最快的设备（GPU）开始。\n",
    "- `quantization_config` 是定义量化设置的配置对象。此示例使用 bitsandbytes 作为量化后端（有关更多可用后端，请参见[量化](https://huggingface.co/docs/transformers/main/en/./quantization/overview)部分），并以 [4位](https://huggingface.co/docs/transformers/main/en/./quantization/bitsandbytes)精度加载模型。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig\n",
    "\n",
    "quantization_config = BitsAndBytesConfig(load_in_4bit=True)\n",
    "model = AutoModelForCausalLM.from_pretrained(\"mistralai/Mistral-7B-v0.1\", device_map=\"auto\", quantization_config=quantization_config)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "对输入进行分词，并将 `padding_side()` 参数设置为 `\"left\"`，因为大语言模型没有被训练从填充token继续生成。分词器返回输入ID和注意力掩码。\n",
    "\n",
    "> [!TIP]\n",
    "> 通过向分词器传递字符串列表，可以一次处理多个提示词。批量处理输入可以提高吞吐量，但会略微增加延迟和内存消耗。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "tokenizer = AutoTokenizer.from_pretrained(\"mistralai/Mistral-7B-v0.1\", padding_side=\"left\")\n",
    "model_inputs = tokenizer([\"A list of colors: red, blue\"], return_tensors=\"pt\").to(\"cuda\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "将输入传递给 [generate()](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationMixin.generate) 来生成token，并使用 [batch_decode()](https://huggingface.co/docs/transformers/main/en/internal/tokenization_utils#transformers.PreTrainedTokenizerBase.batch_decode) 将生成的token解码回文本。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "generated_ids = model.generate(**model_inputs)\n",
    "tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]\n",
    "\"A list of colors: red, blue, green, yellow, orange, purple, pink,\""
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 生成配置"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "所有生成设置都包含在 [GenerationConfig](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationConfig) 中。在上面的示例中，生成设置来自 [mistralai/Mistral-7B-v0.1](https://huggingface.co/mistralai/Mistral-7B-v0.1) 的 `generation_config.json` 文件。当模型没有保存配置时，会使用默认的解码策略。\n",
    "\n",
    "通过 `generation_config` 属性检查配置。它只显示与默认配置不同的值，在这种情况下是 `bos_token_id` 和 `eos_token_id`。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "from transformers import AutoModelForCausalLM\n",
    "\n",
    "model = AutoModelForCausalLM.from_pretrained(\"mistralai/Mistral-7B-v0.1\", device_map=\"auto\")\n",
    "model.generation_config\n",
    "GenerationConfig {\n",
    "  \"bos_token_id\": 1,\n",
    "  \"eos_token_id\": 2\n",
    "}"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "您可以通过覆盖 [GenerationConfig](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationConfig) 中的参数和值来自定义 [generate()](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationMixin.generate)。有关常用调整参数，请参见[下面的部分](#common-options)。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 启用束搜索采样策略\n",
    "model.generate(**inputs, num_beams=4, do_sample=True)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "[generate()](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationMixin.generate) 也可以通过外部库或自定义代码进行扩展：\n",
    "1. `logits_processor` 参数接受自定义的 [LogitsProcessor](https://huggingface.co/docs/transformers/main/en/internal/generation_utils#transformers.LogitsProcessor) 实例，用于操作下一个token的概率分布；\n",
    "2. `stopping_criteria` 参数支持自定义的 [StoppingCriteria](https://huggingface.co/docs/transformers/main/en/internal/generation_utils#transformers.StoppingCriteria) 来停止文本生成；\n",
    "3. 其他自定义生成方法可以通过 `custom_generate` 标志加载（[文档](https://huggingface.co/docs/transformers/main/en/generation_strategies.md/#custom-decoding-methods)）。\n",
    "\n",
    "请参考[生成策略](https://huggingface.co/docs/transformers/main/en/./generation_strategies)指南，了解更多关于搜索、采样和解码策略的信息。"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 保存"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "创建一个 [GenerationConfig](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationConfig) 实例并指定您想要的解码参数。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "from transformers import AutoModelForCausalLM, GenerationConfig\n",
    "\n",
    "model = AutoModelForCausalLM.from_pretrained(\"my_account/my_model\")\n",
    "generation_config = GenerationConfig(\n",
    "    max_new_tokens=50, do_sample=True, top_k=50, eos_token_id=model.config.eos_token_id\n",
    ")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "使用 [save_pretrained()](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationConfig.save_pretrained) 保存特定的生成配置，并将 `push_to_hub` 参数设置为 `True` 以将其上传到 Hub。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "generation_config.save_pretrained(\"my_account/my_model\", push_to_hub=True)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "将 `config_file_name` 参数留空。当在单个目录中存储多个生成配置时，应使用此参数。它为您提供了一种指定要加载哪个生成配置的方法。您可以为不同的生成任务创建不同的配置（使用采样的创意文本生成、使用束搜索的摘要生成），以便与单个模型一起使用。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "from transformers import AutoModelForSeq2SeqLM, AutoTokenizer, GenerationConfig\n",
    "\n",
    "tokenizer = AutoTokenizer.from_pretrained(\"google-t5/t5-small\")\n",
    "model = AutoModelForSeq2SeqLM.from_pretrained(\"google-t5/t5-small\")\n",
    "\n",
    "translation_generation_config = GenerationConfig(\n",
    "    num_beams=4,\n",
    "    early_stopping=True,\n",
    "    decoder_start_token_id=0,\n",
    "    eos_token_id=model.config.eos_token_id,\n",
    "    pad_token=model.config.pad_token_id,\n",
    ")\n",
    "\n",
    "translation_generation_config.save_pretrained(\"/tmp\", config_file_name=\"translation_generation_config.json\", push_to_hub=True)\n",
    "\n",
    "generation_config = GenerationConfig.from_pretrained(\"/tmp\", config_file_name=\"translation_generation_config.json\")\n",
    "inputs = tokenizer(\"translate English to French: Configuration files are easy to use!\", return_tensors=\"pt\")\n",
    "outputs = model.generate(**inputs, generation_config=generation_config)\n",
    "print(tokenizer.batch_decode(outputs, skip_special_tokens=True))"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 常用选项"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "[generate()](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationMixin.generate) 是一个功能强大且可高度自定义的工具。这对新用户来说可能会感到困惑。本节包含了您可以在 Transformers 的大多数文本生成工具中定义的常用生成选项列表：[generate()](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationMixin.generate)、[GenerationConfig](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationConfig)、`pipelines`、`chat` CLI 等...\n",
    "\n",
    "| 选项名称 | 类型 | 简化描述 |\n",
    "|---|---|---|\n",
    "| `max_new_tokens` | `int` | 控制最大生成长度。请务必定义它，因为它通常默认为一个较小的值。 |\n",
    "| `do_sample` | `bool` | 定义生成是否会采样下一个token（`True`），或者使用贪心策略（`False`）。大多数用例应将此标志设置为 `True`。查看[此指南](https://huggingface.co/docs/transformers/main/en/./generation_strategies.md)了解更多信息。 |\n",
    "| `temperature` | `float` | 下一个选择的token的不可预测性。高值（`>0.8`）适合创意任务，低值（例如 `<0.4`）适合需要"思考"的任务。需要 `do_sample=True`。 |\n",
    "| `num_beams` | `int` | 当设置为 `>1` 时，激活束搜索算法。束搜索在基于输入的任务上表现良好。查看[此指南](https://huggingface.co/docs/transformers/main/en/./generation_strategies.md)了解更多信息。 |\n",
    "| `repetition_penalty` | `float` | 如果您发现模型经常重复自己，请将其设置为 `>1.0`。较大的值会施加更大的惩罚。 |\n",
    "| `eos_token_id` | `list[int]` | 将导致生成停止的token。默认值通常是好的，但您可以指定不同的token。 |"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 常见陷阱"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "下面的部分涵盖了您在文本生成过程中可能遇到的一些常见问题以及如何解决它们。"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 输出长度"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "[generate()](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationMixin.generate) 默认返回最多20个token，除非在模型的 [GenerationConfig](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationConfig) 中另有指定。强烈建议使用 `max_new_tokens` 参数手动设置生成的token数量来控制输出长度。[仅解码器](https://hf.co/learn/nlp-course/chapter1/6?fw=pt)模型返回初始提示词以及生成的token。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "model_inputs = tokenizer([\"A sequence of numbers: 1, 2\"], return_tensors=\"pt\").to(\"cuda\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "<hfoptions id=\"output-length\">\n",
    "<hfoption id=\"default length\">"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "generated_ids = model.generate(**model_inputs)\n",
    "tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]\n",
    "'A sequence of numbers: 1, 2, 3, 4, 5'"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "</hfoption>\n",
    "<hfoption id=\"max_new_tokens\">"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "generated_ids = model.generate(**model_inputs, max_new_tokens=50)\n",
    "tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]\n",
    "'A sequence of numbers: 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16,'"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "</hfoption>\n",
    "</hfoptions>"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 解码策略"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "[generate()](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationMixin.generate) 中的默认解码策略是*贪心搜索*，它选择下一个最可能的token，除非在模型的 [GenerationConfig](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationConfig) 中另有指定。虽然这种解码策略在基于输入的任务（转录、翻译）上表现良好，但对于更具创意的用例（故事写作、聊天应用）来说并不是最优的。\n",
    "\n",
    "例如，启用[多项式采样](https://huggingface.co/docs/transformers/main/en/./generation_strategies#multinomial-sampling)策略来生成更多样化的输出。请参考[生成策略](https://huggingface.co/docs/transformers/main/en/./generation_strategies)指南了解更多解码策略。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "model_inputs = tokenizer([\"I am a cat.\"], return_tensors=\"pt\").to(\"cuda\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "<hfoptions id=\"decoding\">\n",
    "<hfoption id=\"greedy search\">"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "generated_ids = model.generate(**model_inputs)\n",
    "tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "</hfoption>\n",
    "<hfoption id=\"multinomial sampling\">"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "generated_ids = model.generate(**model_inputs, do_sample=True)\n",
    "tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "</hfoption>\n",
    "</hfoptions>"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 填充方向"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "如果输入长度不同，需要进行填充。但是大语言模型没有被训练从填充token继续生成，这意味着 `padding_side()` 参数需要设置为输入的左侧。\n",
    "\n",
    "<hfoptions id=\"padding\">\n",
    "<hfoption id=\"right pad\">"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "model_inputs = tokenizer(\n",
    "    [\"1, 2, 3\", \"A, B, C, D, E\"], padding=True, return_tensors=\"pt\"\n",
    ").to(\"cuda\")\n",
    "generated_ids = model.generate(**model_inputs)\n",
    "tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]\n",
    "'1, 2, 33333333333'"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "</hfoption>\n",
    "<hfoption id=\"left pad\">"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "tokenizer = AutoTokenizer.from_pretrained(\"mistralai/Mistral-7B-v0.1\", padding_side=\"left\")\n",
    "tokenizer.pad_token = tokenizer.eos_token\n",
    "model_inputs = tokenizer(\n",
    "    [\"1, 2, 3\", \"A, B, C, D, E\"], padding=True, return_tensors=\"pt\"\n",
    ").to(\"cuda\")\n",
    "generated_ids = model.generate(**model_inputs)\n",
    "tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]\n",
    "'1, 2, 3, 4, 5, 6,'"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "</hfoption>\n",
    "</hfoptions>"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "### 提示词格式"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "某些模型和任务期望特定的输入提示词格式，如果格式不正确，模型会返回次优的输出。您可以在[提示词工程](https://huggingface.co/docs/transformers/main/en/./tasks/prompting)指南中了解更多关于提示词的信息。\n",
    "\n",
    "例如，聊天模型期望输入为[聊天模板](https://huggingface.co/docs/transformers/main/en/./chat_templating)。您的提示词应包含 `role` 和 `content` 来指示谁在参与对话。如果您尝试将提示词作为单个字符串传递，模型并不总是返回预期的输出。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "from transformers import AutoTokenizer, AutoModelForCausalLM\n",
    "\n",
    "tokenizer = AutoTokenizer.from_pretrained(\"HuggingFaceH4/zephyr-7b-alpha\")\n",
    "model = AutoModelForCausalLM.from_pretrained(\n",
    "    \"HuggingFaceH4/zephyr-7b-alpha\", device_map=\"auto\", load_in_4bit=True\n",
    ")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "<hfoptions id=\"format\">\n",
    "<hfoption id=\"no format\">"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "prompt = \"\"\"How many cats does it take to change a light bulb? Reply as a pirate.\"\"\"\n",
    "model_inputs = tokenizer([prompt], return_tensors=\"pt\").to(\"cuda\")\n",
    "input_length = model_inputs.input_ids.shape[1]\n",
    "generated_ids = model.generate(**model_inputs, max_new_tokens=50)\n",
    "print(tokenizer.batch_decode(generated_ids[:, input_length:], skip_special_tokens=True)[0])\n",
    "\"Aye, matey! 'Tis a simple task for a cat with a keen eye and nimble paws. First, the cat will climb up the ladder, carefully avoiding the rickety rungs. Then, with\""
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "</hfoption>\n",
    "<hfoption id=\"chat template\">"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "messages = [\n",
    "    {\n",
    "        \"role\": \"system\",\n",
    "        \"content\": \"You are a friendly chatbot who always responds in the style of a pirate\",\n",
    "    },\n",
    "    {\"role\": \"user\", \"content\": \"How many cats does it take to change a light bulb?\"},\n",
    "]\n",
    "model_inputs = tokenizer.apply_chat_template(messages, add_generation_prompt=True, return_tensors=\"pt\").to(\"cuda\")\n",
    "input_length = model_inputs.shape[1]\n",
    "generated_ids = model.generate(model_inputs, do_sample=True, max_new_tokens=50)\n",
    "print(tokenizer.batch_decode(generated_ids[:, input_length:], skip_special_tokens=True)[0])\n",
    "\"Arr, matey! According to me beliefs, 'twas always one cat to hold the ladder and another to climb up it an’ change the light bulb, but if yer looking to save some catnip, maybe yer can"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "</hfoption>\n",
    "</hfoptions>"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 资源"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "请查看下面一些更具体和专业的文本生成库。\n",
    "\n",
    "- [Optimum](https://github.com/huggingface/optimum)：Transformers 的扩展，专注于在特定硬件设备上优化训练和推理\n",
    "- [Outlines](https://github.com/dottxt-ai/outlines)：用于约束文本生成的库（例如生成 JSON 文件）。\n",
    "- [SynCode](https://github.com/uiuc-focal-lab/syncode)：用于上下文无关语法引导生成的库（JSON、SQL、Python）。\n",
    "- [Text Generation Inference](https://github.com/huggingface/text-generation-inference)：用于大语言模型的生产就绪服务器。\n",
    "- [Text generation web UI](https://github.com/oobabooga/text-generation-webui)：用于文本生成的 Gradio Web UI。\n",
    "- [logits-processor-zoo](https://github.com/NVIDIA/logits-processor-zoo)：用于控制文本生成的额外 logits 处理器。"
   ]
  }
 ],
 "metadata": {},
 "nbformat": 4,
 "nbformat_minor": 4
}

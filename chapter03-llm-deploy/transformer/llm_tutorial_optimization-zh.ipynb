{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Transformers 安装\n", "! pip install transformers datasets evaluate accelerate\n", "# 如果要从源码安装而不是最新发布版本，请注释上面的命令并取消注释下面的命令。\n", "# ! pip install git+https://github.com/huggingface/transformers.git"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 大语言模型的速度和内存优化"]}, {"cell_type": "markdown", "metadata": {}, "source": ["大语言模型（LLMs）如 GPT3/4、[Falcon](https://huggingface.co/tiiuae/falcon-40b) 和 [Llama](https://huggingface.co/meta-llama/Llama-2-70b-hf) 在处理以人为中心的任务方面正在快速发展，已成为现代知识型行业的重要工具。\\n然而，在实际任务中部署这些模型仍然面临挑战：\\n\\n-   为了展现接近人类的文本理解和生成能力，大语言模型目前需要由数十亿个参数组成（参见 [Kaplan et al](https://huggingface.co/papers/2001.08361)、[Wei et. al](https://huggingface.co/papers/2206.07682)）。这相应地增加了推理时的内存需求。\\n-   在许多实际任务中，大语言模型需要获得大量的上下文信息。这要求模型在推理过程中能够处理非常长的输入序列。\\n\\n这些挑战的核心在于增强大语言模型的计算和内存能力，特别是在处理大量输入序列时。\\n\\n在本指南中，我们将介绍高效部署大语言模型的有效技术：\\n\\n1.  **低精度计算：** 研究表明，在降低的数值精度下运行，即 [8位和4位](https://huggingface.co/docs/transformers/main/en/./main_classes/quantization.md) 可以在不显著降低模型性能的情况下获得计算优势。\\n\\n2.  **Flash Attention：** Flash Attention 是注意力算法的一种变体，不仅提供了更节省内存的方法，还由于优化的GPU内存利用率而实现了更高的效率。\\n\\n3.  **架构创新：** 考虑到大语言模型在推理过程中总是以相同的方式部署，即具有长输入上下文的自回归文本生成，已经提出了专门的模型架构，允许更高效的推理。模型架构中最重要的进展包括 [Alibi](https://huggingface.co/papers/2108.12409)、[旋转嵌入](https://huggingface.co/papers/2104.09864)、[多查询注意力（MQA）](https://huggingface.co/papers/1911.02150) 和 [分组查询注意力（GQA）](https://huggingface.co/docs/transformers/main/en/(https://huggingface.co/papers/2305.13245))。\\n\\n在本指南中，我们将从张量的角度分析自回归生成。我们深入探讨采用低精度的优缺点，全面探索最新的注意力算法，并讨论改进的大语言模型架构。在此过程中，我们运行实际示例来展示每个功能改进。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 低精度计算"]}, {"cell_type": "markdown", "metadata": {}, "source": ["大语言模型的内存需求可以通过将大语言模型视为一组权重矩阵和向量，将文本输入视为向量序列来最好地理解。在下文中，*权重*这一定义将用于表示所有模型权重矩阵和向量。\\n\\n在撰写本指南时，大语言模型至少由数十亿个参数组成。每个参数都由一个十进制数组成，例如 `4.5689`，通常以 [float32](https://en.wikipedia.org/wiki/Single-precision_floating-point_format)、[bfloat16](https://en.wikipedia.org/wiki/Bfloat16_floating-point_format) 或 [float16](https://en.wikipedia.org/wiki/Half-precision_floating-point_format) 格式存储。这使我们能够轻松计算将大语言模型加载到内存中的内存需求：\\n\\n> *加载具有 X 十亿参数的模型权重在 float32 精度下大约需要 4 * X GB 的显存*\\n\\n然而，如今的模型很少以完整的 float32 精度进行训练，通常以 bfloat16 精度训练，或较少情况下以 float16 精度训练。因此经验法则变为：\\n\\n> *加载具有 X 十亿参数的模型权重在 bfloat16/float16 精度下大约需要 2 * X GB 的显存*\\n\\n对于较短的文本输入（少于1024个token），推理的内存需求主要由加载权重的内存需求主导。因此，现在让我们假设推理的内存需求等于将模型加载到GPU显存中的内存需求。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["以下是一些在 bfloat16 精度下加载模型大约需要多少显存的示例：\\n\\n-   **GPT3** 需要 2 \\\\* 175 GB = **350 GB** 显存\\n-   [**Bloom**](https://huggingface.co/bigscience/bloom) 需要 2 \\\\* 176 GB = **352 GB** 显存\\n-   [**Llama-2-70b**](https://huggingface.co/meta-llama/Llama-2-70b-hf) 需要 2 \\\\* 70 GB = **140 GB** 显存\\n-   [**Falcon-40b**](https://huggingface.co/tiiuae/falcon-40b) 需要 2 \\\\* 40 GB = **80 GB** 显存\\n-   [**MPT-30b**](https://huggingface.co/mosaicml/mpt-30b) 需要 2 \\\\* 30 GB = **60 GB** 显存\\n-   [**bigcode/starcoder**](https://huggingface.co/bigcode/starcoder) 需要 2 \\\\* 15.5 = **31 GB** 显存\\n\\n截至撰写本文档时，市场上最大的GPU芯片是提供80GB显存的A100和H100。上面列出的大多数模型仅加载就需要超过80GB，因此必须需要[张量并行](https://huggingface.co/docs/transformers/perf_train_gpu_many#tensor-parallelism)和/或[流水线并行](https://huggingface.co/docs/transformers/perf_train_gpu_many#naive-model-parallelism-vertical-and-pipeline-parallelism)。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["🤗 Transformers 现在支持在其相应配置类中具有 `base_tp_plan` 的受支持模型的张量并行。在[这里](https://huggingface.co/docs/transformers/main/en/perf_train_gpu_many#tensor-parallelism)了解更多关于张量并行的信息。此外，如果您有兴趣以张量并行友好的方式编写模型，请随时查看[文本生成推理库](https://github.com/huggingface/text-generation-inference/tree/main/server/text_generation_server/models/custom_modeling)。\\n\\n朴素的流水线并行是开箱即用的。为此，只需使用 `device=\\\"auto\\\"` 加载模型，这将自动将不同的层放置在可用的GPU上，如[这里](https://huggingface.co/docs/accelerate/v0.22.0/en/concept_guides/big_model_inference)所解释的。\\n但是请注意，虽然非常有效，但这种朴素的流水线并行并不能解决GPU空闲的问题。为此需要更高级的流水线并行，如[这里](https://huggingface.co/docs/transformers/en/perf_train_gpu_many#naive-model-parallelism-vertical-and-pipeline-parallelism)所解释的。\\n\\n如果您可以访问8 x 80GB A100节点，您可以按如下方式加载BLOOM\\n\\n```bash\\n!pip install transformers accelerate bitsandbytes optimum\\n```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from transformers import AutoModelForCausalLM\n", "\n", "model = AutoModelForCausalLM.from_pretrained(\\\"bigscience/bloom\\\", device_map=\\\"auto\\\", pad_token_id=0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["通过使用 `device_map=\\\"auto\\\"`，注意力层将在所有可用的GPU上平均分布。\\n\\n在本指南中，我们将使用 [bigcode/octocoder](https://huggingface.co/bigcode/octocoder)，因为它可以在单个40GB A100 GPU设备芯片上运行。请注意，我们将要应用的所有内存和速度优化同样适用于需要模型或张量并行的模型。\\n\\n由于模型以 bfloat16 精度加载，使用我们上面的经验法则，我们预期使用 `bigcode/octocoder` 运行推理的内存需求约为31GB显存。让我们试一试。\\n\\n我们首先加载模型和分词器，然后将两者传递给 Transformers 的 [pipeline](https://huggingface.co/docs/transformers/main_classes/pipelines) 对象。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from transformers import AutoModelForCausalLM, AutoTokenizer, pipeline\\nimport torch\\n\\nmodel = AutoModelForCausalLM.from_pretrained(\\\"bigcode/octocoder\\\", torch_dtype=torch.bfloat16, device_map=\\\"auto\\\", pad_token_id=0)\\ntokenizer = AutoTokenizer.from_pretrained(\\\"bigcode/octocoder\\\")\\n\\npipe = pipeline(\\\"text-generation\\\", model=model, tokenizer=tokenizer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt = \\\"Question: Please write a function in Python that transforms bytes to Giga bytes.\\\\n\\\\nAnswer:\\\"\\n\\nresult = pipe(prompt, max_new_tokens=60)[0][\\\"generated_text\\\"][len(prompt):]\\nresult"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**输出**:\\n```\\nHere is a Python function that transforms bytes to Giga bytes:\\\\n\\\\n```python\\\\ndef bytes_to_giga_bytes(bytes):\\\\n    return bytes / 1024 / 1024 / 1024\\\\n```\\\\n\\\\nThis function takes a single\\n```\\n\\n很好，我们现在可以直接使用结果将字节转换为千兆字节。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def bytes_to_giga_bytes(bytes):\\n  return bytes / 1024 / 1024 / 1024"]}, {"cell_type": "markdown", "metadata": {}, "source": ["让我们调用 [`torch.cuda.max_memory_allocated`](https://pytorch.org/docs/stable/generated/torch.cuda.max_memory_allocated.html) 来测量峰值GPU内存分配。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bytes_to_giga_bytes(torch.cuda.max_memory_allocated())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**输出**:\\n```bash\\n29.0260648727417\\n```\\n\\n与我们的粗略计算非常接近！我们可以看到数字不完全正确，因为从字节到千字节需要乘以1024而不是1000。因此，粗略公式也可以理解为\\\"最多X GB\\\"的计算。\\n请注意，如果我们尝试以完整的 float32 精度运行模型，将需要惊人的64GB显存。\\n\\n> 如今几乎所有模型都以 bfloat16 训练，如果[您的GPU支持 bfloat16](https://discuss.pytorch.org/t/bfloat16-native-support/117155/5)，就没有理由以完整的 float32 精度运行模型。Float32 不会比用于训练模型的精度提供更好的推理结果。\\n\\n如果您不确定模型权重在Hub上以何种格式存储，您可以始终查看检查点配置中的 `\\\"torch_dtype\\\"`，*例如* [这里](https://huggingface.co/meta-llama/Llama-2-7b-hf/blob/6fdf2e60f86ff2481f2241aaee459f85b5b0bbb9/config.json#L21)。建议在使用 `from_pretrained(..., torch_dtype=...)` 加载时将模型设置为与配置中写入的相同精度类型，除非原始类型是 float32，在这种情况下可以使用 `float16` 或 `bfloat16` 进行推理。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["让我们定义一个 `flush(...)` 函数来释放所有分配的内存，以便我们可以准确测量峰值分配的GPU内存。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["del pipe\\ndel model\\n\\nimport gc\\nimport torch\\n\\ndef flush():\\n  gc.collect()\\n  torch.cuda.empty_cache()\\n  torch.cuda.reset_peak_memory_stats()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["flush()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 量化"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在，如果您的GPU没有32GB显存怎么办？已经发现模型权重可以量化为8位或4位，而不会显著降低性能（参见 [Dettmers et al.](https://huggingface.co/papers/2208.07339)）。\\n如最近的 [GPTQ论文](https://huggingface.co/papers/2210.17323) 所示，模型甚至可以量化为3位或2位，性能损失可接受 🤯。\\n\\n不深入太多细节，量化方案旨在降低权重的精度，同时尽量保持模型推理结果的准确性（*即*尽可能接近 bfloat16）。\\n请注意，量化对文本生成特别有效，因为我们关心的只是选择*最可能的下一个token集合*，并不真正关心下一个token *logit* 分布的确切值。\\n重要的是下一个token *logit* 分布保持大致相同，以便 `argmax` 或 `topk` 操作给出相同的结果。"]}], "metadata": {}, "nbformat": 4, "nbformat_minor": 4}
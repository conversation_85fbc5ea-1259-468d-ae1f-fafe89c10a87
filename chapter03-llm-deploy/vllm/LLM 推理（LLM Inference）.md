## LLM 推理

LLM 推理简单来说就是将训练好的大型语言模型投入实际应用。当你给模型一个**提示词（prompt）**时，模型会利用它学到的知识和参数，生成你想要的回答，比如文本、代码或者翻译。

这和模型训练不同。**训练**是一个一次性的、计算资源消耗巨大的过程，目的是让模型学习数据里的规律。而**推理**是反复进行的，通常是实时发生，就像你问 AI 助手问题时，模型就是通过推理来给你答案的。它会一个字一个字（或者叫“token”）地处理你的问题，然后根据它训练时学到的模式，预测接下来最可能的字或词，直到生成完整的回复。

### LLM 推理与训练有何不同？

LLM 训练和推理是模型生命周期中两个不同的阶段。

- **训练：构建模型理解能力**

  训练发生在 LLM 构建之初，旨在教会模型如何识别模式并进行准确预测。这通过让模型接触海量数据并根据数据调整参数来完成。

  LLM 训练中常用的技术包括：

  - **监督学习（Supervised learning）**：向模型展示输入和正确输出的例子。

  - **强化学习（Reinforcement learning）**：允许模型通过试错来学习，根据反馈或奖励进行优化。

  - **自监督学习（Self-supervised learning）**：通过预测数据中缺失或损坏的部分来学习，无需显式标签。

    训练是计算密集型的，通常需要大量的 GPU 或 TPU 集群。尽管这个初始成本可能非常高，但它或多或少是一次性开销。一旦模型达到所需的准确性，通常只需要定期重新训练以更新或改进模型。

- **推理：实时使用模型**

  LLM 推理是指将训练好的模型应用于新数据以进行预测。与训练不同，推理是持续进行的，实时响应用户输入或传入数据。这是模型实际“在使用中”的阶段。训练得越好、微调得越精细的模型通常能提供更准确、更有用的推理。

  推理的计算需求是持续的，并且可能变得非常高，尤其随着用户交互和流量的增长。每个推理请求都会消耗 GPU 等计算资源。虽然每个推理步骤可能比单独的训练要小，但随着时间的推移，累积的需求可能导致显著的运营开销。

### LLM 推理如何工作？

在推理过程中，LLM 会**一个词元（token）**一个词元地生成文本，利用其内部的注意力机制和对先前上下文的理解。

- 什么是词元和词元化？

  **词元（token）**是 LLM 用于处理文本的最小语言单位。根据词元分词器（tokenizer）的不同，它可以是一个词、一个子词，甚至一个字符。每个 LLM 都有自己的词元分词器，采用不同的词元化算法。**词元化（tokenization）**是将输入文本（如句子或段落）转换为词元的过程。然后，词元化的输入被转换为 ID，并在推理过程中传递给模型。

  以下是使用 GPT-4o 的词元分词器对句子“BentoML supports custom LLM inference.”进行词元化的示例：

  词元："B", "ento", "ML", " supports", " custom", " L", "LM", " inference", "."

  词元 ID：[33, 13969, 4123, 17203, 2602, 451, 19641, 91643, 13]

  对于输出，LLM 会**自回归地（autoregressively）**生成新的词元。模型从初始词元序列开始，根据它目前看到的所有内容预测下一个词元。这个过程会重复进行，直到满足停止条件。

#### LLM 推理的两个阶段

对于基于 Transformer 的模型（如 GPT-4），整个过程分解为两个阶段：**预填充（Prefill）和解码（Decode）**。

1. 预填充（Prefill）

   当用户发送查询时，LLM 的词元分词器会将提示转换为词元序列。词元化完成后，预填充阶段开始：

   - 这些词元（或词元 ID）被嵌入为 LLM 可以理解的数值向量。

   - 向量通过多个 Transformer 层，每层都包含自注意力机制。在这里，为每个词元计算查询（Q）、键（K）和值（V）向量。这些向量决定了词元之间如何相互关注，捕捉上下文含义。

   - 随着模型处理提示，它会构建一个 KV 缓存（KV cache），用于存储每个词元在每个层的键和值向量。它充当内部内存，以便在解码期间更快地查找。

     在预填充阶段，整个提示（即整个输入词元序列）在 LLM 开始实际计算之前就已经可用。这意味着 LLM 可以通过高度并行化的矩阵运算同时处理所有词元，特别是在注意力计算中。

     因此，预填充阶段是计算密集型（compute-bound）的，并且通常会使 GPU 利用率饱和。实际利用率取决于序列长度、批次大小和硬件规格等因素。

     预填充需要监控的一个关键指标是首词元时间（Time to First Token, TTFT），它衡量从提示提交到第一个词元生成之间的延迟。

2. 解码（Decode）

   预填充后，LLM 进入解码阶段，逐个生成新词元。

   - 对于每个新词元，模型从基于提示和所有先前生成的词元生成的概率分布中进行采样。这个过程是自回归的，这意味着词元 T₀ 到 Tₙ₋₁ 用于生成词元 Tₙ，然后是 T₀ 到 Tₙ 用于生成 Tₙ₊₁，依此类推。

   - 每个新生成的词元都会附加到不断增长的序列中。这种自回归循环会持续到：

     - 达到最大词元限制，
     - 生成停止词，
     - 或出现特殊的序列结束词元（例如，`<end>`）。

   - 最后，生成的词元序列被解码回人类可读的文本。

     与预填充相比，解码更内存密集型（memory-bound），因为它频繁地从不断增长的 KV 缓存中读取。KV 缓存将这些键和值矩阵存储在内存中，这样在后续词元生成期间，LLM 只需计算新词元的键和值，而无需从头开始重新计算所有内容。

     这种 KV 缓存机制通过避免冗余计算显著加快了推理速度。然而，它会以增加内存消耗为代价，因为缓存会随着生成序列的长度而增长。

     解码需要监控的一个关键指标是词元间延迟（Inter-token latency, ITL），即序列中连续词元生成之间的平均时间。它也称为每输出词元时间（Time Per Output Token, TPOT）。

#### 预填充和解码的协同

传统的 LLM 服务系统通常在同一硬件上运行预填充和解码阶段。然而，这种设置带来了几个挑战。

一个主要问题是预填充和解码阶段之间的干扰，因为它们不能完全并行运行。在生产环境中，可能会有多个请求同时到达，每个请求都有自己的预填充和解码阶段，这些阶段会在不同请求之间重叠。然而，一次只能运行一个阶段。当 GPU 被计算密集型预填充任务占用时，解码任务必须等待，从而增加了词元延迟，反之亦然。这使得为这两个阶段调度资源变得困难。

开源社区正在积极研究不同的策略来分离预填充和解码。

### LLM 生产部署的硬件选择：CPU、GPU 与 NPU

将大型语言模型（LLM）部署到生产环境时，选择合适的硬件至关重要。不同类型的硬件提供不同级别的性能和成本效率。三种主要选项是 **CPU**、**GPU** 和 **NPU**。了解它们的优缺点有助于您有效地优化推理工作负载。

#### CPU

**中央处理器（CPU）** 是所有计算机和服务器中使用的通用处理器。CPU 广泛可用，适用于运行小型模型或处理不频繁的请求。然而，它们缺乏并行处理能力来有效运行 LLM。对于生产级 LLM 推理，特别是对于大型模型或高请求量，CPU 在延迟和吞吐量方面往往不足。

#### GPU

**图形处理器（GPU）** 最初是为图形渲染和数字可视化任务设计的。由于它们可以执行高度并行的操作，因此也非常适合机器学习和人工智能工作负载。如今，GPU 已成为 LLM 等生成式 AI 模型训练和推理的默认选择。

GPU 的架构针对矩阵乘法和张量运算进行了优化，这些是基于 Transformer 的模型的核心组件。现代推理框架和运行时（例如 vLLM、SGLang、LMDeploy、TensorRT-LLM 和 Hugging Face TGI）旨在充分利用 GPU 加速。

#### NPU

**神经网络处理器（NPU）** 是专门为加速人工智能（AI）工作负载而设计的专用处理器。它们旨在高效执行神经网络操作，如矩阵乘法和卷积，这使得它们非常适合设备上的 AI 推理任务。

NPU 旨在提供高能效和低延迟的 AI 推理，特别是在边缘设备上。虽然它们通常不像高端 GPU 那样具有原始计算能力，但它们在特定 AI 推理任务中可以提供更好的能效和成本效益。随着 AI 模型变得越来越普遍，NPU 在智能手机、物联网设备和自动驾驶汽车等领域变得越来越重要。它们在处理大型 LLM 时可能不如 GPU 那样通用或强大，但在某些场景下，尤其是对功耗和尺寸有严格要求的场景，NPU 是一个有吸引力的选择。

### 无服务器与自托管 LLM 推理

在构建 LLM 应用时，您通常有两种主要的底层架构选择：**无服务器（托管）服务**或**自托管解决方案**。每种方案在易用性、定制化、可扩展性和合规性方面都具有独特的优势和权衡。

- 无服务器 LLM 推理

  由 OpenAI、Anthropic 和其他托管 API 提供商提供的无服务器推理服务，显著简化了应用开发。主要优势包括：

  - **易用性**：只需使用 API 密钥和几行代码即可快速开始，无需管理硬件、软件环境或复杂的扩展逻辑。
  - **快速原型开发**：非常适合快速测试想法、构建演示或内部工具，而无需基础设施开销。
  - **硬件抽象**：大规模自托管 LLM 通常需要高端 GPU（如 NVIDIA A100 或 H100）。无服务器 API 抽象了这些硬件的复杂性，让您能够避免 GPU 短缺、配额限制和供应延迟。

- 自托管 LLM 推理

  自托管 LLM 推理意味着部署和管理您自己的 LLM 基础设施。它提供了显著的控制和灵活性，这对于企业构建长期竞争优势至关重要。

  自托管的主要优势包括：

  - **数据隐私和合规性**：LLM 广泛用于现代应用，如 RAG 和 AI 代理。这些系统通常需要频繁访问敏感数据（例如客户详细信息、医疗记录、财务信息）。对于受监管行业中具有合规性和隐私要求的组织来说，这通常不是一个可接受的选择。自托管 LLM 可确保您的数据始终保留在您的安全环境中。
  - **高级定制和优化**：通过自托管，您可以根据特定需求调整推理过程，例如：
    - 精确调整延迟和吞吐量权衡。
    - 实施高级优化，如预填充-解码分离、前缀缓存、KV 缓存感知路由。
    - 针对长上下文或批处理场景进行优化。
    - 强制结构化解码以确保输出遵循严格的模式。
    - 使用专有数据微调模型以获得竞争优势。
  - **可预测的性能和控制**：当您自托管 LLM 时，您可以完全控制系统行为和性能。您不必受制于外部 API 速率限制或可能影响应用程序性能和可用性的突然政策变更。

选择无服务器与自托管 LLM 推理取决于您在易用性、数据隐私、性能优化和控制方面的具体需求。与自托管推理相比，无服务器模型 API 难以实现对性能调优和成本优化的精细控制。您只是调用与其他人相同的 API。要交付具有竞争优势的生产系统，您需要**拥有自己的推理层**。

------

## 开始使用 LLM 推理

### 选择合适的模型

第一步是确定哪种类型的模型适合您的用例。以下是 LLM 中常见模型类型的分类。

- **基础模型（Base models）**

  基础模型，也称为基础模型（foundation models），是大多数 LLM 的起点。它们通常通过无监督学习在大量文本语料库上进行训练，这不需要标记数据。

  在这个初始阶段，称为预训练（pretraining），模型学习通用的语言模式，例如语法、句法、语义和上下文。它能够预测下一个词（或词元），并且可以执行简单的少样本学习（few-shot learning）（在看到几个示例后处理任务）。然而，它尚未理解如何遵循指令，也未针对特定任务进行开箱即用的优化。

  为了使其有用，它们通常会使用**指令微调（instruction fine-tuning）**等技术在精选数据集上进行微调。

- **指令微调模型（Instruction-tuned models）**

  指令微调模型是在基础模型之上构建的。在初始预训练阶段之后，这些模型会通过使用由指令及其相应响应组成的数据集进行第二次训练。

  这个过程教会模型如何更可靠地遵循用户提示，从而使它们更好地与人类期望保持一致。它们理解任务意图并更连贯地响应命令，例如：

  - “总结这篇文章。”

  - “解释 LLM 推理是如何工作的。”

  - “列出远程工作的优缺点。”

    这使得它们在聊天机器人、虚拟助手和直接与用户交互的 AI 工具等实际应用中更加实用。

- **专家混合模型（Mixture of Experts, MoE models）**

  专家混合（MoE）模型，例如 DeepSeek-V3，与传统密集模型采用不同的方法。它们不为每个输入都使用所有模型参数，而是包含多个专门的子网络，称为专家（experts），每个专家专注于不同类型的数据或任务。

  在推理过程中，只有一部分专家会根据输入的特征被激活。这种选择机制使模型能够更选择性地路由计算——根据内容或上下文激活不同的专家。因此，MoE 模型通过在大型网络中分配工作负载，同时保持每推理计算成本可控，从而实现更大的可扩展性和效率。

### 将 LLM 与其他模型结合使用

现代 AI 应用程序很少只使用单个 LLM。许多高级系统依赖于将 LLM 与其他类型的模型组合，每种模型都专门用于不同的模态或任务。这使它们能够超越纯文本生成，变得更强大、多模态和任务感知。

以下是一些常见的示例：

- **小型语言模型（Small Language Models, SLMs）**。用于对延迟和资源限制重要的轻量级任务。它们可以充当备用模型或设备上的助手，处理基本交互而无需依赖完整的 LLM。
- **嵌入模型（Embedding models）**。它们将输入（例如文本、图像）转换为向量表示，使其可用于语义搜索、RAG 管道、推荐系统和聚类。
- **图像生成模型（Image generation models）**。像 Stable Diffusion 这样的模型根据文本提示生成图像。当与 LLM 结合使用时，它们可以支持更高级的文本到图像工作流，例如创意助手、内容生成器或多模态代理。
- **视觉语言模型（Vision language models, VLMs）**。像 NVLM 1.0 和 Qwen2.5-VL 这样的模型结合了视觉和文本理解，支持图像字幕、视觉问答或对屏幕截图和图表进行推理等任务。
- **文本到语音（Text-to-speech, TTS）模型**。它们可以将文本转换为听起来自然的语音。当与 LLM 集成时，它们可以用于基于语音的代理、可访问的界面或沉浸式体验。

### 计算用于服务 LLM 的 GPU 内存

如果您计划自托管 LLM，您需要弄清楚的第一件事就是它需要多少 GPU 内存（VRAM）。这主要取决于**模型的大小**和**推理过程中使用的精度**。

- **模型大小（参数数量）**。较大的模型需要更多内存。拥有数百亿甚至数千亿参数的模型通常需要高端 GPU，如 NVIDIA H100 或 H200。
- **位精度（Bit precision）**。使用的精度（例如 FP16、FP8、INT8）会影响内存消耗。较低的精度格式可以显著减少内存占用，但可能会降低准确性。

估算加载 LLM 所需内存的近似公式为：

内存 (GB)=P×(Q/8)×(1+Overhead)

其中：

- P：参数数量（以十亿为单位）
- Q：位精度（例如 16、32），除以 8 将位转换为字节
- Overhead(：推理过程中额外的内存或临时使用（例如 KV 缓存、激活缓冲区、优化器状态）

例如，加载一个 70B 模型（FP16 精度，20% 开销），大约需要 168 GB 的 GPU 内存：

内存=70×(16/8)×1.2=168 GB

### 选择合适的推理框架

一旦您选择了模型，下一步就是选择如何运行它。您选择的推理框架直接影响延迟、吞吐量、硬件效率和功能支持。没有一劳永逸的解决方案。您的决定取决于您的部署场景、用例和基础设施。

#### 推理框架

如果您正在构建高吞吐量、低延迟的应用程序（例如聊天机器人和 RAG 管道），这些框架已针对运行 LLM 推理进行了优化：

- **vLLM**：一个高性能推理引擎，针对服务 LLM 进行了优化。它以其高效的 GPU 资源利用和快速解码能力而闻名。
- **SGLang**：一个用于 LLM 和视觉语言模型的快速服务框架。它通过协同设计后端运行时和前端语言，使您与模型的交互更快、更可控。
- **LMDeploy**：一个专注于提供高解码速度和高效处理并发请求的推理后端。它支持各种量化技术，使其适用于部署内存需求减少的大型模型。
- **TensorRT-LLM**：一个推理后端，利用 NVIDIA 的 TensorRT，一个高性能深度学习推理库。它针对在 NVIDIA GPU 上运行大型模型进行了优化，提供快速推理并支持高级优化，如量化。
- **Hugging Face TGI**：一个用于部署和服务 LLM 的工具包。它在 Hugging Face 生产环境中使用，为 Hugging Chat、推理 API 和推理端点提供支持。

如果您正在使用有限的硬件或面向桌面/边缘设备，这些工具已针对低资源环境进行了优化：

- **llama.cpp**：一个轻量级 LLM 推理运行时，用纯 C/C++ 实现，没有外部依赖。其主要目标是使 LLM 推理快速、便携，并易于在各种硬件上运行。尽管名称如此，llama.cpp 支持的模型架构远不止 Llama，还支持许多流行的架构，如 Qwen、DeepSeek 和 Mistral。该工具非常适合低延迟推理，并在消费级 GPU 上表现良好。
- **MLC-LLM**：一个用于 LLM 的 ML 编译器和高性能部署引擎。它构建在 Apache TVM 之上，在服务模型之前需要进行编译和权重转换。MLC-LLM 可用于各种硬件平台，支持 AMD、NVIDIA、Apple 和 Intel GPU，以及 Linux、Windows、macOS、iOS、Android 和网络浏览器。
- **Ollama**：一个用户友好的本地推理工具，构建在 llama.cpp 之上。它设计简洁易用，非常适合在笔记本电脑上以最少的设置运行模型。然而，Ollama 主要用于单请求用例。与 vLLM 或 SGLang 等运行时不同，它不支持并发请求。这种差异很重要，因为许多推理优化（如分页注意力、KV 缓存和动态批处理）仅在并行处理多个请求时才有效。

#### 为什么您可能需要多个推理运行时？

在实际部署中，没有一个运行时是完美的适用于所有场景。这就是为什么 AI 团队通常最终会使用多个运行时：

- 不同的用例有不同的需求

  模型、硬件和工作负载各不相同。最佳性能通常来自将每个用例与针对该环境量身定制的运行时相匹配。

  - **高吞吐量，批处理**：vLLM、SGLang、LMDeploy、TensorRT-LLM（需要调优以获得更好性能）
  - **边缘/移动部署**：MLC-LLM、llama.cpp
  - **本地实验或单用户场景**：Ollama 和 llama.cpp

- 工具链和框架快速发展

  推理运行时不断更新。今天最好的工具下个月可能就缺少功能了。此外，一些模型在发布时仅在特定运行时中进行优化（或支持）。

  为了保持灵活性，您的基础设施应该与运行时无关。这使您可以结合每个工具的优点，而不会被锁定在单一堆栈中。

------

## OpenAI 兼容 API

一旦 LLM 开始运行，您就需要一种标准的方式来与其交互。这就是 OpenAI 兼容 API 的作用。

### 什么是 OpenAI 兼容 API？

OpenAI 兼容 API 是指任何复制 OpenAI 原始 API 的接口、请求/响应模式和身份验证模型的 API。尽管 OpenAI 没有正式将此定义为行业标准，但其 API 已成为 LLM 的事实接口。

ChatGPT 在 2022 年底的兴起展示了这种方法有多么强大和用户友好：

- 简洁、文档齐全的 API 使开发者能够轻松地使用 LLM 构建应用程序。

- 像 gpt-4o 这样的模型可以通过简单、一致的端点进行访问。

  因此，它在各行各业得到了快速采用和生态系统增长。

### 为什么兼容性很重要？

虽然 OpenAI 的 API 帮助启动了 AI 应用程序开发，但它们的广泛采用也造成了生态系统锁定。许多开发工具和框架现在只支持 OpenAI API 模式。切换模型或提供商通常需要重写应用程序逻辑的很大一部分。

OpenAI 兼容 API 通过提供以下功能来解决这些挑战：

- **即插即用替代**：无需更改应用程序代码，即可将 OpenAI 的托管 API 替换为您自己的自托管或开源模型。
- **无缝迁移**：以最小的中断在提供商或自托管部署之间移动。
- **一致性集成**：与依赖 OpenAI API 模式的工具和框架（例如 `chat/completions`、`embeddings` 端点）保持兼容。

### 如何调用 OpenAI 兼容 API？

以下是一个简单的示例，说明如何轻松地将现有 OpenAI 客户端指向自托管或替代提供商的端点：

```Python
from openai import OpenAI

# 使用您的自定义端点 URL 和 API 密钥
client = OpenAI(
    base_url="https://your-custom-endpoint.com/v1",
    api_key="your-api-key"
)

response = client.chat.completions.create(
    model="your-model-name",
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "How can I integrate OpenAI-compatible APIs?"}
    ]
)

print(response.choices[0].message)
```

如果您已经在使用 OpenAI 的 SDK 或 REST 接口，只需将它们重定向到您自己的 API 端点即可。这使您能够控制 LLM 部署，减少供应商锁定，并确保您的应用程序面向未来。

希望这次的整理能更清晰地帮助您理解 LLM 推理的方方面面！还有其他想了解或深入讨论的部分吗？

## LLM 推理的优化

### LLM 推理面临的挑战

尽管推理是 LLM 发挥作用的关键，但它也面临着一些实际的难题：

1. **高延迟**：LLM 处理用户输入通常是一个字一个字地来，这可能导致回复速度慢，尤其是在问题复杂或回复较长时。对于需要即时反馈的应用，比如聊天机器人或虚拟助手，这种延迟会是个大问题。
2. **计算成本高**：像 GPT-4 和 PaLM 2 这样的 LLM 有数十亿个参数，这意味着每次推理都需要巨大的计算能力，导致运营成本很高，特别是在大规模使用时。对于在面向客户的应用中部署 LLM 的企业来说，这些成本很快就会变得难以承受。
3. **内存限制**：推理过程中需要存储和访问大量的模型参数和中间数据。像手机、嵌入式设备这样内存有限的设备，通常很难处理大型模型，可能导致性能瓶颈甚至任务失败。
4. **词元长度限制**：许多 LLM 对单次输入能处理的字数（词元数量）有上限。如果输入太长，可能需要截断或分段处理，这会影响模型对上下文的理解，甚至降低表现。比如，翻译很长的文本时，可能需要截掉一部分，从而丢失关键信息，导致翻译不准确。
5. **工具不够成熟**：目前用于 LLM 推理的工具和框架还不够完善，缺乏部署大型模型所需的灵活性、稳定性和扩展性。主要问题包括：
   - **生态系统碎片化**：开发者经常需要把各种工具拼凑起来，才能完成 LLM 的服务、优化和监控，效率很低。
   - **缺乏统一标准**：部署和微调 LLM 没有统一的标准，导致操作复杂且不一致。
   - **兼容性差**：很多工具无法与最新的硬件加速器或模型架构无缝结合，阻碍了性能提升。
   - **调试和监控困难**：由于缺乏成熟的诊断工具，排查 LLM 推理过程中的问题通常很麻烦。
6. **准确性与“幻觉”**：虽然 LLM 能生成精妙且符合上下文的回复，但它们也可能产生**“幻觉”**——也就是编造出不准确或毫无意义的信息。这在医疗、法律或金融等对准确性要求极高的领域是个非常严重的问题。
7. **扩展性挑战**：在保持高性能的同时，处理成千上万甚至数百万个并发推理请求是一个巨大挑战。依赖 LLM 的应用必须高效地分配工作，以避免系统卡顿和用户体验下降。

### 优化指标

在探索优化技术之前，让我们先了解它们所针对的关键指标。评估 LLM 性能涉及使用各种工具，这些工具以不同方式定义、测量和计算这些指标。

### 延迟（Latency）

**延迟**衡量模型响应请求的速度。对于单个请求，延迟是从发送请求到用户端接收到最终词元的时间。它对于用户体验至关重要，尤其是在实时应用中。

衡量延迟有两个关键指标：

- **首词元时间（Time to First Token, TTFT）**：从发送请求到生成第一个词元所需的时间。它反映了模型开始响应的速度。不同的应用程序通常对 TTFT 有不同的期望。例如，在总结长文档时，用户通常愿意等待更长时间来获得第一个词元，因为任务要求更高。
- **每输出词元时间（Time per Output Token, TPOT）**：也称为**词元间延迟（inter-token latency, ITL）**，TPOT 衡量生成每个后续词元之间的时间。较低的 TPOT 意味着模型可以更快地生成词元，从而导致更高的每秒词元数。在用户逐字看到文本出现（如 ChatGPT 界面）的流式传输场景中，TPOT 决定了体验的流畅程度。它应该足够快，以跟上人类阅读速度。

### 吞吐量（Throughput）

**吞吐量**描述了 LLM 在给定时间内可以完成的工作量。当同时服务许多用户或处理大量数据时，高吞吐量至关重要。

衡量吞吐量有两种常用方法：

- 每秒请求数（Requests per Second, RPS）：此指标衡量 LLM 每秒可以成功完成的请求数。其计算公式为：

  每秒请求数=(T1−T2)已完成请求总数

  这里，T1 和 T2 标记时间窗口（以秒为单位）。

  RPS 提供了 LLM 处理并发请求能力的总体概念。然而，仅凭此指标无法捕捉每个请求的复杂性或大小。例如，生成一个简短的问候语（如“你好！”）远不如撰写一篇长文章要求高。

  影响 RPS 的因素：

  - 提示的复杂性和长度
  - 模型大小和硬件规格
  - 优化（例如，批处理、缓存、推理引擎）
  - 每个请求的延迟

- **每秒词元数（Tokens per Second, TPS）**：此指标通过衡量所有活动请求每秒处理的词元数来提供更细致的吞吐量视图。它有两种形式：

  - **输入 TPS**：模型每秒处理的输入词元数。

  - 输出 TPS：模型每秒生成的输出词元数。

    了解这两个指标有助于您根据推理工作负载的性质识别性能瓶颈。例如：

  - 包含长文档（例如 2,000 个词元输入）的摘要请求更关注输入 TPS。

  - 从短提示生成长回复（例如 20 个词元提示 → 500 个词元回复）的聊天机器人严重依赖输出 TPS。

    在查看基准测试或评估 LLM 性能时，务必检查 TPS 指标是指输入、输出还是组合视图。它们根据用例突出显示不同的优势和局限性。

    影响 TPS 的因素：

  - 批次大小（较大的批次可以增加 TPS 直到饱和）

  - KV 缓存效率和内存使用

  - 提示长度和生成长度

  - GPU 内存带宽和计算利用率

    随着并发请求数量的增加，总 TPS 也会增长，直到 LLM 达到可用计算资源的饱和点。超过此点，性能可能会下降，因为 LLM 已超负荷。

### Goodput

**Goodput** 完善了吞吐量的概念。它衡量 LLM 在满足您定义的服务水平目标（SLOs）的同时，每秒成功完成的请求数。这使其成为实际部署中更有用的指标，因为它直接反映了服务质量。

> **服务水平目标（Service-Level Objective, SLO）**定义了特定指标的目标性能水平。它为可接受的服务设定了标准。例如，聊天机器人的 TTFT SLO 可能规定 95% 的交互 TTFT 应低于 200 毫秒。SLO 通常是服务提供商与其用户之间更广泛的服务水平协议（SLA）的关键部分。

为什么 Goodput 很重要？高吞吐量并不总是意味着良好的用户体验。如果未达到延迟目标，许多请求可能无法使用。Goodput 是 LLM 服务系统在延迟约束下满足性能和用户体验目标的直接衡量标准。它有助于避免为了最大化吞吐量而牺牲实际用户体验和成本效率的陷阱。

### 延迟与吞吐量的权衡

在托管和优化 LLM 推理时，始终需要在两个关键目标之间取得平衡：最小化延迟和最大化吞吐量。

| 目标             | 含义                                                         | 影响 |
| ---------------- | ------------------------------------------------------------ | ---- |
| **最大化吞吐量** | 专注于尽可能多地服务每瓦词元数。这通常意味着使用更大的批次大小和共享计算资源。然而，这可能会减慢单个用户的响应速度。 |      |
| **最小化延迟**   | 专注于为每个用户提供快速响应（低 TTFT）。这通常涉及小批次和隔离计算资源，但这意味着您将更低效地使用 GPU。 |      |
| **两者平衡**     | 某些系统旨在实现动态平衡。它们根据工作负载、用户优先级和应用程序要求实时调整资源使用。这对于服务具有不同 SLO 的多样化应用程序是理想选择。 |      |

为了达到您用例的最佳权衡，您需要调整几个重要的系统级“旋钮”，例如数据并行（Data Parallelism, DP）、张量并行（Tensor Parallelism, TP）、专家并行（Expert Parallelism, EP）、批次大小（batch size）、精度（precision）（例如 FP8、FP4）和解耦（disaggregation）（分离预填充和解码）。这些调优选项直接影响您在低延迟或高吞吐量之间进行优化的能力，或者找到合适的中间地带。

使用无服务器 API 可以抽象这些优化，让您对微调的控制更少。另一方面，构建您自己的可编程和低级堆栈，可以帮助您驾驭这些权衡，并使您的系统性能与您应用程序的特定 SLO 保持一致。

### 优化 LLM 推理的解决方案

针对 LLM 推理的这些挑战，业界涌现出了一系列旨在提高效率、降低成本和确保可靠性的创新方案：

### 模型优化

通过优化 LLM 本身的结构和行为，可以在不牺牲性能的前提下显著提高推理效率。主要技术包括：

- **模型剪枝（Pruning）**：移除模型中不太重要的参数，从而减小模型体积，使其运行更快、更高效。
- **模型量化（Quantization）**：降低模型参数的数值精度（例如，从 32 位浮点数降到 8 位整数），以此减少计算量。
- **知识蒸馏（Knowledge Distillation）**：训练一个较小的**“学生”模型**去模仿一个更大、更复杂**“教师”模型**的行为，这样就能得到一个更紧凑、适合推理的模型。

### 硬件加速

现代硬件利用并行处理和专用架构来加速推理。**GPU**、**TPU** 以及新型的 **AI 专用加速器**，都专门针对 LLM 所需的高维计算进行了优化。例如，Nvidia 最新的 GPU 集成了 Tensor Cores，能大幅加速矩阵运算——这是 LLM 推理的核心。而像 Cerebras Systems 的晶圆级引擎这样的专用加速器，更是专门为处理 LLM 所需的大规模并行计算而设计。

### 推理技术

创新的推理方法可以提升处理能力和效率：

- **KV 缓存（KV Caching）**：在生成每个字（词元）时，存储中间的计算结果，减少重复计算，从而加速后续的预测。
- **批处理（Batching）**：将多个推理请求打包在一起同时处理，这样可以更充分地利用硬件资源，减少单个请求的延迟。
- **推测解码（Speculative Decoding）**：让一个更小、更快的模型先生成初步预测，然后由主 LLM 进行验证，从而加速整个生成过程。

### 软件优化

优化的软件框架，比如 TensorFlow Serving 或 ONNX Runtime，通过更有效地管理资源来提高推理性能。这些平台实现了动态批处理和自动扩缩等功能，以适应不断变化的工作负载。

### 高效注意力机制

研究人员正在开发更高效的注意力机制，以降低处理长输入时的计算成本。这包括：

- **稀疏注意力（Sparse Attention）**：只将注意力集中在输入中的一部分关键词元上，而不是全部。
- **线性化注意力（Linearized Attention）**：用线性复杂度的方法来近似注意力机制。
- **Flash Attention**：优化注意力计算过程，以便在 GPU 上运行得更快。
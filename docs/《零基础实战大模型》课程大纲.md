# 《零基础实战大模型》课程大纲

## 一、选题背景与市场分析

- **市场需求激增**：大模型（LLM）已成为AI领域核心驱动力，企业对大模型应用开发、部署、微调等能力需求激增，相关岗位薪资普遍较高
- **技能缺口明显**：当前缺乏系统、实战导向、面向零基础的中文大模型开发课程，市面上多为理论课程或碎片化教程
- **课程定位**：本课程聚焦“从零到一”大模型开发，理论与实战并重，覆盖主流技术栈与企业级应用场景，帮助学员快速获得就业竞争力

## 二、目标学员

**主要学员群体**：
- 🎓 **AI开发初学者**：计算机相关专业学生，希望进入AI领域
- 💼 **企业技术团队**：需要快速掌握大模型技术的开发团队
- 🔄 **转型工程师**：传统软件开发者希望转向AI方向
- 📈 **创业者/产品经理**：需要了解大模型技术实现的业务人员

**学员基础要求**：
- 具备基础Python编程能力
- 了解基本的机器学习概念（非必需）
- 有Linux基础操作经验（非必需）

## 三、课程特色

- 🚀 **零基础友好**：从环境搭建到企业级应用，循序渐进，每个概念都有详细解释
- 💻 **实战驱动**：每模块配套真实项目，学完即可上手，提供完整代码和部署方案
- 🔥 **技术前沿**：涵盖vLLM、RAG、Agent、MCP等2024年最新技术栈
- 🏢 **企业级落地**：对接真实业务场景，提供可直接商用的解决方案
- 📊 **成果可见**：每个模块都有可演示的成果，便于求职展示

## 四、课程模块与核心内容

| 模块 | 主题 | 核心技术栈 | 实战项目 | 商业价值 |
|------|------|------------|----------|----------|
| 1 | 大模型开发入门与环境搭建 | Python、GPU、Docker、API调用 | 🔧 个人AI开发环境搭建 | 掌握开发基础，为后续学习铺路 |
| 2 | 基础推理与提示词工程 | Prompt Engineering、Function Calling | 🤖 智能客服提示词系统 | 可直接应用于客服、内容生成等场景 |
| 3 | 高性能推理与压测 | vLLM、Ollama、性能监控 | ⚡ 高并发推理服务部署 | 企业级部署必备技能，降低推理成本 |
| 4 | MCP协议与AI工具集成 | MCP协议、JSON-RPC、工具开发 | 🌤️ 智能天气助手系统 | 掌握AI工具集成，可扩展到各种业务工具 |
| 5 | RAG检索增强生成 | 向量数据库、LangChain、Embedding | 📚 企业知识库问答系统 | 解决企业内部知识管理痛点 |
| 6 | 大模型Agent实战 | LangChain Agents、AutoGen、Agno | 🧳 AI旅行规划助手 | 多Agent协作，可应用于复杂业务流程 |
| 7 | n8n自动化与AI集成 | n8n、工作流自动化、API集成 | 🔄 内容审核自动化系统 | 提升业务效率，减少人工成本 |
| 8 | 高效微调与部署 | LoRA、Q-LoRA、LlamaFactory | ⚖️ 法律咨询模型微调 | 领域模型定制，提升专业能力 |

## 五、核心实战项目详解

### 🎯 项目驱动学习路径
每个模块都包含一个完整的实战项目，从需求分析到部署上线：

1. **🔧 个人AI开发环境** - 一键部署脚本，支持GPU加速
2. **🤖 智能客服系统** - 基于提示词工程的客服机器人
3. **⚡ 高性能推理服务** - 支持千级并发的推理API
4. **🌤️ 智能天气助手** - MCP协议标准化工具集成
5. **📚 企业知识库** - 支持多格式文档的RAG问答系统
6. **🧳 AI旅行助手** - 多Agent协作的智能规划系统
7. **🔄 自动化工作流** - 零代码AI业务流程自动化
8. **⚖️ 专业领域模型** - 法律咨询模型的完整微调流程

### 💼 商业应用价值
- **直接商用**：所有项目都可直接应用于实际业务场景
- **技术栈完整**：覆盖从开发到部署的完整技术链路
- **成本优化**：教授如何降低推理成本，提升系统性能
- **可扩展性**：提供标准化的架构设计，便于后续扩展

## 六、课程优势与差异化竞争力

### 🎯 核心优势
- **📈 系统性强**：覆盖大模型开发全流程，从环境搭建到企业部署的完整链路
- **💼 产业导向**：所有项目均来自真实企业需求，直接对接就业市场
- **🚀 技术前沿**：引入2024年最新的MCP、RAG、Agent等技术，保持技术领先性
- **🛠️ 资源丰富**：提供完整代码库、数据集、部署脚本和Docker镜像
- **🔄 持续更新**：课程内容根据行业发展持续迭代，保证技术时效性

### 💡 差异化特色
- **零代码门槛**：通过n8n等工具，让非技术人员也能构建AI应用
- **成本优化导向**：重点教授如何降低推理成本，提升商业可行性
- **标准化架构**：提供企业级的架构设计模式，便于团队协作
- **多模态支持**：不仅限于文本，还涵盖图像、语音等多模态应用

### 📊 市场可行性分析
- **需求旺盛**：大模型相关岗位需求增长300%+，薪资普遍高于传统开发岗位
- **技能稀缺**：市场上缺乏系统性的大模型开发人才
- **应用广泛**：从客服到内容创作，从数据分析到自动化，应用场景丰富
- **投资回报高**：学员完成课程后，可直接承接相关项目或获得高薪就业机会

---

# 附录：详细课程大纲

## 模块一：大模型实战入门与环境搭建

### 📋 模块概述

本模块是整个课程的基石，将带领学员从零开始构建完整的大模型开发环境。不仅涵盖理论基础，更重要的是通过实际操作，让学员快速上手大模型开发。我们将提供一键部署脚本和Docker镜像，确保每位学员都能成功搭建开发环境。

### 🎯 学习目标

**知识目标：**
- 🧠 深入理解大模型的核心概念、技术原理和商业价值
- 📊 掌握主流大模型的特点、性能对比和选择标准
- 🔧 理解大模型开发的技术栈和工具链

**技能目标：**
- ⚙️ 熟练搭建Python开发环境、GPU加速环境和Docker容器
- 💻 掌握Cursor等AI编程工具，提升开发效率
- 🔌 能够调用各种大模型API，实现基础的文本生成和处理
- 🐛 具备基础的调试和问题排查能力

**实战目标：**
- 🚀 搭建一个完整的个人AI开发环境
- 🤖 开发一个简单的AI助手原型
- 📈 完成第一个可演示的大模型应用

### 📚 详细内容

#### 1. 大模型技术全景解析 (2小时)
**理论基础：**
- 🧠 **大模型核心概念**：从GPT到ChatGPT的技术演进
- 🏗️ **Transformer架构深度解析**：注意力机制、编码器-解码器结构
- 📊 **主流大模型对比分析**：
  - OpenAI GPT系列 (GPT-4o, GPT-4o-mini)
  - Anthropic Claude系列 (Claude-3.5-Sonnet)
  - 国产大模型 (Qwen2.5, DeepSeek-V3, GLM-4)
  - 开源模型 (Llama3.1, Mistral)

**商业应用分析：**
- 💼 **企业应用场景**：客服、内容创作、代码生成、数据分析
- 💰 **成本效益分析**：API调用成本 vs 自部署成本
- 📈 **行业发展趋势**：多模态、Agent、垂直领域应用

#### 2. 开发环境一键部署 (3小时)
**环境搭建实战：**
- 🐍 **Python环境管理**：
  - Conda环境创建与管理
  - 虚拟环境最佳实践
  - 依赖包管理和版本控制
- 🚀 **GPU加速环境**：
  - NVIDIA驱动安装与验证
  - CUDA Toolkit配置
  - PyTorch GPU支持测试
- 🐳 **Docker容器化部署**：
  - 提供预配置的Docker镜像
  - 容器化开发环境的优势
  - 一键启动脚本

**开发工具配置：**
- 💻 **Cursor AI编程工具**：
  - 安装配置与基础使用
  - AI辅助编程技巧
  - 代码补全和重构功能
- 🔧 **开发辅助工具**：
  - Git版本控制
  - Jupyter Notebook配置
  - 远程开发环境搭建

#### 3. 大模型API实战调用 (3小时)
**API调用基础：**
- 🔑 **API密钥管理**：
  - 安全存储和使用最佳实践
  - 环境变量配置
  - 密钥轮换策略
- 🌐 **HTTP请求实战**：
  - 使用curl命令行调用
  - Python requests库封装
  - 异步请求处理

**多平台API集成：**
- 🤖 **OpenAI API**：GPT-4o调用实战
- 🇨🇳 **国产API**：DeepSeek、Qwen、GLM调用
- 📊 **响应处理**：JSON解析、错误处理、重试机制

**实践练习：**

1. **实践一：配置本地大模型开发环境**
   - 任务：在Ubuntu环境上成功配置Anaconda/Miniconda、Python环境，并安装必要的库。
   - 任务：检查并确保GPU驱动和CUDA Toolkit安装正确，能被PyTorch等框架识别。
   - 任务：初始化一个Git仓库，并将其推送到GitHub。
2. **实践二：调用主流大模型API**
   - 任务：申请一个OpenAI API Key（或其他国内主流大模型API Key）。
   - 任务：使用Cursor编写Python代码，成功调用API完成以下至少两种文本生成任务：
     - 一段关于“未来科技”的短文。
     - 将一段英文翻译成中文。
     - 对一篇新闻文章进行摘要。

------

## 模块二：大模型基础推理与提示词工程

说明：

本模块深入探讨大模型的核心推理机制和提示词工程的艺术。学员将理解大模型如何生成文本，掌握各种解码策略，并学习如何设计高效、精准的提示词，以最大化模型的性能。重点将放在**思维链（CoT）、自我反思（Self-Reflection）等高级技巧，以及函数调用（Function Calling）**的实战应用，为构建复杂AI应用奠定基础。

**学习目标：**

- 理解大模型文本生成的基本原理和解码策略。
- 掌握基础提示词设计原则，能够有效引导模型完成任务。
- 熟练运用CoT、自我反思等高级提示词技巧。
- 理解并实践大模型函数调用（Function Calling）机制，实现与外部工具的交互。

**详细内容：**

1. **大模型推理基础**
   - 文本生成原理概述：自回归生成、条件生成。
   - **Tokenization**与**Embedding**。
   - 解码策略：**贪婪解码、束搜索、Top-k、Top-p、温度参数 (temperature)**。
2. **提示词工程 (Prompt Engineering) 核心概念**
   - 什么是提示词工程？为什么重要？
   - 基础提示词设计原则：**清晰、具体、角色设定、格式化输出要求**。
   - **Few-shot prompting (少样本学习)** 与 **Zero-shot prompting (零样本学习)**。
3. **高阶提示词技巧**
   - **思维链 (Chain of Thought, CoT)**：通过分解步骤引导模型逐步推理。
   - **自我反思 (Self-Reflection)**：让模型自我评估和改进输出。
   - **提示模板设计**：Jinja2模板引擎、LangChain Prompt Templates。
   - **动态Prompt生成**：根据用户输入或上下文动态构造Prompt。
   - **提示词管理**：提示词的创建、管理、版本控制、优化和评估功能
4. **大模型调用方式与函数调用 (Function Calling)**
   - **OpenAI Function Calling 原理与实战**：
     - 使用**JSON Schema**定义工具参数结构。
     - **Tool Call + Response Chain** 流程。
     - 示例场景：天气查询、数据库查询、API 调用。
4. **从零开始构建高效的提示词**
   - 少量示例法
   - 问题分解法
   - 自我批评法
   - 上下文增强法
   - 集成提示法
   - 设定角色法
   
**实践练习：**

1. **实践一：高级提示词设计**
   - 任务：针对同一任务（例如：为一篇电商产品描述润色），分别使用以下提示词技巧进行生成，并对比结果：
     - 直接提问（零样本）。
     - 提供2-3个示例（少样本）。
     - 引导模型逐步思考（思维链）。
     - 让模型生成后进行自我评估和改进（自我反思）。
2. **实践二：Function Calling 实战**
   - 任务：构建一个简单的天气查询机器人，利用Function Calling调用一个虚拟的或真实的**第三方天气API**（如和风天气API，仅需模拟请求响应即可）。
   - 要求：用户输入“明天上海天气如何？”时，模型能识别意图并调用天气工具，返回模拟的天气数据。

------

## 模块三：大模型推理与评估优化

说明：

本模块聚焦大模型在生产环境中的高性能推理与部署(如Transformer、vLLM、Ollama)。学员将理解大模型推理的性能瓶颈，学习如何利用vLLM等先进推理框架提升吞吐量和降低延迟。通过实际部署开源大模型并进行压测，学员将掌握性能评估、优化以及不同私有化部署方案的对比，为企业级应用落地提供技术支撑。

**学习目标：**

- 理解大模型推理的性能挑战及优化策略。
- 掌握vLLM的原理、安装与高性能推理实践。
- 学会使用压测工具评估大模型服务性能指标。
- 能够对比并选择合适的本地/私有化部署方案。

**详细内容：**

1. **大模型推理性能优化挑战**
   - 显存占用、推理延迟、吞吐量瓶颈分析。
   - **批处理 (Batching)** 与 **KV Cache** (Paged Attention原理)。
2. **vLLM：高性能大模型推理库**
   - vLLM核心原理与优势：**Paged Attention**、连续批处理。
   - vLLM安装与配置：**pip安装**、Docker部署。
   - 支持的模型与硬件要求：**NVIDIA GPU (非硬性)**。
3. **vLLM模型推理与压测**
   - vLLM RESTful API部署与调用。
   - vLLM模型压测：TTFT、吞吐量、并发数（FIXME）
   - 性能监控与分析：**NVIDIA-SMI** (GPU利用率、显存占用)。
4. **本地/私有化部署调用方式对比**
   - **HuggingFace Transformers (pipeline)**：易用性、灵E活性。
   - **TGI (Text Generation Inference)**：HuggingFace官方推荐的推理服务。
   - **Ollama**：本地LLM一站式解决方案，轻量易用。

**实践练习：**

1. **实践一：vLLM本地部署与推理**
   - 任务：在本地成功部署并运行一个基于vLLM的**Llama 3 8B Instruct**或**Qwen-1.5-7B-Chat**模型推理服务。
   - 任务：通过Python客户端调用vLLM的API，生成一段指定长度的中文文本，并记录推理时间。
2. **实践二：vLLM服务压测**
   - 任务：使用对部署的vLLM服务进行压测。
   - 要求：设计不同并发用户数（例如：1, 5, 10, 20）下的测试场景，记录并分析**QPS (Queries Per Second)** 和平均**延迟**，并尝试找出性能瓶颈。

------

## 模块四：模型上下文协议（MCP）实战

说明：

本模块专注于Anthropic开发的模型上下文协议（Model Context Protocol, MCP），这是一个革命性的标准协议，用于在AI应用程序与其数据源之间建立安全、标准化的连接。学员将学习MCP的核心概念、架构设计，掌握如何通过MCP构建可扩展的AI工具生态系统，实现大模型与外部数据源的高效交互。

**学习目标：**

- 理解MCP协议的设计理念和解决的核心问题
- 掌握MCP服务器和客户端的开发与部署
- 学会构建标准化的AI工具和数据连接器
- 能够设计和实现企业级MCP解决方案
- 理解MCP在AI Agent系统中的重要作用

**详细内容：**

1. **模型上下文协议 (Model Context Protocol, MCP) 核心概念**
   - MCP背景与重要性：统一AI应用与数据源连接的标准协议
   - MCP解决的核心问题：**数据孤岛、重复开发、安全性、可维护性**
   - MCP架构设计：**客户端-服务器模式**、**标准化通信协议**
   - MCP与传统API集成方式的对比：**标准化 vs 定制化**

2. **MCP协议规范与实现**
   - MCP协议规范详解：**JSON-RPC 2.0通信协议**
   - MCP核心概念：**Resources（资源）**、**Tools（工具）**、**Prompts（提示词模板）**
   - MCP服务器开发：**Python MCP SDK**、**TypeScript MCP SDK**
   - MCP客户端集成：**Claude Desktop**、**其他AI应用程序**

3. **MCP实践应用场景**
   - 企业数据集成：**数据库连接器**、**文件系统访问**、**API代理**
   - 开发工具集成：**Git操作**、**代码分析**、**项目管理**
   - 业务系统连接：**CRM系统**、**ERP系统**、**知识库**
   - 安全与权限控制：**访问控制**、**数据脱敏**、**审计日志**
   - 如何将检索到的信息有效组织并传递给大模型：**Prompt Template中嵌入检索结果**。


**MCP实践练习：**

1. **实践一：MCP入门 - 让AI模型获取实时天气信息**
   - 任务：构建一个简单的MCP服务器，提供天气查询工具（Tool）
   - 要求：实现基础的天气API调用功能，让AI能够获取指定城市的实时天气信息
   - 技术栈：Python MCP SDK + OpenWeatherMap API

2. **实践二：MCP进阶 - 集成DeepSeek模型与MCP的天气信息助手**
   - 任务：在实践一基础上，集成DeepSeek模型，构建完整的天气助手对话系统
   - 要求：实现MCP客户端与DeepSeek模型的集成，支持自然语言天气查询对话
   - 技术栈：MCP Client + DeepSeek API + 天气MCP服务器

3. **实践三：MCP高阶 - 借助LangChain快速打造MCP天气助手**
   - 任务：使用LangChain框架重构天气助手，实现更复杂的对话流程和工具调用
   - 要求：集成LangChain的Agent机制，支持多轮对话、上下文记忆和智能工具选择
   - 技术栈：LangChain + MCP Protocol + 多种天气相关工具


---

## 模块五：RAG（检索增强生成）实战

说明：

本模块深入讲解检索增强生成（RAG）技术，它是解决大模型知识截止和幻觉问题的关键。学员将学习RAG的核心原理、架构，掌握向量数据库和Embedding模型的使用。通过LangChain框架，实现从文档加载到向量存储、检索、重排序和生成全流程。同时，引入模型上下文协议（MCP），优化大模型对检索结果的利用，最终实战构建一个企业内部技术文档智能问答系统。

**学习目标：**

- 理解RAG的原理与价值，解决大模型知识边界问题。
- 掌握向量数据库（Milvus和Qdrant）和Embedding模型的使用。
- 熟练运用LangChain构建RAG管道。
- 理解并实践模型上下文协议（MCP），优化大模型上下文利用效率。
- 能够构建基于企业私有知识库的智能问答系统。

**详细内容：**

1. **RAG (Retrieval Augmented Generation) 核心原理**
   - 传统大模型局限性：知识截止、幻觉问题。
   - RAG如何解决这些问题：外部知识引入。
   - RAG架构与工作流程：**文档加载 → 分块 → Embedding → 存储 → 检索 → 重排序 → 生成**。
2. **向量数据库与Embedding模型**
   - 向量数据库介绍：Milvus、Qdrant。
   - Embedding模型选择与使用：**OpenAI text-embedding-3-large/small, BGE-large-zh-v1.5, E5**。
3. **LangChain全流程实践**
   - **LangChain 核心组件详解**：
     - **Chains (LLMChain, RetrievalQAChain)**：基本流程封装。
     - **Retrievers**：向量相似度检索、BM25稀疏检索。
     - **Memory (ConversationBufferWindowMemory)**：短期对话记忆。
   - **文档加载与向量化**：支持多种格式（如PDF、Markdown、网页等），分块后进行Embedding并存储到向量数据库。
   - **检索与生成**：基于用户问题检索相关文档片段，并结合大模型生成最终答案。
4. **RAG与MCP结合实践**
   - 背景与重要性：RAG流程中，检索到的文档片段往往结构松散、信息分散，直接拼接进Prompt容易导致大模型理解困难或上下文利用效率低下。MCP的引入，能够以结构化、标准化的方式组织检索结果，极大提升大模型的推理效果和答案准确性。
   - MCP设计思想：**结构化、可扩展的上下文组织**，如将检索到的文档片段以Markdown表格、列表、特定XML/JSON格式等方式嵌入Prompt，便于大模型高效解析和引用。
   - 实践方法：
     - 在RAG流程中，检索到的多个文档片段，先进行去重、重排序和摘要处理。
     - 采用MCP协议，将这些片段以结构化格式（如Markdown表格、编号列表、带标签的JSON等）组织起来，插入到Prompt模板的指定位置。
     - 通过LangChain的PromptTemplate等工具，将结构化检索结果与用户问题一同输入大模型，提升复杂问题的回答准确性和上下文连贯性。
   - 典型应用场景：如企业知识库问答、技术文档检索、合规性分析等，MCP能显著提升RAG系统的专业性和可扩展性。

**实践练习：**

1. **实践一：构建企业内部技术文档RAG**
   - 任务：收集并处理一份企业内部技术文档（如Markdown格式的API文档或开发规范，可自拟或使用开源文档）。
   - 任务：使用**LangChain**，选择一个合适的Embedding模型（如**BGE-large-zh-v1.5**），将文档分块并向量化，存储到**ChromaDB**中。
   - 任务：实现基于该知识库的RAG问答功能，用户输入问题后，系统能检索相关文档片段并生成回答。
2. **实践二：MCP优化RAG上下文**
   - 任务：在实践一的基础上，设计一个简单的MCP方案，例如，将检索到的文档片段以Markdown列表或表格的形式嵌入到Prompt中。
   - 任务：对比使用MCP前后，大模型在回答复杂技术问题（需要结合多个文档片段）时的准确性和连贯性。

------

## 模块六：大模型Agent 实战

说明：

本模块将聚焦大模型Agent的构建与协作，学员将学习Agent的核心概念、组件及主流框架。通过LangChain Agents、AutoGen和Ango的深度实践，掌握如何设计工具、规划任务以及实现多Agent之间的协同。最终，通过构建一个智慧园区通行管理助理，学员将掌握如何在企业实际场景中，利用Agent实现复杂任务的自动化和流程优化。

**学习目标：**

- 理解大模型Agent的原理、组成要素及应用场景。
- 掌握LangChain Agents中ReAct、Plan-and-Execute等Agent类型及工具集成。
- 掌握 Agno 框架的核心概念与使用方法，理解其在自主智能体构建中的优势。
- 熟练运用AutoGen框架实现多Agent协作，包括角色定义、群聊模式与辩论机制。
- 具备设计Agent状态机和多轮对话逻辑的能力。
- 能够构建企业级多Agent协作系统，解决实际业务问题。

**详细内容：**

1. **大模型Agent概念与架构**
   - 什么是Agent？为什么需要Agent？**自主性、响应性、社会性、能动性**。
   - Agent组成要素：**LLM、Memory、Tools、Planning**。
   - 主流Agent框架介绍：**LangChain Agents、AutoGPT (概念)、AutoGen**。
2. **LangChain Agents 深度实践**
   - **Agents 类型**：
     - **ReAct Agent (ZeroShotReactDescriptionAgent)**：推理+行动循环。
     - **Plan-and-Execute Agent**：先规划再执行。
   - **Tools**：集成自定义工具 (如HTTP请求工具、数据库查询工具、Python代码执行工具)。
3. **AutoGen 多 Agent 协作框架**
   - Agent 角色定义：**UserProxyAgent、AssistantAgent、GroupChatManager**。
   - **Group Chat模式**：多Agent轮流发言、达成共识。
   - **Debate机制**：Agent间进行辩论以提升决策质量。
   - 通信机制：**Message Passing、工具调用、反馈循环**。
4. **Agno 框架入门与实战**
   - Agno 的设计哲学：面向自主智能体的开发，强调规划与执行。
   - Agno 的核心组件：智能体、工具、任务、环境。
   - Agno 的优势：高性能、模型无关性、原生多模态、内置 RAG/记忆。
   - Agno 环境搭建与基本使用：
   - **Agno 智能体间的协作模式：**
     - 单智能体任务执行。
     - 多智能体协同工作原理（`agno.team.Team`，任务分解、结果汇总）。
5. **Agent 构建与多轮对话逻辑设计**
   - 如何设计一个 Agent 的状态机？
   - 多轮对话中如何保持上下文？
   - 如何设计 Agent 之间的消息传递协议？

**实践练习：**

1. **实践一：基于LangChain的单Agent工具调用**
   - 任务：构建一个简单的单Agent，使其能够通过调用一个工具来完成任务。例如：
     - **网页内容摘要Agent**：利用LangChain的`requests_tool`或`PlaywrightBrowser`工具，接收URL，访问网页并生成摘要。
     - **天气查询Agent**：集成在模块二中实现的函数调用工具，处理更复杂的意图和多轮对话。
2. **实践三：基于 Agno 的 AI 旅行助手**
   - **任务：** 使用 Agno 框架，设计并实现一个 AI 旅行助手的最小原型，展示其研究和规划能力。
   - **系统组件：**
     - **研究员智能体 (Researcher Agent)：**
       - **职责：** 负责根据用户偏好搜索旅行目的地、活动和住宿信息。
       - **工具：** 使用 **SerpAPI**（或模拟其功能）进行网络搜索，获取最新旅行资讯、景点评价、酒店价格等。
       - **输出：** 分析搜索结果并返回最相关的信息摘要。
     - **规划师智能体 (Planner Agent)：**
       - **职责：** 基于研究结果生成详细的行程计划。
       - **功能：** 确保行程结构良好、信息丰富且具有吸引力，提供平衡的建议，包含事实引用。
       - **依赖：** 接收研究员智能体提供的原始信息，并进行整合、筛选和优化，形成可行的旅行方案。
   - **核心功能：**
     - 用户输入旅行偏好（如：目的地、预算、天数、偏好活动类型）。
     - 研究员智能体进行信息收集。
     - 规划师智能体基于收集到的信息生成详细的旅行计划。

------

## 模块七：n8n（AI Workflow Automation）实战

说明：

本模块将介绍n8n这一强大的AI工作流自动化平台，旨在帮助学员脱离代码，通过可视化界面快速编排和部署复杂的AI工作流。学员将学习n8n的环境搭建、基础操作，以及如何集成大模型API。通过构建企业级自动化工作流，例如社交媒体内容审核或客户反馈智能分类，学员将掌握利用n8n实现业务流程自动化的能力。

**学习目标：**

- 理解n8n作为AI工作流自动化平台的价值与核心概念。
- 掌握n8n环境搭建（Docker）及UI界面的基础操作。
- 能够使用n8n的节点调用大模型API，并编排数据流。
- 能够设计并实现基于大模型的自动化业务工作流。

**详细内容：**

1. **n8n 简介与核心概念**
   - 什么是n8n？**可视化工作流自动化平台**。
   - 核心概念：**节点 (Nodes)**、**工作流 (Workflows)**、**触发器 (Triggers)**、**操作 (Operations)**。
   - n8n与大模型结合的优势：**快速编排、降低代码量、可视化管理**。
2. **n8n环境搭建与基础操作**
   - **Docker部署n8n** (推荐)。
   - n8n UI界面介绍与导航。
   - 基础工作流构建：**Webhook触发、HTTP请求节点、数据转换、条件判断**。
3. **n8n与大模型集成实战**
   - 使用n8n的**HTTP Request节点**或**OpenAI节点**调用大模型API。
   - 数据流与变量管理。
4. **n8n高级应用与实战**
   - 自定义节点开发 (JavaScript/Python Code节点)。
   - 错误处理与日志记录。
   - 结合RAG和Agent：在n8n中编排复杂的AI工作流。

5. **n8n-MCP：让AI通过自然语言自动生成n8n工作流**
   - n8n-MCP（Model Context Protocol）是一个创新的AI集成方案，使大模型（如Claude、GPT-4o等）能够通过自然语言直接理解n8n的全部节点、属性和操作，自动生成、修改和验证n8n工作流。
   - 支持AI自动发现、管理和调用n8n的“工具型”工作流，实现“用一句话让AI帮你搭建自动化流程”。

**实践练习：**

1. **实践一：自动化社交媒体内容审核工作流**
   - 任务：使用n8n构建一个工作流。
   - 触发器：模拟接收到新的社交媒体内容（例如通过**Webhook**节点接收一个JSON请求）。
   - 操作：调用大模型API（例如**OpenAI GPT-4o**）对内容进行审核，判断是否包含敏感词汇或不当内容。
   - 分支：根据大模型的审核结果，如果内容违规，发送一封邮件通知审核人员；如果合规，则模拟发布（例如打印到控制台或发送到Mock API）。
2. **实践二：客户反馈智能分类与派发工作流**
   - 任务：设计并实现一个n8n工作流，自动化处理客户反馈。
   - 触发器：模拟接收客户邮件或表单提交。
   - 操作：大模型抽取关键信息（如：客户姓名、问题类型、紧急程度、产品名称）。
   - 操作：根据问题类型和紧急程度，自动派发到不同部门的负责人邮箱或内部工单系统（通过n8n的**Email Send Node**或**HTTP Request Node**调用虚拟工单API）。

3. **实践三：AI驱动的自然语言工作流生成（n8n-MCP实战）**
   - 任务：基于n8n-mcp，体验AI通过自然语言自动生成n8n工作流。
   - 步骤：
     - 部署n8n-mcp服务，并配置到Claude Desktop或支持MCP协议的AI Agent。
     - 用自然语言描述一个自动化需求（如“每天早上汇总邮箱新邮件并推送到企业微信”）。
     - 让AI自动生成n8n工作流JSON，导入n8n并验证其可用性。
   - 要求：体验AI自动理解n8n节点、参数和流程，感受“零代码”自动化开发的创新能力。

4. **实践四：使用n8n实现RAG（检索增强生成）系统**
   - 任务：基于n8n平台，构建一个RAG智能问答系统。
   - 步骤：
     - 搭建n8n环境，集成向量数据库（如ChromaDB、Qdrant等）和大模型API（如OpenAI、DeepSeek等）。
     - 设计工作流，实现文档上传、分块、文本向量化、入库。
     - 用户提问时，检索相关文档片段，并调用大模型API生成答案。
     - 支持多格式文档（如PDF、Markdown），并实现检索结果与问题的上下文拼接。
   - 要求：实现端到端的RAG流程，体验n8n在AI知识问答自动化中的强大能力。

------

## 模块八：大模型高效微调工具实战

说明：

本模块将深入探讨大模型微调技术，特别是高效参数微调（PEFT）方法。学员将学习微调的必要性、LoRA、Q-LoRA等主流PEFT原理，并掌握Usloth、LlamaFactory这一强大的微调工具。通过实际操作，学员将对开源大模型进行LoRA微调，并学会评估和部署微调后的模型，从而实现模型的领域特化和性能优化。

**学习目标：**

- 理解大模型微调的重要性，区分全量微调与PEFT。
- 掌握LoRA、Q-LoRA等主流PEFT方法的原理与优势。
- 熟练使用LlamaFactory进行开源大模型的PEFT微调。
- 学会评估微调模型的效果，并将其部署到推理服务。

**详细内容：**

1. **大模型微调概述**
   - 为什么需要微调？(领域适应、任务特化、性能优化、成本控制)。
   - **全量微调 (Full Fine-tuning)** 与 **高效参数微调 (PEFT)** 对比。
2. **PEFT (Parameter-Efficient Fine-Tuning) 方法**
   - **LoRA (Low-Rank Adaptation) 原理与优势**：低秩矩阵分解、减少训练参数量。
   - **Q-LoRA (Quantized LoRA)**：量化后LoRA，进一步降低显存占用。
   - **P-Tuning v2**：基于可学习Prompt的轻量微调。
   - **Adapter**：插入小型神经网络模块进行增量训练。
   - 适用场景分析：何时选择哪种微调方式？
3. **LlamaFactory (LLaMA-Factory) 入门**
   - LlamaFactory简介与功能：**一站式大模型微调训练框架**。
   - 环境搭建与依赖安装：**Python依赖、PyTorch、Transformers**。
   - 数据集准备与格式要求：**Alpaca格式 (JSONL)**。
4. **LlamaFactory PEFT微调实战**
   - 配置文件参数详解：**学习率、batch size、epoch、LoRA秩、Alpha值**等。
   - 监控微调过程：**TensorBoard** (Loss曲线、评估指标)。
   - 微调模型保存与加载：**Adapter模型保存与合并 (merge_and_unload)**。
5. **微调模型评估与部署**
   - 评估指标：**Perplexity、Rouge、BLEU** (生成任务)，**准确率、F1 Score** (分类任务)。

**实践练习：**

1. **实践一：法律领域开源大模型LoRA微调**
   - 任务：选择一个开源大模型（如**Qwen-1.5-7B-Chat / Llama 3 8B-Instruct**）。
   - 任务：收集一个小型（例如：100-200条问答对）的法律领域数据集（可自制或从公开数据集中抽取，例如：关于合同法、劳动法的常见问题解答）。
   - 任务：使用LlamaFactory对该模型进行**LoRA微调**。
   - 任务：监控训练过程中的Loss变化。
2. **实践二：微调模型效果评估与部署**
   - 任务：使用测试集评估微调前后模型在该法律领域问答任务上的表现，进行**人工评估**（判断回答的准确性、专业性）和**量化指标**（若有合适的评估工具）。
   - 任务：将微调后的Adapter模型与原模型合并，并使用**vLLM**或其他推理框架进行部署，通过API测试其在法律领域问答上的能力。

------



# LLM101 第二模块：大模型基础推理与提示词工程
# pip依赖包配置文件（与environment.yml版本保持一致）

# === 大模型API调用相关 ===
openai==1.61.0              # OpenAI官方SDK，用于调用GPT系列模型
python-dotenv==1.0.0        # 环境变量管理，安全存储API密钥

# === 数据处理与验证 ===
pandas==2.0.0               # 数据分析和处理库，处理结构化数据
numpy==1.24.0               # 数值计算基础库，支持数组操作
pydantic==2.0.0             # 数据验证和类型检查，确保数据质量

# === 模板引擎 ===
jinja2==3.1.0               # 模板引擎，用于提示词模板管理

# === 网络请求 ===
requests==2.28.0            # HTTP请求库，用于API调用和网络请求
httpx==0.24.0               # 现代异步HTTP客户端，支持HTTP/2

# === 日期时间处理 ===
python-dateutil==2.8.2     # 日期时间解析和处理扩展

# === 测试框架 ===
pytest==7.2.0              # 单元测试框架，保证代码质量
pytest-asyncio==0.21.0     # pytest异步测试支持

# === 类型检查与代码质量 ===
mypy==1.0.0                 # 静态类型检查工具，提高代码可靠性

# === 代码格式化工具 ===
black==23.1.0               # 代码格式化工具，统一代码风格
isort==5.12.0               # import语句排序工具，整理导入语句

# === 用户界面增强 ===
tqdm==4.64.0                # 进度条显示，提升用户体验

# === 配置管理 ===
pyyaml==6.0                 # YAML文件解析，用于配置文件读取 
# AI驱动的RAG聊天机器人使用文档

## 概述

这个n8n工作流创建了一个强大的RAG（检索增强生成）聊天机器人，能够处理、存储和与Google Drive中的文档进行交互，使用Qdrant向量存储和Google的Gemini AI。

## 功能特性

### 🔍 智能文档处理
- 自动从指定的Google Drive文件夹检索文档
- 将文档分割成可管理的块进行处理
- 使用AI提取元数据以增强搜索能力
- 在Qdrant中存储文档向量以实现高效检索

### 💬 智能聊天界面
- 提供由Google Gemini驱动的对话界面
- 使用RAG从存储的文档中检索相关上下文
- 在Google Docs中维护聊天历史记录以供参考
- 提供准确、上下文感知的响应

### 🗄️ 向量存储管理
- 具有人工验证的安全删除操作
- 包含重要操作的Telegram通知
- 通过适当的版本控制维护数据完整性
- 支持文档的批量处理

## 工作流程说明

### 步骤1：文档处理和存储

1. **配置Google Drive文件夹**
   - 在"Google文件夹ID"节点中设置您的Google Drive文件夹ID
   - 工作流将自动扫描该文件夹中的所有文档

2. **文档处理流程**
   ```
   Google Drive文件夹 → 查找文件ID → 下载文件 → 提取内容 → AI元数据提取 → 向量化 → 存储到Qdrant
   ```

3. **元数据提取**
   - 总体主题分析
   - 重复话题识别
   - 痛点总结
   - 分析洞察
   - 结论提取
   - 关键词生成

### 步骤2：聊天交互

1. **启动聊天**
   - 使用"当收到聊天消息时"触发器
   - 聊天界面会自动激活

2. **查询处理**
   ```
   用户输入 → AI代理 → Qdrant向量搜索 → 上下文检索 → Gemini生成响应 → 返回用户
   ```

3. **记忆管理**
   - 使用窗口缓冲记忆保持对话上下文
   - 自动保存聊天历史到Google Docs

## 使用步骤

### 初始设置

1. **导入工作流**
   - 将JSON文件导入到您的n8n实例中
   - 确保所有必需的节点包已安装

2. **配置凭据**（详见配置文档）
   - Google Drive OAuth2 API
   - Google Gemini API
   - OpenAI API（用于嵌入）
   - Qdrant API
   - Telegram Bot API

3. **设置基本参数**
   - 在"Google文件夹ID"节点中设置您的文件夹ID
   - 在"Qdrant集合名称"节点中配置集合名称

### 文档上传和处理

1. **准备文档**
   - 将要处理的文档上传到指定的Google Drive文件夹
   - 支持的格式：PDF、DOC、DOCX、TXT等

2. **执行文档处理**
   - 点击"测试工作流"按钮开始处理
   - 工作流将自动：
     - 扫描文件夹中的所有文件
     - 下载并提取文本内容
     - 使用AI分析和提取元数据
     - 将文档向量化并存储到Qdrant

3. **监控处理进度**
   - 通过Telegram接收处理状态通知
   - 检查n8n执行日志了解详细信息

### 聊天交互

1. **开始对话**
   - 访问聊天触发器的webhook URL
   - 或使用n8n的聊天界面

2. **提问技巧**
   - 提出具体、明确的问题
   - 可以询问文档中的特定信息
   - 支持多轮对话和上下文跟踪

3. **示例查询**
   ```
   - "文档中提到的主要痛点是什么？"
   - "总结一下关于去中心化的讨论"
   - "有哪些重复出现的话题？"
   - "文档的主要结论是什么？"
   ```

### 向量存储管理

1. **删除操作**
   - 工作流包含安全的删除功能
   - 需要通过Telegram进行人工确认
   - 删除操作不可撤销，请谨慎操作

2. **更新文档**
   - 要更新文档，首先删除旧版本
   - 然后重新运行文档处理流程

## 最佳实践

### 文档准备
- 确保文档格式清晰，结构良好
- 避免过大的文件（建议单个文件小于10MB）
- 使用描述性的文件名

### 查询优化
- 使用具体、明确的问题
- 避免过于宽泛或模糊的查询
- 利用文档中的关键词进行搜索

### 性能优化
- 定期清理不需要的向量数据
- 监控Qdrant存储使用情况
- 根据需要调整块大小和重叠参数

## 故障排除

### 常见问题

1. **文档处理失败**
   - 检查Google Drive权限
   - 确认文件格式支持
   - 查看API配额限制

2. **聊天响应慢**
   - 检查Qdrant连接状态
   - 优化向量搜索参数
   - 考虑减少检索的文档数量

3. **向量存储错误**
   - 验证Qdrant服务状态
   - 检查集合配置
   - 确认API密钥有效性

### 日志和监控
- 启用n8n详细日志记录
- 监控各个节点的执行状态
- 使用Telegram通知跟踪重要操作

## 扩展功能

### 自定义元数据
- 修改"提取元数据"节点以添加自定义字段
- 调整AI提示以提取特定信息

### 多语言支持
- 配置Gemini模型支持多种语言
- 调整系统提示以适应不同语言

### 集成其他服务
- 添加更多文档源（Dropbox、OneDrive等）
- 集成其他通知渠道（Slack、Discord等）

## 安全注意事项

- 定期轮换API密钥
- 限制Google Drive文件夹访问权限
- 监控API使用情况和成本
- 备份重要的向量数据

---

*此文档将随着工作流的更新而持续维护和改进。*

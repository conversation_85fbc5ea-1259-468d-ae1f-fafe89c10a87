{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 第六章：逐步思考\n", "\n", "\n", "## 设置\n", "\n", "运行以下设置单元格来加载您的API密钥并建立`get_completion`辅助函数。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Requirement already satisfied: openai==1.61.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (1.61.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.10.0)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (2.11.5)\n", "Requirement already satisfied: sniffio in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.67.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.14.0)\n", "Requirement already satisfied: idna>=2.8 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from anyio<5,>=3.5.0->openai==1.61.0) (3.10)\n", "Requirement already satisfied: certifi in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (2025.4.26)\n", "Requirement already satisfied: httpcore==1.* in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai==1.61.0) (0.16.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.4.1)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n", "Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Requirement already satisfied: openai==1.61.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (1.61.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.10.0)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (2.11.5)\n", "Requirement already satisfied: sniffio in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.67.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.14.0)\n", "Requirement already satisfied: idna>=2.8 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from anyio<5,>=3.5.0->openai==1.61.0) (3.10)\n", "Requirement already satisfied: certifi in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (2025.4.26)\n", "Requirement already satisfied: httpcore==1.* in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai==1.61.0) (0.16.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.4.1)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n", "✅ OpenAI环境设置完成!\n", "🔧 OpenAI API 配置信息:\n", "  📡 配置来源: 环境变量 + 自定义API地址: https://vip.apiyi.com/v1\n", "  🤖 模型: gpt-4o\n", "  🌐 API地址: https://vip.apiyi.com/v1\n", "  🔑 API密钥: sk-R2utG...B944\n", "\n", "✅ 使用统一配置管理成功！\n"]}], "source": ["# 安装OpenAI库\n", "%pip install openai==1.61.0\n", "\n", "# 🔧 OpenAI环境自动配置\n", "# 此设置会自动从环境变量或IPython存储中加载配置\n", "\n", "# 安装OpenAI库\n", "%pip install openai==1.61.0\n", "\n", "# 导入Python内置的正则表达式库\n", "import re\n", "\n", "# 🚀 使用统一配置管理系统\n", "from config import setup_notebook_environment, print_config_info\n", "\n", "# 自动设置OpenAI客户端和get_completion函数\n", "# 优先级：环境变量 > IPython存储 > 默认值\n", "try:\n", "    client, get_completion = setup_notebook_environment()\n", "    print(\"✅ 使用统一配置管理成功！\")\n", "except Exception as e:\n", "    print(f\"❌ 统一配置失败，回退到传统方式: {e}\")\n", "    \n", "    # 回退到传统的配置方式\n", "    import openai\n", "    \n", "    # 从IPython存储中检索API_KEY和MODEL_NAME变量\n", "    %store -r API_KEY\n", "    %store -r MODEL_NAME\n", "\n", "    # 如果没有设置MODEL_NAME，使用默认值\n", "    try:\n", "        MODEL_NAME\n", "    except NameError:\n", "        MODEL_NAME = \"gpt-3.5-turbo\"  # 默认使用gpt-4o模型\n", "\n", "    # 创建OpenAI客户端\n", "    client = openai.OpenAI(api_key=API_KEY)\n", "\n", "    def get_completion(prompt: str, system_prompt=\"\"):\n", "        \"\"\"\n", "        获取GPT的完成响应\n", "        \n", "        参数:\n", "            prompt (str): 用户提示\n", "            system_prompt (str): 系统提示（可选）\n", "        \n", "        返回:\n", "            str: GPT的响应文本\n", "        \"\"\"\n", "        # 构建消息列表\n", "        messages = []\n", "        \n", "        # 如果有系统提示，添加系统消息\n", "        if system_prompt:\n", "            messages.append({\"role\": \"system\", \"content\": system_prompt})\n", "        \n", "        # 添加用户消息\n", "        messages.append({\"role\": \"user\", \"content\": prompt})\n", "        \n", "        # 调用OpenAI API\n", "        response = client.chat.completions.create(\n", "            model=MODEL_NAME,              # 模型名称 (gpt-4o 或 deepseek-r1)\n", "            messages=messages,             # 消息列表\n", "            max_completion_tokens=2000,    # 最大token数\n", "            temperature=0.0               # 温度参数，0表示更确定性\n", "        )\n", "        return response.choices[0].message.content\n", "    \n", "    print(\"⚠️  使用传统配置方式，建议配置环境变量以获得更好体验\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "## 课程\n", "\n", "如果有人叫醒您，然后立即开始问您几个复杂的问题，要求您立即回答，您会表现如何？可能不如给您时间**先思考答案**时表现得好。\n", "\n", "猜猜怎么着？GPT也是一样的。\n", "\n", "**给GPT时间逐步思考有时会让GPT更准确**，特别是对于复杂任务。"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 🔥 中文提示词逐步思考示例\n", "\n", "在中文语境下，逐步思考技术同样非常有效。以下示例展示了如何在中文提示词中应用逐步思考的方法。\n", "\n", "### 核心原理\n", "- **让模型慢下来思考**：给大模型时间进行逐步推理\n", "- **结构化思维过程**：通过明确的步骤引导模型思考\n", "- **提高准确性**：特别适用于复杂推理、数学计算、逻辑分析等任务\n", "\n", "让我们通过实际示例来学习！\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### 示例1：数学问题解决 - 对比基础版与逐步思考版\n", "\n", "**背景场景**：解决复杂的数学应用题\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📍 基础版本：直接提问数学题\n", "==================================================\n", "GPT回答：\n", "设小明妈妈一共买了 \\( x \\) 个苹果。\n", "\n", "第一天吃掉了总数的 \\( \\frac{1}{4} \\)，剩下的苹果数为：\n", "\\[\n", "x - \\frac{1}{4}x = \\frac{3}{4}x\n", "\\]\n", "\n", "第二天吃掉了剩余的 \\( \\frac{1}{3} \\)，剩下的苹果数为：\n", "\\[\n", "\\frac{3}{4}x - \\frac{1}{3} \\times \\frac{3}{4}x = \\frac{3}{4}x \\times \\frac{2}{3} = \\frac{1}{2}x\n", "\\]\n", "\n", "第三天吃掉了4个苹果，剩下的苹果数为：\n", "\\[\n", "\\frac{1}{2}x - 4\n", "\\]\n", "\n", "最后还剩下8个苹果，所以有：\n", "\\[\n", "\\frac{1}{2}x - 4 = 8\n", "\\]\n", "\n", "解这个方程：\n", "\\[\n", "\\frac{1}{2}x = 12\n", "\\]\n", "\\[\n", "x = 24\n", "\\]\n", "\n", "所以小明妈妈一共买了24个苹果。\n", "\n", "================================================================================\n", "\n"]}], "source": ["# 示例1A：基础版本 - 直接提问\n", "print(\"📍 基础版本：直接提问数学题\")\n", "print(\"=\" * 50)\n", "\n", "BASIC_MATH_PROMPT = \"\"\"\n", "小明的妈妈买了一些苹果。第一天吃掉了总数的1/4，第二天吃掉了剩余的1/3，第三天吃掉了4个，最后还剩下8个苹果。请问小明妈妈一共买了多少个苹果？\n", "\"\"\"\n", "\n", "response_basic = get_completion(BASIC_MATH_PROMPT)\n", "print(\"GPT回答：\")\n", "print(response_basic)\n", "print(\"\\n\" + \"=\"*80 + \"\\n\")\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧠 逐步思考版本：引导详细推理\n", "==================================================\n", "GPT回答：\n", "好的，我们一步步来解决这个问题。\n", "\n", "### 1. 理解题目条件，设定未知数\n", "\n", "设小明的妈妈一共买了 \\( x \\) 个苹果。\n", "\n", "### 2. 分析每天的消费情况\n", "\n", "- **第一天**：吃掉了总数的 \\( \\frac{1}{4} \\)，即 \\( \\frac{1}{4}x \\) 个苹果。  \n", "  剩下的苹果数为：  \n", "  \\[\n", "  x - \\frac{1}{4}x = \\frac{3}{4}x\n", "  \\]\n", "\n", "- **第二天**：吃掉了剩余的 \\( \\frac{1}{3} \\)，即 \\( \\frac{1}{3} \\times \\frac{3}{4}x = \\frac{1}{4}x \\) 个苹果。  \n", "  剩下的苹果数为：  \n", "  \\[\n", "  \\frac{3}{4}x - \\frac{1}{4}x = \\frac{1}{2}x\n", "  \\]\n", "\n", "- **第三天**：吃掉了 4 个苹果。  \n", "  剩下的苹果数为：  \n", "  \\[\n", "  \\frac{1}{2}x - 4\n", "  \\]\n", "\n", "- **最后**：还剩下 8 个苹果。  \n", "  \\[\n", "  \\frac{1}{2}x - 4 = 8\n", "  \\]\n", "\n", "### 3. 建立数学方程\n", "\n", "根据最后的条件，我们可以建立方程：  \n", "\\[\n", "\\frac{1}{2}x - 4 = 8\n", "\\]\n", "\n", "### 4. 求解并验证答案\n", "\n", "解这个方程：  \n", "\\[\n", "\\frac{1}{2}x - 4 = 8\n", "\\]\n", "\n", "首先，移项：  \n", "\\[\n", "\\frac{1}{2}x = 8 + 4\n", "\\]\n", "\\[\n", "\\frac{1}{2}x = 12\n", "\\]\n", "\n", "两边乘以 2：  \n", "\\[\n", "x = 24\n", "\\]\n", "\n", "所以，小明的妈妈一共买了 24 个苹果。\n", "\n", "**验证：**\n", "\n", "- **第一天**：吃掉 \\( \\frac{1}{4} \\times 24 = 6 \\) 个，剩下 \\( 24 - 6 = 18 \\) 个。\n", "- **第二天**：吃掉 \\( \\frac{1}{3} \\times 18 = 6 \\) 个，剩下 \\( 18 - 6 = 12 \\) 个。\n", "- **第三天**：吃掉 4 个，剩下 \\( 12 - 4 = 8 \\) 个。\n", "\n", "验证结果与题目条件一致，答案正确。小明的妈妈一共买了 24 个苹果。\n", "\n", "================================================================================\n", "\n"]}], "source": ["# 示例1B：逐步思考版本 - 引导详细推理过程\n", "print(\"🧠 逐步思考版本：引导详细推理\")\n", "print(\"=\" * 50)\n", "\n", "STEP_BY_STEP_MATH_PROMPT = \"\"\"\n", "小明的妈妈买了一些苹果。第一天吃掉了总数的1/4，第二天吃掉了剩余的1/3，第三天吃掉了4个，最后还剩下8个苹果。请问小明妈妈一共买了多少个苹果？\n", "\n", "请按照以下步骤一步一步分析：\n", "1. 首先，理解题目条件，设定未知数\n", "2. 然后，分析每天的消费情况\n", "3. 接着，建立数学方程\n", "4. 最后，求解并验证答案\n", "\n", "让我一步步来解决这个问题：\n", "\"\"\"\n", "\n", "response_step_by_step = get_completion(STEP_BY_STEP_MATH_PROMPT)\n", "print(\"GPT回答：\")\n", "print(response_step_by_step)\n", "print(\"\\n\" + \"=\"*80 + \"\\n\")\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### 示例2：情感分析 - 中文版本的逐步思考\n", "\n", "**背景场景**：分析中文评论的情感倾向，特别是含有讽刺或双重含义的内容\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📍 基础版本：直接分析评论情感\n", "==================================================\n", "GPT回答：\n", "这段评论使用了讽刺的语气，虽然表面上使用了一些积极的词汇（如“精彩”、“印象深刻”、“自然”、“终生难忘”），但结合上下文来看，这些词实际上是在表达负面的情感倾向。评论者提到自己在观看电影时睡着了，醒来后电影还未结束，暗示电影冗长乏味。此外，演员的表演被描述为“自然”到让人怀疑是在念台词，这进一步表明对表演的不满。因此，整体情感倾向是负面的。\n", "\n", "============================================================\n", "\n"]}], "source": ["# 示例2A：基础版本 - 直接分析情感\n", "print(\"📍 基础版本：直接分析评论情感\")\n", "print(\"=\" * 50)\n", "\n", "BASIC_SENTIMENT_PROMPT = \"\"\"\n", "请分析以下评论的情感倾向（正面/负面/中性）：\n", "\n", "\"这部电影真是太'精彩'了，我看到一半就睡着了，醒来发现还没结束，真是让人'印象深刻'。演员的表演也很'自然'，自然到我都怀疑他们是不是在念台词。总之，这是一部让我'终生难忘'的作品。\"\n", "\"\"\"\n", "\n", "response_basic_sentiment = get_completion(BASIC_SENTIMENT_PROMPT)\n", "print(\"GPT回答：\")\n", "print(response_basic_sentiment)\n", "print(\"\\n\" + \"=\"*60 + \"\\n\")\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧠 逐步思考版本：结构化情感分析\n", "==================================================\n", "GPT回答：\n", "<步骤1>\n", "首先分析表面词汇：识别评论中使用的形容词和描述性词语\n", "\n", "在评论中，使用了以下形容词和描述性词语：\n", "- \"精彩\"\n", "- \"印象深刻\"\n", "- \"自然\"\n", "- \"终生难忘\"\n", "\n", "这些词汇表面上看起来是积极的，通常用于赞美和肯定。\n", "\n", "<步骤2>\n", "然后分析语境线索：注意引号、反讽等修辞手法\n", "\n", "评论中使用了引号，例如“精彩”、“印象深刻”、“自然”、“终生难忘”，这些引号通常暗示反讽或讽刺的语气。此外，评论中提到“看到一半就睡着了”、“醒来发现还没结束”，这些语境线索表明评论者对电影的实际体验并不积极。\n", "\n", "<步骤3>\n", "接着分析具体描述：分析用户提到的具体行为和感受\n", "\n", "评论者提到“看到一半就睡着了”，这表明电影并没有吸引力或足够的精彩来保持观众的注意力。醒来后发现电影还没结束，进一步暗示电影冗长或乏味。对于演员的表演，评论者怀疑他们是否在念台词，这表明表演缺乏真实感或投入。\n", "\n", "<步骤4>\n", "最后综合判断：基于以上分析，判断真实的情感倾向\n", "\n", "综合以上分析，评论者使用了反讽的手法，通过表面积极的词汇和引号来表达对电影的不满和失望。评论者的真实情感倾向是负面的，对电影的评价是消极的。\n", "\n", "============================================================\n", "\n"]}], "source": ["# 示例2B：逐步思考版本 - 结构化分析过程\n", "print(\"🧠 逐步思考版本：结构化情感分析\")\n", "print(\"=\" * 50)\n", "\n", "STEP_BY_STEP_SENTIMENT_PROMPT = \"\"\"\n", "请分析以下评论的情感倾向。请先进行逐步分析，然后给出最终判断。\n", "\n", "评论：\n", "\"这部电影真是太'精彩'了，我看到一半就睡着了，醒来发现还没结束，真是让人'印象深刻'。演员的表演也很'自然'，自然到我都怀疑他们是不是在念台词。总之，这是一部让我'终生难忘'的作品。\"\n", "\n", "请按照以下步骤分析：\n", "\n", "<步骤1>\n", "首先分析表面词汇：识别评论中使用的形容词和描述性词语\n", "</步骤1>\n", "\n", "<步骤2>\n", "然后分析语境线索：注意引号、反讽等修辞手法\n", "</步骤2>\n", "\n", "<步骤3>\n", "接着分析具体描述：分析用户提到的具体行为和感受\n", "</步骤3>\n", "\n", "<步骤4>\n", "最后综合判断：基于以上分析，判断真实的情感倾向\n", "</步骤4>\n", "\n", "让我逐步分析这个评论：\n", "\"\"\"\n", "\n", "response_step_sentiment = get_completion(STEP_BY_STEP_SENTIMENT_PROMPT)\n", "print(\"GPT回答：\")\n", "print(response_step_sentiment)\n", "print(\"\\n\" + \"=\"*60 + \"\\n\")\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### 示例3：商业决策分析 - 中文商业场景\n", "\n", "**背景场景**：帮助企业制定市场策略决策\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📍 基础版本：直接询问商业策略\n", "==================================================\n", "GPT回答：\n", "在面对激烈的市场竞争时，尤其是与像Apple和华为这样的强大品牌竞争时，制定有效的市场策略至关重要。以下是一些建议，可以帮助您的公司在智能手环市场中提升竞争力：\n", "\n", "1. **差异化产品**：\n", "   - **独特功能**：开发具有独特功能的智能手环，例如专注于特定健康监测（如血糖监测）或提供个性化健身建议。\n", "   - **设计创新**：在设计上进行创新，提供多样化的款式和颜色，以吸引不同的消费者群体。\n", "\n", "2. **目标市场细分**：\n", "   - **专注于特定人群**：例如，针对运动爱好者、老年人或儿童设计专门的产品。\n", "   - **区域市场策略**：分析不同地区的市场需求，制定针对性的营销策略。\n", "\n", "3. **提升用户体验**：\n", "   - **用户界面优化**：确保手环的操作简单直观，提升用户体验。\n", "   - **应用生态系统**：开发与手环配套的应用程序，提供更多增值服务。\n", "\n", "4. **品牌建设与营销**：\n", "   - **品牌故事**：讲述品牌的独特故事，增强品牌的情感吸引力。\n", "   - **社交媒体营销**：利用社交媒体平台进行推广，与用户互动，建立社区。\n", "\n", "5. **合作与联盟**：\n", "   - **与健康机构合作**：与医院、健身中心等合作，提升产品的专业性和可信度。\n", "   - **跨界合作**：与时尚品牌或科技公司合作，推出联名产品。\n", "\n", "6. **价格策略**：\n", "   - **竞争性定价**：在保证质量的前提下，提供具有竞争力的价格。\n", "   - **促销活动**：定期推出促销活动，吸引消费者购买。\n", "\n", "7. **客户反馈与改进**：\n", "   - **收集用户反馈**：通过调查和用户评论收集反馈，持续改进产品。\n", "   - **快速响应市场变化**：根据市场趋势和用户需求快速调整产品和策略。\n", "\n", "通过以上策略，您的公司可以在竞争激烈的市场中找到自己的定位，提升品牌知名度和市场份额。\n", "\n", "============================================================\n", "\n"]}], "source": ["# 示例3A：基础版本 - 直接提问商业建议\n", "print(\"📍 基础版本：直接询问商业策略\")\n", "print(\"=\" * 50)\n", "\n", "BASIC_BUSINESS_PROMPT = \"\"\"\n", "我们公司是一家生产智能手环的科技公司，目前面临激烈的市场竞争。Apple Watch和华为等大品牌占据了主要市场份额，我们的销量正在下滑。请给出市场策略建议。\n", "\"\"\"\n", "\n", "response_basic_business = get_completion(BASIC_BUSINESS_PROMPT)\n", "print(\"GPT回答：\")\n", "print(response_basic_business)\n", "print(\"\\n\" + \"=\"*60 + \"\\n\")\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧠 逐步思考版本：结构化商业策略分析\n", "==================================================\n", "GPT回答：\n", "<市场分析>\n", "\n", "1. **竞争格局**: 智能手环市场竞争激烈，主要由几大品牌主导，如Apple、华为、三星等。这些品牌拥有强大的品牌影响力和技术研发能力。市场上也有许多中小型企业提供价格更低的产品，试图通过性价比吸引消费者。\n", "\n", "2. **用户需求**: 用户对智能手环的需求主要集中在健康监测（如心率、睡眠、运动）、便捷支付、通知提醒等功能。此外，用户越来越关注产品的设计美观、续航能力和与其他设备的兼容性。\n", "\n", "3. **趋势**: 市场趋势显示，消费者对健康和健身功能的需求持续增长，尤其是在疫情后。可穿戴设备的智能化和个性化定制也成为趋势。随着技术进步，AI和大数据在智能手环中的应用越来越普遍。\n", "\n", "<竞争对手分析>\n", "\n", "1. **Apple Watch**:\n", "   - 优势: 强大的品牌影响力、丰富的生态系统、卓越的用户体验和设计、强大的技术支持。\n", "   - 劣势: 价格较高，可能不适合预算有限的消费者。\n", "\n", "2. **华为**:\n", "   - 优势: 性价比高、强大的技术研发能力、良好的电池续航、广泛的市场覆盖。\n", "   - 劣势: 在某些市场（如美国）面临政治和贸易限制。\n", "\n", "<自身SWOT分析>\n", "\n", "1. **优势**:\n", "   - 专注于某些特定功能或用户群体（如专业运动员或老年人）。\n", "   - 具有灵活的产品设计和快速响应市场变化的能力。\n", "\n", "2. **劣势**:\n", "   - 品牌知名度较低，市场营销资源有限。\n", "   - 技术研发能力可能不如大品牌。\n", "\n", "3. **机会**:\n", "   - 健康和健身市场的持续增长。\n", "   - 新兴市场的扩展机会。\n", "\n", "4. **威胁**:\n", "   - 大品牌的市场主导地位。\n", "   - 技术快速变化带来的压力。\n", "\n", "<差异化策略>\n", "\n", "1. **定位**: 专注于特定用户群体，如专业运动员、老年人或特定健康需求的用户，提供定制化解决方案。\n", "\n", "2. **策略**: 强调产品的独特功能和用户体验，利用价格优势和灵活的设计来吸引特定市场。\n", "\n", "<具体执行方案>\n", "\n", "1. **产品创新**: 开发具有独特功能的智能手环，如针对特定健康问题的监测功能，或与专业运动应用集成的功能。\n", "\n", "2. **市场营销**: 加强品牌宣传，通过社交媒体和合作伙伴关系提高品牌知名度。利用用户推荐和口碑营销来扩大影响力。\n", "\n", "3. **价格策略**: 提供多层次的产品线，以满足不同预算的消费者需求，同时保持高性价比。\n", "\n", "4. **渠道扩展**: 拓展线上和线下销售渠道，尤其是在新兴市场，增加产品的可见性和可获得性。\n", "\n", "5. **客户服务**: 提供优质的售后服务和客户支持，增强用户忠诚度和满意度。通过用户反馈不断改进产品和服务。\n", "\n", "============================================================\n", "\n"]}], "source": ["# 示例3B：逐步思考版本 - 结构化商业分析\n", "print(\"🧠 逐步思考版本：结构化商业策略分析\")\n", "print(\"=\" * 50)\n", "\n", "STEP_BY_STEP_BUSINESS_PROMPT = \"\"\"\n", "我们公司是一家生产智能手环的科技公司，目前面临激烈的市场竞争。Apple Watch和华为等大品牌占据了主要市场份额，我们的销量正在下滑。\n", "\n", "请按照商业分析框架逐步分析并提出策略建议：\n", "\n", "<市场分析>\n", "首先分析当前智能手环市场的竞争格局、用户需求和趋势\n", "</市场分析>\n", "\n", "<竞争对手分析>\n", "分析主要竞争对手（Apple Watch、华为等）的优势和劣势\n", "</竞争对手分析>\n", "\n", "<自身SWOT分析>\n", "分析我们公司的优势(Strengths)、劣势(Weaknesses)、机会(Opportunities)、威胁(Threats)\n", "</自身SWOT分析>\n", "\n", "<差异化策略>\n", "基于分析结果，提出我们的差异化定位和策略\n", "</差异化策略>\n", "\n", "<具体执行方案>\n", "给出3-5个具体可执行的市场策略和实施建议\n", "</具体执行方案>\n", "\n", "让我按照框架来分析：\n", "\"\"\"\n", "\n", "response_step_business = get_completion(STEP_BY_STEP_BUSINESS_PROMPT)\n", "print(\"GPT回答：\")\n", "print(response_step_business)\n", "print(\"\\n\" + \"=\"*60 + \"\\n\")\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### 🎯 逐步思考技巧总结\n", "\n", "通过以上中文示例，我们可以看到逐步思考的核心价值：\n", "\n", "#### 🔑 关键技巧\n", "\n", "1. **使用引导性短语**\n", "   - \"让我一步步来解决这个问题：\"\n", "   - \"请按照以下步骤分析：\"\n", "   - \"首先...然后...接着...最后...\"\n", "\n", "2. **结构化标签**\n", "   - 使用`<步骤1>`、`<分析>`、`<结论>`等XML标签\n", "   - 明确每个步骤的具体任务\n", "   - 保持逻辑递进关系\n", "\n", "3. **领域专业框架**\n", "   - 数学：理解→分析→建立方程→求解→验证\n", "   - 商业：市场分析→竞争分析→SWOT→策略→执行\n", "   - 创作：主题→人物→情节→冲突→细节\n", "\n", "#### 📈 效果对比\n", "\n", "| 方面 | 基础提示 | 逐步思考提示 |\n", "|------|----------|--------------|\n", "| **准确性** | 一般 | 显著提升 |\n", "| **逻辑性** | 跳跃 | 严密连贯 |\n", "| **深度** | 表面 | 深入分析 |\n", "| **可用性** | 需要二次确认 | 直接可用 |\n", "\n", "#### 💡 适用场景\n", "\n", "- ✅ **复杂推理**：数学计算、逻辑分析\n", "- ✅ **多步骤任务**：商业分析、项目规划\n", "- ✅ **创意工作**：写作、设计、策划\n", "- ✅ **专业分析**：法律、医学、技术评估\n", "\n", "---\n", "\n", "**💡 记住：给AI时间思考，就像给自己时间思考一样重要！**\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 4}
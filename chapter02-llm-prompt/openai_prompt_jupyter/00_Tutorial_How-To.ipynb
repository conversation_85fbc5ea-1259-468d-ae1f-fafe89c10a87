{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 教程使用指南\n", "\n", "本教程**需要API密钥**进行交互。如果您没有API密钥，可以通过[OpenAI Platform](https://platform.openai.com/)注册获取。如果您想使用DeepSeek模型，也可以通过[DeepSeek Platform](https://platform.deepseek.com/)获取API密钥。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 如何开始\n", "\n", "1. 将此仓库克隆到本地机器，按照Python、<PERSON>da和Ju<PERSON><PERSON>。\n", "\n", "2. 浏览器访问jupyter后，通过运行以下命令安装所需的依赖项：\n", " "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Collecting openai==1.61.0\n", "  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/93/76/70c5ad6612b3e4c89fa520266bbf2430a89cae8bd87c1e2284698af5927e/openai-1.61.0-py3-none-any.whl (460 kB)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.10.0)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (2.11.5)\n", "Requirement already satisfied: sniffio in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.67.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.14.0)\n", "Requirement already satisfied: idna>=2.8 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from anyio<5,>=3.5.0->openai==1.61.0) (3.10)\n", "Requirement already satisfied: certifi in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (2025.4.26)\n", "Requirement already satisfied: httpcore==1.* in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai==1.61.0) (0.16.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.4.1)\n", "Installing collected packages: openai\n", "  Attempting uninstall: openai\n", "    Found existing installation: openai 1.82.0\n", "    Uninstalling openai-1.82.0:\n", "      Successfully uninstalled openai-1.82.0\n", "Successfully installed openai-1.61.0\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n"]}], "source": ["# 安装OpenAI Python SDK\n", "%pip install openai==1.61.0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["3. 设置您的API密钥和模型名称。将`\"your_api_key_here\"`替换为您实际的OpenAI API密钥。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📋 配置检查中...\n", "✅ 配置验证通过\n", "✅ API配置已就绪\n", "🔧 OpenAI API 配置信息:\n", "  📡 配置来源: 环境变量 + 自定义API地址: https://vip.apiyi.com/v1\n", "  🤖 模型: gpt-4o\n", "  🌐 API地址: https://vip.apiyi.com/v1\n", "  🔑 API密钥: sk-R2utG...B944\n", "\n"]}], "source": ["# 🔧 OpenAI API 配置方式\n", "\n", "# 方式一：环境变量（推荐）\n", "# 在终端中设置，或者在 .env 文件中设置\n", "# export OPENAI_API_KEY=\"sk-your-api-key-here\"\n", "# export MODEL_NAME=\"gpt-4o\"\n", "# export OPENAI_BASE_URL=\"https://vip.apiyi.com/v1\"  # 可选：国内代理地址\n", "\n", "# 方式二：在此处直接设置（仅用于测试）\n", "# API_KEY = \"sk-your-api-key-here\"  # 请替换为您的实际OpenAI API密钥\n", "# MODEL_NAME = \"gpt-4o\"  # GPT-4o模型（推荐）或 \"deepseek-r1\"\n", "\n", "# 将API_KEY和MODEL_NAME变量存储在IPython存储中，以便在不同notebook中使用\n", "# %store API_KEY\n", "# %store MODEL_NAME\n", "\n", "# 配置验证和自动设置\n", "from config import setup_notebook_environment, print_config_info, validate_config\n", "\n", "# 如果还没设置过API密钥，请先配置\n", "print(\"📋 配置检查中...\")\n", "if not validate_config():\n", "    print(\"\"\"\n", "⚠️  需要配置API密钥！请选择以下方式之一：\n", "\n", "🌟 方式一：环境变量（推荐）\n", "# 在终端中运行：\n", "export OPENAI_API_KEY=\"sk-your-api-key-here\"\n", "\n", "# 或者使用项目根目录的脚本：\n", "./set_bashrc_env.sh OPENAI_API_KEY \"sk-your-api-key-here\"\n", "\n", "🔧 方式二：在notebook中设置\n", "API_KEY = \"sk-your-api-key-here\"\n", "MODEL_NAME = \"gpt-4o\"\n", "%store API_KEY\n", "%store MODEL_NAME\n", "\n", "📥 获取API密钥：\n", "- OpenAI官方: https://platform.openai.com/api-keys\n", "- DeepSeek: https://platform.deepseek.com/api-keys  \n", "- 国内代理: https://www.apiyi.com/register/?aff_code=we80\n", "    \"\"\")\n", "else:\n", "    print(\"✅ API配置已就绪\")\n", "    print_config_info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["4. 按顺序运行notebook单元格，遵循提供的说明。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "## 使用说明和提示 💡\n", "\n", "- 本课程使用GPT-4o，温度设置为0。我们将在课程后面详细讨论温度参数。现在，只需理解这些设置能产生更确定性的结果。本课程中的所有提示工程技术也适用于其他OpenAI模型，如GPT-4o-mini，以及DeepSeek的deepseek-r1等模型。\n", "\n", "- 您可以使用`Shift + Enter`执行单元格并移动到下一个。\n", "\n", "- 当您到达教程页面底部时，导航到文件夹中的下一个编号文件，或者如果您已完成该章节文件内的内容，则转到下一个编号文件夹。\n", "\n", "### OpenAI SDK和Chat Completions API\n", "我们将在整个教程中使用[OpenAI Python SDK](https://platform.openai.com/docs/libraries)和[Chat Completions API](https://platform.openai.com/docs/api-reference/chat)。\n", "\n", "下面是本教程中运行提示的示例。首先，我们创建`get_completion`，这是一个辅助函数，用于向GPT发送提示并返回GPT生成的响应。现在运行该单元格。"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ OpenAI环境设置完成!\n", "🔧 OpenAI API 配置信息:\n", "  📡 配置来源: 环境变量 + 自定义API地址: https://vip.apiyi.com/v1\n", "  🤖 模型: gpt-4o\n", "  🌐 API地址: https://vip.apiyi.com/v1\n", "  🔑 API密钥: sk-R2utG...B944\n", "\n", "🎉 环境设置完成！可以开始使用 get_completion 函数了。\n"]}], "source": ["# 🚀 自动设置OpenAI环境\n", "from config import setup_notebook_environment\n", "\n", "# 设置OpenAI客户端和get_completion函数\n", "# 此函数会自动处理环境变量和IPython存储的配置\n", "client, get_completion = setup_notebook_environment()\n", "\n", "# 现在您可以直接使用 get_completion 函数了！\n", "print(\"🎉 环境设置完成！可以开始使用 get_completion 函数了。\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在我们将为GPT编写一个示例提示，并通过运行我们的`get_completion`辅助函数来打印GPT的输出。运行下面的单元格将在其下方打印出GPT的响应。\n", "\n", "请随意修改提示字符串，以获得GPT的不同响应。"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello! How can I assist you today?\n"]}], "source": ["# 提示文本\n", "prompt = \"Hello, GPT!\"\n", "\n", "# 获取GPT回答\n", "print(get_completion(prompt))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["之前定义的`API_KEY`和`MODEL_NAME`变量将在整个教程中使用。请确保从上到下运行每个教程页面的单元格。"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 4}
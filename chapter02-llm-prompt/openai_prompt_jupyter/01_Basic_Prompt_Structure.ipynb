{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 第一章：基础提示结构\n", "\n", "## 设置\n", "\n", "运行以下设置单元格来加载您的API密钥并建立`get_completion`辅助函数。"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Requirement already satisfied: openai==1.61.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (1.61.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.10.0)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (2.11.5)\n", "Requirement already satisfied: sniffio in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.67.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.14.0)\n", "Requirement already satisfied: idna>=2.8 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from anyio<5,>=3.5.0->openai==1.61.0) (3.10)\n", "Requirement already satisfied: certifi in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (2025.4.26)\n", "Requirement already satisfied: httpcore==1.* in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai==1.61.0) (0.16.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.4.1)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n", "✅ OpenAI环境设置完成!\n", "🔧 OpenAI API 配置信息:\n", "  📡 配置来源: 环境变量 + 自定义API地址: https://vip.apiyi.com/v1\n", "  🤖 模型: gpt-4o\n", "  🌐 API地址: https://vip.apiyi.com/v1\n", "  🔑 API密钥: sk-R2utG...B944\n", "\n", "✅ 使用统一配置管理成功！\n"]}], "source": ["# 🔧 OpenAI环境自动配置\n", "# 此设置会自动从环境变量或IPython存储中加载配置\n", "\n", "# 安装OpenAI库\n", "%pip install openai==1.61.0\n", "\n", "# 导入Python内置的正则表达式库\n", "import re\n", "\n", "# 🚀 使用统一配置管理系统\n", "from config import setup_notebook_environment, print_config_info\n", "\n", "# 自动设置OpenAI客户端和get_completion函数\n", "# 优先级：环境变量 > IPython存储 > 默认值\n", "try:\n", "    client, get_completion = setup_notebook_environment()\n", "    print(\"✅ 使用统一配置管理成功！\")\n", "except Exception as e:\n", "    print(f\"❌ 统一配置失败，回退到传统方式: {e}\")\n", "    \n", "    # 回退到传统的配置方式\n", "    import openai\n", "    \n", "    # 从IPython存储中检索API_KEY和MODEL_NAME变量\n", "    %store -r API_KEY\n", "    %store -r MODEL_NAME\n", "\n", "    # 如果没有设置MODEL_NAME，使用默认值\n", "    try:\n", "        MODEL_NAME\n", "    except NameError:\n", "        MODEL_NAME = \"gpt-4o\"  # 默认使用gpt-4o模型\n", "\n", "    # 创建OpenAI客户端\n", "    client = openai.OpenAI(api_key=API_KEY)\n", "\n", "    def get_completion(prompt: str, system_prompt=\"\"):\n", "        \"\"\"\n", "        获取GPT的完成响应\n", "        \n", "        参数:\n", "            prompt (str): 用户提示\n", "            system_prompt (str): 系统提示（可选）\n", "        \n", "        返回:\n", "            str: GPT的响应文本\n", "        \"\"\"\n", "        # 构建消息列表\n", "        messages = []\n", "        \n", "        # 如果有系统提示，添加系统消息\n", "        if system_prompt:\n", "            messages.append({\"role\": \"system\", \"content\": system_prompt})\n", "        \n", "        # 添加用户消息\n", "        messages.append({\"role\": \"user\", \"content\": prompt})\n", "        \n", "        # 调用OpenAI API\n", "        response = client.chat.completions.create(\n", "            model=MODEL_NAME,              # 模型名称 (gpt-4o 或 deepseek-r1)\n", "            messages=messages,             # 消息列表\n", "            max_completion_tokens=2000,    # 最大token数\n", "            temperature=0.0               # 温度参数，0表示更确定性\n", "        )\n", "        return response.choices[0].message.content\n", "    \n", "    print(\"⚠️  使用传统配置方式，建议配置环境变量以获得更好体验\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "## 课程\n", "\n", "OpenAI提供了强大的[Chat Completions API](https://platform.openai.com/docs/api-reference/chat)来与GPT模型进行交互。在本教程中，我们将使用Chat Completions API。\n", "\n", "使用Chat Completions API调用GPT模型最少需要以下参数：\n", "- `model`：您要使用的模型名称，如`gpt-4o`或`deepseek-r1`\n", "\n", "- `max_tokens（或max_completion_tokens）`：停止前要生成的最大token数。请注意，GPT可能在达到这个最大值之前就停止。此参数仅指定要生成的绝对最大token数。此外，这是一个**硬停止**，意味着它可能导致GPT在单词或句子中间停止生成。\n", "\n", "- `messages`：输入消息列表<数组>。模型被训练为在交替的`user`和`assistant`对话轮次上操作。创建新对话时，您使用messages参数指定先前的对话轮次，然后模型生成对话中的下一个响应。\n", "  - 每个输入消息必须是一个包含`role`和`content`的对象。您可以指定单个`user`角色消息，或者可以包含多个`user`和`assistant`消息（如果是这样，它们必须交替）。第一条消息可以是`system`消息（用于系统提示），然后是`user`消息。\n", "\n", "还有可选参数，例如：\n", "- `system`消息：系统提示 - 下面会详细介绍。\n", "  \n", "- `temperature`：GPT响应的变化程度。对于这些课程和练习，我们将`temperature`设置为0。\n", "\n", "有关所有API参数的完整列表，请访问OpenAI的[API文档](https://platform.openai.com/docs/api-reference/chat)。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["以下是 `chat/completions` API 接口参数的中文翻译和整理，以表格形式呈现，方便大模型初学者理解：\n", "\n", "---\n", "\n", "### `chat/completions` API 请求体参数\n", "\n", "| 参数名称 | 类型 | 是否必填 | 默认值 | 描述 | 备注 (适合初学者) |\n", "|---|---|---|---|---|---|\n", "| **messages** | array | 是 | - | 包含到目前为止对话内容的**消息列表**。根据你使用的模型，支持不同的消息类型（模态），如文本、图像和音频。 | 这是最重要的参数！你需要在这里提供你想要模型回复的对话历史，比如用户的问题和模型之前的回复。 |\n", "| **model** | string | 是 | - | 用于生成回复的**模型 ID**，例如 `gpt-4o` 或 `o3`。OpenAI 提供了各种功能、性能和价格不同的模型。 | 选择一个你想要用来生成回复的模型。`gpt-4o` 是目前比较推荐的选择。 |\n", "| **audio** | object or null | 否 | - | 音频输出参数。当 `modalities` 包含 `\"audio\"` 时需要。 | 如果你希望模型除了文本还生成音频回复，你需要设置这个参数。目前只有 `gpt-4o-audio-preview` 模型支持。 |\n", "| **frequency_penalty** | number or null | 否 | 0 | 介于 -2.0 和 2.0 之间的数字。正值会根据新 token 在目前文本中的出现频率对其进行惩罚，降低模型逐字重复相同行的可能性。 | 调整这个值可以控制模型重复相同内容的倾向。值越高，模型越倾向于说新内容。 |\n", "| **function_call** | string or object | 否 | 见描述 | **已废弃**，请改用 `tool_choice`。控制模型调用哪个（如果有的话）函数。`none` 表示模型不会调用函数，而是生成消息。`auto` 表示模型可以自行选择生成消息或调用函数。通过 `{\"name\": \"my_function\"}` 指定特定函数会强制模型调用该函数。在没有函数时，`none` 是默认值。在有函数时，`auto` 是默认值。 | 这是一个旧的参数，如果你想让模型调用外部工具或函数，现在应该使用 `tool_choice` 和 `tools`。 |\n", "| **functions** | array | 否 | - | **已废弃**，请改用 `tools`。模型可能为其生成 JSON 输入的函数列表。 | 这是一个旧的参数，如果你想让模型调用外部工具或函数，现在应该使用 `tool_choice` 和 `tools`。 |\n", "| **logit_bias** | map | 否 | null | 修改指定 token 在补全中出现的可能性。接受一个 JSON 对象，该对象将 token（由其在分词器中的 token ID 指定）映射到 -100 到 100 之间的关联偏差值。 | 这是高级用法，可以用来“引导”模型输出某些特定的词语，或者避免某些词语。不建议初学者使用。 |\n", "| **logprobs** | boolean or null | 否 | false | 是否返回输出 token 的对数概率。如果为 true，则返回消息内容中每个输出 token 的对数概率。 | 对于分析模型输出的置信度很有用，但通常不是生成回复所必需的。 |\n", "| **max_completion_tokens** | integer or null | 否 | - | 可以为补全生成的 token 数量的上限，包括可见输出 token 和推理 token。 | 推荐使用的参数！用来控制模型回复的**最大长度**。设置一个合理的长度可以避免回复过长和节省成本。 |\n", "| **max_tokens** | integer or null | 否 | - | **已废弃**。聊天补全中可以生成的最大 token 数量。此值可用于控制通过 API 生成文本的成本。此值现已弃用，有利于 `max_completion_tokens`，并且与 `o-series` 模型不兼容。 | 这是旧的参数，现在推荐使用 `max_completion_tokens`。 |\n", "| **metadata** | map | 否 | - | 可以附加到对象的 16 个键值对集合。这对于以结构化格式存储对象的附加信息以及通过 API 或仪表板查询对象非常有用。 | 用于给你自己的请求添加一些额外信息，方便后续管理和查询。 |\n", "| **modalities** | array or null | 否 | `[\"text\"]` | 你希望模型生成的输出类型。大多数模型能够生成文本，这是默认值：`[\"text\"]`。`gpt-4o-audio-preview` 模型还可以用于生成音频。要请求此模型同时生成文本和音频回复，可以使用：`[\"text\", \"audio\"]`。 | 告诉模型你想要什么类型的输出，比如文本、音频。 |\n", "| **n** | integer or null | 否 | 1 | 为每个输入消息生成多少个聊天补全选项。请注意，你将根据所有选项生成的 token 数量付费。将 `n` 设为 1 可最大限度地降低成本。 | 通常设置为 1 就好，除非你需要模型提供多个不同的回复版本来选择。设置为 1 可以节省你的费用。 |\n", "| **parallel_tool_calls** | boolean | 否 | true | 在工具使用过程中是否启用并行函数调用。 | 当模型需要调用多个工具时，这个参数可以控制它们是否可以同时调用，提高效率。 |\n", "| **prediction** | object | 否 | - | 预测输出的配置，当模型回复的大部分内容已知时，可以大大缩短响应时间。这在你重新生成一个只有少量修改的文件时最常见。 | 高级优化功能，不建议初学者使用。 |\n", "| **presence_penalty** | number or null | 否 | 0 | 介于 -2.0 和 2.0 之间的数字。正值会根据新 token 是否出现在目前文本中对其进行惩罚，增加模型谈论新主题的可能性。 | 调整这个值可以控制模型是否倾向于引入新话题。值越高，模型越倾向于说新内容。 |\n", "| **reasoning_effort** | string or null | 否 | `medium` | 仅限 `o-series` 模型。限制推理模型上的推理工作量。目前支持的值为 `low`、`medium` 和 `high`。减少推理工作量可以缩短响应时间，并减少响应中用于推理的 token。 | 对于 `o-series` 模型，可以控制模型“思考”的深入程度。设置 `low` 可以加快速度，但可能牺牲一些质量。 |\n", "| **response_format** | object | 否 | - | 指定模型必须输出的格式的对象。设置为 `{ \"type\": \"json_schema\", \"json_schema\": {...} }` 可启用结构化输出，确保模型将匹配你提供的 JSON 模式。设置为 `{ \"type\": \"json_object\" }` 可启用旧的 JSON 模式，确保模型生成的消息是有效的 JSON。对于支持的模型，优先使用 `json_schema`。 | 如果你希望模型回复的数据是特定的 JSON 格式，可以使用这个参数。例如，你希望模型返回一个包含姓名和年龄的 JSON 对象。 |\n", "| **seed** | integer or null | 否 | - | **Beta 功能**。如果指定，我们的系统将尽最大努力确定性地采样，以便使用相同的种子和参数的重复请求应返回相同的结果。不保证确定性，你应该参考 `system_fingerprint` 响应参数来监控后端的变化。 | 如果你希望每次请求相同参数时模型都返回相同的回复（尽可能），可以使用这个参数。 |\n", "| **service_tier** | string or null | 否 | `auto` | 指定用于处理请求的处理类型。 | 这是一个高级参数，用于控制请求的服务层级，通常不需要修改。 |\n", "| **stop** | string / array / null | 否 | null | 最多 4 个序列，API 将在此处停止生成进一步的 token。返回的文本将不包含停止序列。 | 当模型生成到这些词语时，它就会停止生成。例如，如果你设置为 `[\"###\"]`，模型生成到 `###` 就会停下来。 |\n", "| **store** | boolean or null | 否 | false | 是否存储此聊天补全请求的输出，以供我们的模型蒸馏或评估产品使用。支持文本和图像输入。注意：超过 10MB 的图像输入将被丢弃。 | 通常设置为 `false`。如果你不希望 OpenAI 使用你的数据来改进模型，就不要开启。 |\n", "| **stream** | boolean or null | 否 | false | 如果设置为 `true`，模型响应数据将以服务器发送事件的形式流式传输到客户端。 | 如果你希望模型边生成边返回内容，而不是等全部生成完再返回，就设置为 `true`。这在构建实时聊天应用时非常有用。 |\n", "| **stream_options** | object or null | 否 | null | 流式响应的选项。仅在 `stream: true` 时设置。 | 配合 `stream: true` 使用，提供更多流式传输的控制选项。 |\n", "| **temperature** | number or null | 否 | 1 | 要使用的采样温度，介于 0 和 2 之间。像 0.8 这样的较高值会使输出更随机，而像 0.2 这样的较低值会使输出更集中和确定。通常建议更改此参数或 `top_p`，但不要同时更改两者。 | **非常重要且常用！** 控制模型回复的**创造性/随机性**。值越高，回复越有创意，但也可能更“奇怪”；值越低，回复越保守和确定。初学者可以尝试 0.7-1.0 之间的值。 |\n", "| **tool_choice** | string or object | 否 | 见描述 | 控制模型调用哪个（如果有的话）工具。`none` 表示模型不会调用任何工具，而是生成消息。`auto` 表示模型可以自行选择生成消息或调用一个或多个工具。`required` 表示模型必须调用一个或或多个工具。通过 `{\"type\": \"function\", \"function\": {\"name\": \"my_function\"}}` 指定特定工具会强制模型调用该工具。在没有工具时，`none` 是默认值。在有工具时，`auto` 是默认值。 | 这是用来让模型调用外部工具（如搜索、计算器等）的关键参数。`auto` 是常用的设置，让模型自己决定是否调用。 |\n", "| **tools** | array | 否 | - | 模型可能调用的工具列表。目前，只有函数支持作为工具。使用此参数提供模型可能为其生成 JSON 输入的函数列表。最多支持 128 个函数。 | 当你希望模型能够使用一些“工具”来帮助它完成任务时，你需要在这里定义这些工具（比如你的自定义函数）。 |\n", "| **top_logprobs** | integer or null | 否 | - | 一个介于 0 和 20 之间的整数，指定在每个 token 位置返回最可能的 token 数量，每个 token 都带有一个关联的对数概率。如果使用此参数，`logprobs` 必须设置为 true。 | 配合 `logprobs` 使用，提供更多关于模型预测下一个词的细节。 |\n", "| **top_p** | number or null | 否 | 1 | 采样温度的替代方法，称为核采样，模型考虑具有 `top_p` 概率质量的 token 结果。因此 0.1 表示只考虑占前 10% 概率质量的 token。通常建议更改此参数或 `temperature`，但不要同时更改两者。 | 另一个控制模型随机性的参数，与 `temperature` 类似。通常建议只调整其中一个，而不是两个都调。 |\n", "| **user** | string | 否 | - | 你的最终用户的稳定标识符。用于通过更好地对相似请求进行分类来提高缓存命中率，并帮助 OpenAI 检测和防止滥用。 | 可以用来标识你的用户，有助于 OpenAI 进行使用分析和防止滥用。 |\n", "| **web_search_options** | object | 否 | - | 此工具会搜索网络以获取相关结果，用于生成回复。了解更多关于网络搜索工具的信息。 | 如果你希望模型能够“上网”获取最新信息来回答问题，可以使用这个参数来配置网络搜索。 |\n", "\n", "---\n", "\n", "#### 数据类型\n", "\n", "以下是编程中常见的数据类型，用于在程序代码中定义变量的类型：\n", "\n", "* **字符串 (String)**：表示文本数据，由字符序列组成，例如 `\"Hello, World!\"`。\n", "* **整数 (Integer)**：表示没有小数部分的整数，例如 `10`, `-5`, `0`。\n", "* **浮点数 (Float)**：表示带有小数部分的数字，例如 `3.14`, `-0.5`, `2.0`。\n", "* **布尔值 (Boolean)**：表示真或假两种状态，只有 `True` 或 `False`。\n", "* **列表 (List)**：有序、可变的数据集合，可以包含不同类型的数据，例如 `[1, \"apple\", 3.14]`。\n", "* **元组 (<PERSON><PERSON>)**：有序、不可变的数据集合，一旦创建就不能修改，例如 `(1, \"banana\", 2.71)`。\n", "* **字典 (Dictionary)**：无序的键值对集合，通过唯一的键来访问值，例如 `{\"name\": \"<PERSON>\", \"age\": 30}`。\n", "* **集合 (Set)**：无序、不重复的元素集合，例如 `{1, 2, 3}`。\n", "* **空值 (NoneType)**：表示一个空值或没有值，通常用 `None` 表示。\n", "* **字节 (Bytes)**：表示不可变的字节序列，通常用于处理二进制数据，例如 `b\"hello\"`。\n", "* **字节数组 (Bytearray)**：表示可变的字节序列，类似于字节，但内容可以修改。\n", "* **内存视图 (MemoryView)**：允许你访问一个对象的内部数据而无需复制它，通常用于高效处理大数据。\n", "* **复数 (Complex)**：表示带有实部和虚部的数字，例如 `1 + 2j`。\n", "* **范围 (Range)**：表示一个不可变的数字序列，通常用于循环，例如 `range(0, 5)`。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 示例\n", "\n", "让我们看看GPT如何响应一些格式正确的提示。对于以下每个单元格，运行单元格（`shift+enter`），GPT的响应将出现在代码块下方。"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["你好！我很好，谢谢你。有什么我可以帮助你的吗？\n"]}], "source": ["# 提示\n", "PROMPT = \"你好GPT，你好吗？\"\n", "\n", "# 显示GPT回答\n", "print(get_completion(PROMPT))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["海洋的颜色主要是蓝色，但它可以根据不同的因素呈现出各种不同的色调。海水的颜色受阳光的照射、海水的深度、海底的成分以及水中的浮游生物和悬浮颗粒的影响。浅水区域可能呈现出绿色或青绿色，因为阳光能够穿透水面并被海底的植物或沙子反射。而在深海区域，水通常显得更蓝，因为水分子吸收了阳光中的红色和黄色光谱，只剩下蓝色光谱被反射出来。此外，海水中含有大量浮游生物时，可能会呈现出绿色或其他颜色。\n"]}], "source": ["# 提示\n", "PROMPT = \"你能告诉我海洋的颜色吗？\"\n", "\n", "# 显示GPT回答\n", "print(get_completion(PROMPT))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["席琳·迪翁（Céline Dion）出生于1968年。\n"]}], "source": ["# 提示\n", "PROMPT = \"席琳·迪翁出生于哪一年？\"\n", "\n", "# 显示GPT回答\n", "print(get_completion(PROMPT))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["现在让我们看看一些不包含正确Chat Completions API格式的提示。对于这些格式错误的提示，OpenAI API会返回错误。\n", "\n", "首先，我们有一个Chat Completions API调用示例，在`messages`数组中缺少`role`和`content`字段。"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["API调用错误: Object of type set is not JSON serializable\n", "错误原因: 消息缺少必需的'role'和'content'字段\n"]}], "source": ["# 获取GPT回答（错误格式示例）\n", "MODEL_NAME = \"gpt-4o\" \n", "try:\n", "    response = client.chat.completions.create(\n", "        model=MODEL_NAME,\n", "        max_tokens=2000,\n", "        temperature=0.0,\n", "        messages=[\n", "          {\"你好GPT，你好吗？\"}  # 缺少role和content字段\n", "        ]\n", "    )\n", "    # 显示GPT回答\n", "    print(response.choices[0].message.content)\n", "except Exception as e:\n", "    print(f\"API调用错误: {e}\")\n", "    print(\"错误原因: 消息缺少必需的'role'和'content'字段\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["以下是一个两个连续的user消息示例"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["席琳·迪翁出生于1968年3月30日。她是一位来自加拿大的著名歌手，以其强大的嗓音和广泛的音乐风格而闻名。以下是一些关于她的其他事实：\n", "\n", "1. **早期成名**：席琳·迪翁在12岁时录制了她的第一张法语专辑，并迅速在加拿大和法国获得了关注。\n", "\n", "2. **国际突破**：她在1990年代通过英语专辑《Unison》进入国际市场，并凭借《The Colour of My Love》和《Falling into You》等专辑获得全球成功。\n", "\n", "3. **《我心永恒》**：她演唱的《My Heart Will Go On》是电影《泰坦尼克号》的主题曲，这首歌成为她最著名的作品之一，并赢得了多项奖项。\n", "\n", "4. **奖项和荣誉**：席琳·迪翁获得了多项格莱美奖、朱诺奖和其他音乐奖项，巩固了她作为全球顶级歌手的地位。\n", "\n", "5. **拉斯维加斯驻唱**：她在拉斯维加斯进行了长期驻唱表演，吸引了大量观众，并成为拉斯维加斯演出史上最成功的艺人之一。\n", "\n", "6. **个人生活**：席琳·迪翁与她的经理人雷内·安杰利尔结婚，并育有三个孩子。雷内于2016年去世。\n", "\n", "7. **慈善事业**：她积极参与慈善活动，支持多个儿童和健康相关的组织。\n", "\n", "席琳·迪翁以其音乐才华和个人魅力赢得了全球观众的喜爱，成为音乐界的传奇人物。\n"]}], "source": ["# 获取GPT回答（两个连续的user消息示例）\n", "MODEL_NAME = \"gpt-4o\" \n", "try:\n", "    response = client.chat.completions.create(\n", "        model=MODEL_NAME,\n", "        max_tokens=2000,\n", "        temperature=0.0,\n", "        messages=[\n", "          {\"role\": \"user\", \"content\": \"席琳·迪翁出生于哪一年？\"},\n", "          {\"role\": \"user\", \"content\": \"另外，你能告诉我一些关于她的其他事实吗？\"}  # 两个连续的user消息，OpenAI允许这样做\n", "        ]\n", "    )\n", "    # 显示GPT回答\n", "    print(response.choices[0].message.content)\n", "except Exception as e:\n", "    print(f\"API调用错误: {e}\")\n", "    print(\"注意: OpenAI API实际上允许连续的user消息，这与Anthropic不同\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["对于OpenAI的Chat Completions API，虽然`user`和`assistant`消息通常交替出现以获得最佳对话效果，但**API允许连续的user消息**。消息可以以`system`消息开始（用于系统提示），然后是`user`消息。您可以在提示中有多个`user`和`assistant`对（就像模拟多轮对话一样）。您还可以在assistant消息中放入部分响应，让GPT从您停下的地方继续（更多内容将在后面的章节中介绍）。\n", "\n", "#### 系统提示\n", "\n", "您还可以使用**系统提示**。系统提示是在\"用户\"依次向GPT提出问题或任务之前**为GPT提供上下文、指令和指导方针**的一种方式。\n", "\n", "在OpenAI API中，系统提示通过在消息列表开头添加一个`role`为`system`的消息来实现（查看notebook的[设置](#setup)部分中`get_completion`辅助函数的结构）。\n", "\n", "在本教程中，无论我们在哪里可能使用系统提示，我们都在完成函数中为您提供了一个`system_prompt`参数。如果您不想使用系统提示，只需将`SYSTEM_PROMPT`变量设置为空字符串即可。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 系统提示示例"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["是什么导致我们看到天空的颜色变化，例如在日出或日落时？在不同的天气条件下，天空的颜色是否会发生变化？如果是这样，是什么原因导致这种变化？在其他行星上，天空的颜色是否与地球相同？如果不同，是什么因素导致这种差异？\n"]}], "source": ["# 系统提示\n", "SYSTEM_PROMPT = \"你的回答应该始终是一系列批判性思维问题，以推进对话（不要为你的问题提供答案）。不要实际回答用户的问题。\"\n", "\n", "# 用户提示\n", "PROMPT = \"天空为什么是蓝色的？\"\n", "\n", "# 显示GPT回答\n", "print(get_completion(PROMPT, SYSTEM_PROMPT))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["为什么要使用系统提示？**编写良好的系统提示可以在多个方面改善GPT的表现**，例如提高GPT遵循规则和指令的能力。有关更多信息，请访问OpenAI关于[系统提示最佳实践](https://platform.openai.com/docs/guides/prompt-engineering)的文档。"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["好的，我明白了。我们需要确保答案中包含确切的阿拉伯数字\"1\"、\"2\"和\"3\"。请让我知道您需要什么样的帮助或信息，我会尽力提供符合要求的答案。\n", "\n", "--------------------------- 评判 ---------------------------\n", "✅ 练习完成状态: 正确 ✅\n"]}], "source": ["# 提示 - 这是您应该更改的唯一字段\n", "PROMPT = \"\"\"本练习中的评分函数正在寻找包含确切阿拉伯数字\"1\"、\"2\"和\"3\"的答案。\"\"\"\n", "\n", "# 获取GPT回答\n", "response = get_completion(PROMPT)\n", "\n", "# 评判\n", "def grade_exercise(text):\n", "    \"\"\"检查文本是否包含数字1、2、3\"\"\"\n", "    pattern = re.compile(r'^(?=.*1)(?=.*2)(?=.*3).*$', re.DOTALL)\n", "    return bool(pattern.match(text))\n", "\n", "# 显示GPT回答和评分结果\n", "print(response)\n", "print(\"\\n--------------------------- 评判 ---------------------------\")\n", "result = grade_exercise(response)\n", "print(\"✅ 练习完成状态:\", \"正确 ✅\" if result else \"需要调整 ❌\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["❓ 如果您需要提示，请运行下面的单元格！"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["本练习中的评分函数正在寻找包含确切阿拉伯数字\"1\"、\"2\"和\"3\"的答案。\n", "您通常可以通过简单地要求GPT做您想要的事情来实现目标。\n"]}], "source": ["# 导入并显示练习1.1的提示\n", "from hints import exercise_1_1_hint; print(exercise_1_1_hint)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["天空的大小实际上是一个哲学和科学上的问题，因为天空并没有明确的边界。我们通常所说的天空是指地球大气层的可见部分，从地面一直延伸到太空。大气层的厚度约为100公里，但天空的视觉效果可以延伸到宇宙的深处。\n", "\n", "从科学的角度来看，天空的大小可以被认为是无限的，因为它是宇宙的一部分，而宇宙本身是无边无际的。宇宙的可观测部分直径约为930亿光年，但这只是我们能够观测到的范围，实际的宇宙可能更大。\n", "\n", "因此，天空的大小取决于我们如何定义它：作为地球大气的一部分，它是有限的；作为宇宙的一部分，它是无限的。\n", "\n", "==================== 练习评分 ====================\n", "✅ 练习完成状态: 需要调整 ❌\n"]}], "source": ["# 系统提示 - 这是您应该更改的唯一字段\n", "SYSTEM_PROMPT = \"[请替换此文本]\"\n", "\n", "# 用户提示\n", "PROMPT = \"天空有多大？\"\n", "\n", "# 获取GPT回答\n", "response = get_completion(PROMPT, SYSTEM_PROMPT)\n", "\n", "# 检查练习答案是否正确\n", "def grade_exercise(text):\n", "    \"\"\"检查响应是否包含像3岁小孩一样的表达方式\"\"\"\n", "    return bool(re.search(r\"咯咯笑\", text) or re.search(r\"好大好大\", text) or re.search(r\"哇\", text))\n", "\n", "# 显示GPT回答和评分结果\n", "print(response)\n", "print(\"\\n==================== 练习评分 ====================\")\n", "result = grade_exercise(response)\n", "print(\"✅ 练习完成状态:\", \"正确 ✅\" if result else \"需要调整 ❌\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["❓ 如果您需要提示，请运行下面的单元格！"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["本练习中的评分函数正在寻找包含\"咯咯笑\"、\"好大好大\"或\"哇\"的答案。\n", "有很多方法可以解决这个问题，只需要提出要求！\n"]}], "source": ["# 导入并显示练习1.2的提示\n", "from hints import exercise_1_2_hint; print(exercise_1_2_hint)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["你好！我很好，谢谢你。有什么我可以帮助你的吗？\n"]}], "source": ["# 提示\n", "PROMPT = \"你好GPT，你好吗？\"\n", "\n", "# 显示GPT回答\n", "print(get_completion(PROMPT))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["海洋的颜色主要取决于几个因素，包括阳光的照射、海水的深度、海水中的颗粒物和生物等。通常情况下，海洋呈现蓝色，这是因为水分子吸收了阳光中的红色和橙色光波，而散射了蓝色光波。此外，海洋中的浮游生物和矿物质也会影响海水的颜色，使其在某些区域呈现绿色、灰色或其他颜色。在浅水区域，海底的颜色也可能影响海水的颜色，使其看起来更浅或更深。\n"]}], "source": ["# 提示\n", "PROMPT = \"你能告诉我海洋的颜色吗？\"\n", "\n", "# 显示GPT回答\n", "print(get_completion(PROMPT))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["席琳·迪翁出生于1968年。\n"]}], "source": ["# 提示\n", "PROMPT = \"席琳·迪翁出生于哪一年？\"\n", "\n", "# 显示GPT回答\n", "print(get_completion(PROMPT))"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["API调用错误: Object of type set is not JSON serializable\n", "错误原因: 消息缺少必需的'role'和'content'字段\n"]}], "source": ["# 获取GPT回答（错误格式示例）\n", "try:\n", "    response = client.chat.completions.create(\n", "        model=MODEL_NAME,\n", "        max_tokens=2000,\n", "        temperature=0.0,\n", "        messages=[\n", "          {\"你好GPT，你好吗？\"}  # 缺少role和content字段\n", "        ]\n", "    )\n", "    # 显示GPT回答\n", "    print(response.choices[0].message.content)\n", "except Exception as e:\n", "    print(f\"API调用错误: {e}\")\n", "    print(\"错误原因: 消息缺少必需的'role'和'content'字段\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["席琳·迪翁出生于1968年3月30日。她是一位来自加拿大的著名歌手，以其强大的嗓音和广泛的音乐风格而闻名。以下是一些关于她的其他事实：\n", "\n", "1. **早期成就**：席琳·迪翁在12岁时就录制了她的第一张法语专辑，并迅速在加拿大和法国获得了成功。\n", "\n", "2. **国际突破**：她在1990年代初通过发行英语专辑《Unison》进入国际市场，并凭借《The Power of Love》、《Because You Loved Me》、《My Heart Will Go On》等热门歌曲成为全球知名的流行歌手。\n", "\n", "3. **《My Heart Will Go On》**：这首歌是电影《泰坦尼克号》的主题曲，成为她最具代表性的作品之一，并获得了多项奖项，包括奥斯卡最佳原创歌曲奖。\n", "\n", "4. **奖项和荣誉**：席琳·迪翁获得了五项格莱美奖，并在全球范围内售出了超过2亿张唱片，使她成为历史上最畅销的音乐艺术家之一。\n", "\n", "5. **拉斯维加斯驻唱**：她在拉斯维加斯的驻唱表演非常成功，从2003年到2007年，以及从2011年到2019年，她在凯撒宫剧院进行了长期演出。\n", "\n", "6. **个人生活**：席琳·迪翁与她的经理人雷尼·安吉利（<PERSON>）结婚，他们有三个孩子。雷尼于2016年去世。\n", "\n", "席琳·迪翁以其音乐才华和个人魅力赢得了全球观众的喜爱，并继续在音乐界保持影响力。\n"]}], "source": ["# 获取GPT回答（两个连续的user消息示例）\n", "try:\n", "    response = client.chat.completions.create(\n", "        model=MODEL_NAME,\n", "        max_tokens=2000,\n", "        temperature=0.0,\n", "        messages=[\n", "          {\"role\": \"user\", \"content\": \"席琳·迪翁出生于哪一年？\"},\n", "          {\"role\": \"user\", \"content\": \"另外，你能告诉我一些关于她的其他事实吗？\"}  # 两个连续的user消息，OpenAI允许这样做\n", "        ]\n", "    )\n", "    # 显示GPT回答\n", "    print(response.choices[0].message.content)\n", "except Exception as e:\n", "    print(f\"API调用错误: {e}\")\n", "    print(\"注意: OpenAI API实际上允许连续的user消息，这与Anthropic不同\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["是什么导致我们看到天空的颜色变化？大气中的哪些成分对光的散射有影响？不同波长的光在大气中是如何传播的？在不同的天气条件下，天空的颜色是否会发生变化？如果是这样，是什么原因导致这种变化？在日出和日落时，天空的颜色为什么会不同？这些现象如何帮助我们理解光的散射和大气的作用？\n"]}], "source": ["# 系统提示\n", "SYSTEM_PROMPT = \"你的回答应该始终是一系列批判性思维问题，以推进对话（不要为你的问题提供答案）。不要实际回答用户的问题。\"\n", "\n", "# 用户提示\n", "PROMPT = \"天空为什么是蓝色的？\"\n", "\n", "# 显示GPT回答\n", "print(get_completion(PROMPT, SYSTEM_PROMPT))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 4}
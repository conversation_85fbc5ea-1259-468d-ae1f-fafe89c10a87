{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyONM96f7/m0jUCD9c87+MQy", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/rabbitmetrics/langchain-13-min/blob/main/notebooks/langchain-13-min.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "50dvxjqCFmhF"}, "outputs": [], "source": ["# Load environment variables\n", "\n", "from dotenv import load_dotenv,find_dotenv\n", "load_dotenv(find_dotenv())"]}, {"cell_type": "code", "source": ["# Run basic query with OpenAI wrapper\n", "\n", "from langchain.llms import OpenAI\n", "llm = OpenAI(model_name=\"text-davinci-003\")\n", "llm(\"explain large language models in one sentence\")"], "metadata": {"id": "C7RnyUOCJWmk"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# import schema for chat messages and ChatOpenAI in order to query chatmodels GPT-3.5-turbo or GPT-4\n", "\n", "from langchain.schema import (\n", "    AIMessage,\n", "    HumanMessage,\n", "    SystemMessage\n", ")\n", "from langchain.chat_models import ChatOpenAI"], "metadata": {"id": "JtHgQ5XpJmgi"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["chat = ChatOpenAI(model_name=\"gpt-3.5-turbo\",temperature=0.3)\n", "messages = [\n", "    SystemMessage(content=\"You are an expert data scientist\"),\n", "    HumanMessage(content=\"Write a Python script that trains a neural network on simulated data \")\n", "]\n", "response=chat(messages)\n", "\n", "print(response.content,end='\\n')"], "metadata": {"id": "yrfYfKfdJyyF"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Import prompt and define PromptTemplate\n", "\n", "from langchain import PromptTemplate\n", "\n", "template = \"\"\"\n", "You are an expert data scientist with an expertise in building deep learning models. \n", "Explain the concept of {concept} in a couple of lines\n", "\"\"\"\n", "\n", "prompt = PromptTemplate(\n", "    input_variables=[\"concept\"],\n", "    template=template,\n", ")"], "metadata": {"id": "2grf7I8AJ_hK"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Run LLM with PromptTemplate\n", "\n", "llm(prompt.format(concept=\"autoencoder\"))"], "metadata": {"id": "vcz7Q9Y-KFvI"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Import LLMChain and define chain with language model and prompt as arguments.\n", "\n", "from langchain.chains import LLMChain\n", "chain = LLMChain(llm=llm, prompt=prompt)\n", "\n", "# Run the chain only specifying the input variable.\n", "print(chain.run(\"autoencoder\"))"], "metadata": {"id": "dm78i-rUKXIB"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Define a second prompt \n", "\n", "second_prompt = PromptTemplate(\n", "    input_variables=[\"ml_concept\"],\n", "    template=\"Turn the concept description of {ml_concept} and explain it to me like I'm five in 500 words\",\n", ")\n", "chain_two = LLMChain(llm=llm, prompt=second_prompt)"], "metadata": {"id": "B6MF4-nMKul3"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Define a sequential chain using the two chains above: the second chain takes the output of the first chain as input\n", "\n", "from langchain.chains import SimpleSequentialChain\n", "overall_chain = SimpleSequentialChain(chains=[chain, chain_two], verbose=True)\n", "\n", "# Run the chain specifying only the input variable for the first chain.\n", "explanation = overall_chain.run(\"autoencoder\")\n", "print(explanation)"], "metadata": {"id": "SkJKFyk1K-MO"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Import utility for splitting up texts and split up the explanation given above into document chunks\n", "\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "\n", "text_splitter = RecursiveCharacterTextSplitter(\n", "    chunk_size = 100,\n", "    chunk_overlap  = 0,\n", ")\n", "\n", "texts = text_splitter.create_documents([explanation])\n"], "metadata": {"id": "mDDu1B_SLQls"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Individual text chunks can be accessed with \"page_content\"\n", "\n", "texts[0].page_content"], "metadata": {"id": "F6lfAdeuLhtp"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Import and instantiate OpenAI embeddings\n", "\n", "from langchain.embeddings import OpenAIEmbeddings\n", "\n", "embeddings = OpenAIEmbeddings(model_name=\"ada\")"], "metadata": {"id": "Z5sv4e3tLw2y"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Turn the first text chunk into a vector with the embedding\n", "\n", "query_result = embeddings.embed_query(texts[0].page_content)\n", "print(query_result)"], "metadata": {"id": "dqzoir4hMlfl"}, "execution_count": 1, "outputs": []}, {"cell_type": "code", "source": ["# Import and initialize Pinecone client\n", "\n", "import os\n", "import pinecone\n", "from langchain.vectorstores import Pinecone\n", "\n", "\n", "pinecone.init(\n", "    api_key=os.getenv('PINECONE_API_KEY'),  \n", "    environment=os.getenv('PINECONE_ENV')  \n", ")"], "metadata": {"id": "QaOY5bIZM3Xz"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Upload vectors to Pinecone\n", "\n", "index_name = \"langchain-quickstart\"\n", "search = Pinecone.from_documents(texts, embeddings, index_name=index_name)"], "metadata": {"id": "lZhSUt3FNBzN"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Do a simple vector similarity search\n", "\n", "query = \"What is magical about an autoencoder?\"\n", "result = search.similarity_search(query)\n", "\n", "print(result)"], "metadata": {"id": "cCXVuXwPNKcc"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Import Python REPL tool and instantiate Python agent\n", "\n", "from langchain.agents.agent_toolkits import create_python_agent\n", "from langchain.tools.python.tool import PythonREPLTool\n", "from langchain.python import PythonREPL\n", "from langchain.llms.openai import OpenAI\n", "\n", "agent_executor = create_python_agent(\n", "    llm=OpenAI(temperature=0, max_tokens=1000),\n", "    tool=PythonREPLTool(),\n", "    verbose=True\n", ")"], "metadata": {"id": "4Ogz8luZNRnJ"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Execute the Python agent\n", "\n", "agent_executor.run(\"Find the roots (zeros) if the quadratic function 3 * x**2 + 2*x -1\")"], "metadata": {"id": "BVHMDj0sNi09"}, "execution_count": null, "outputs": []}]}
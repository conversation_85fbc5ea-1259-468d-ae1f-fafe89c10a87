n8n 开源自动化生态全景图，聚焦社区前100热门节点，构建高效工作流的必备资源库

• 2515+ 社区节点，涵盖通信、文档生成、浏览器自动化、数据处理、API 集成、AI 语音、文件处理等八大类，持续高速增长，平均每日新增14.5个节点。  
• 通信与消息节点支持 WhatsApp、Zalo、Discord、ChatWoot 等主流渠道，月下载量最高节点突破190万，助力多渠道消息自动化。  
• 文档与内容生成节点支持动态文档、二维码、Notion 转 Markdown、AI PDF 生成，提升内容创作效率。  
• 浏览器自动化与网络爬虫节点集成 Puppeteer、Playwright、ScrapeNinja，简化网页数据抓取与自动操作。  
• 数据处理节点覆盖文本处理、OCR、加密解密、数据验证等多样功能，保障数据质量与安全。  
• API 与云服务节点涵盖 Asaas、Apify、Brave Search、Kommo、Binance、TikTok、Power BI 等主流平台，打通业务系统边界。  
• AI、LLM 及语音节点紧跟前沿，支持 ElevenLabs 语音合成、Perplexity AI、AI 图像生成等，赋能智能化自动化。  
• 文件与 PDF 节点强力支持图片转 PDF、视频编辑等多媒体处理，扩展工作流应用场景。  
• 社区维护活跃，节奏快，数据实时更新，适合长期参考与二次开发，助力构建可持续的自动化体系。  

n8n 生态通过开放社区节点的持续迭代，打造了一个灵活、可扩展的自动化底座，赋能开发者和企业实现端到端的数字化转型。

https://github.com/restyler/awesome-n8n

## 参考
- [awesome-n8n-templates](https://github.com/enescingoz/awesome-n8n-templates)
- [n8n-free-templates](https://github.com/FlyAIBox/n8n-free-templates)
- [n8n-mcp](https://github.com/czlonkowski/n8n-mcp)
- [n8n-agent](https://github.com/FlyAIBox/ai_agents_az)
- [awesome-n8n](https://github.com/restyler/awesome-n8n)
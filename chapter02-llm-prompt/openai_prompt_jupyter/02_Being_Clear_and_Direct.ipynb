{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 第二章：明确和直接\n", "\n", "## 设置\n", "\n", "运行以下设置单元格来加载您的API密钥并建立`get_completion`辅助函数。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Requirement already satisfied: openai==1.61.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (1.61.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.10.0)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (2.11.5)\n", "Requirement already satisfied: sniffio in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.67.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.14.0)\n", "Requirement already satisfied: idna>=2.8 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from anyio<5,>=3.5.0->openai==1.61.0) (3.10)\n", "Requirement already satisfied: certifi in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (2025.4.26)\n", "Requirement already satisfied: httpcore==1.* in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai==1.61.0) (0.16.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.4.1)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n", "Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Requirement already satisfied: openai==1.61.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (1.61.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.10.0)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (2.11.5)\n", "Requirement already satisfied: sniffio in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.67.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.14.0)\n", "Requirement already satisfied: idna>=2.8 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from anyio<5,>=3.5.0->openai==1.61.0) (3.10)\n", "Requirement already satisfied: certifi in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (2025.4.26)\n", "Requirement already satisfied: httpcore==1.* in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai==1.61.0) (0.16.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.4.1)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n", "✅ OpenAI环境设置完成!\n", "🔧 OpenAI API 配置信息:\n", "  📡 配置来源: 环境变量 + 自定义API地址: https://vip.apiyi.com/v1\n", "  🤖 模型: gpt-4o\n", "  🌐 API地址: https://vip.apiyi.com/v1\n", "  🔑 API密钥: sk-R2utG...B944\n", "\n", "✅ 使用统一配置管理成功！\n"]}], "source": ["# 安装OpenAI库\n", "%pip install openai==1.61.0\n", "\n", "# 🔧 OpenAI环境自动配置\n", "# 此设置会自动从环境变量或IPython存储中加载配置\n", "\n", "# 安装OpenAI库\n", "%pip install openai==1.61.0\n", "\n", "# 导入Python内置的正则表达式库\n", "import re\n", "\n", "# 🚀 使用统一配置管理系统\n", "from config import setup_notebook_environment, print_config_info\n", "\n", "# 自动设置OpenAI客户端和get_completion函数\n", "# 优先级：环境变量 > IPython存储 > 默认值\n", "try:\n", "    client, get_completion = setup_notebook_environment()\n", "    print(\"✅ 使用统一配置管理成功！\")\n", "except Exception as e:\n", "    print(f\"❌ 统一配置失败，回退到传统方式: {e}\")\n", "    \n", "    # 回退到传统的配置方式\n", "    import openai\n", "    \n", "    # 从IPython存储中检索API_KEY和MODEL_NAME变量\n", "    %store -r API_KEY\n", "    %store -r MODEL_NAME\n", "\n", "    # 如果没有设置MODEL_NAME，使用默认值\n", "    try:\n", "        MODEL_NAME\n", "    except NameError:\n", "        MODEL_NAME = \"gpt-4o\"  # 默认使用gpt-4o模型\n", "\n", "    # 创建OpenAI客户端\n", "    client = openai.OpenAI(api_key=API_KEY)\n", "\n", "    def get_completion(prompt: str, system_prompt=\"\"):\n", "        \"\"\"\n", "        获取GPT的完成响应\n", "        \n", "        参数:\n", "            prompt (str): 用户提示\n", "            system_prompt (str): 系统提示（可选）\n", "        \n", "        返回:\n", "            str: GPT的响应文本\n", "        \"\"\"\n", "        # 构建消息列表\n", "        messages = []\n", "        \n", "        # 如果有系统提示，添加系统消息\n", "        if system_prompt:\n", "            messages.append({\"role\": \"system\", \"content\": system_prompt})\n", "        \n", "        # 添加用户消息\n", "        messages.append({\"role\": \"user\", \"content\": prompt})\n", "        \n", "        # 调用OpenAI API\n", "        response = client.chat.completions.create(\n", "            model=MODEL_NAME,              # 模型名称 (gpt-4o 或 deepseek-r1)\n", "            messages=messages,             # 消息列表\n", "            max_completion_tokens=2000,    # 最大token数\n", "            temperature=0.0               # 温度参数，0表示更确定性\n", "        )\n", "        return response.choices[0].message.content\n", "    \n", "    print(\"⚠️  使用传统配置方式，建议配置环境变量以获得更好体验\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "## 课程 \n", "\n", "**GPT对清晰直接的指令响应最佳。**\n", "\n", "把GPT想象成任何一个刚入职的人。除了您直接告诉它的内容外，**GPT对该做什么没有任何上下文**。就像您第一次指导人类完成任务一样，您越是以直接的方式向GPT准确解释您想要什么，GPT的响应就会越好、越准确。\"\t\t\t\t\n", "\t\t\t\t\n", "当有疑问时，遵循**清晰提示的黄金法则**：\n", "- 将您的提示展示给同事或朋友，让他们自己按照指令操作，看看他们能否产生您想要的结果。如果他们感到困惑，GPT也会困惑。\t\t\t\t"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 示例\n", "\n", "让我们通过几个对比鲜明的例子来演示明确和直接的重要性。\n", "\n", "#### 示例1：模糊 vs 明确的任务指令"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["模糊提示的回答:\n", "当然可以！请提供您需要处理的数据，并告诉我您希望如何处理它。无论是数据清理、分析还是可视化，我都会尽力帮助您。\n", "\n"]}], "source": ["# 模糊的提示\n", "PROMPT = \"帮我处理一下这个数据\"\n", "\n", "# 显示GPT回答\n", "print(\"模糊提示的回答:\")\n", "print(get_completion(PROMPT))\n", "print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["可以看到，GPT无法理解\"处理数据\"具体指什么，只能给出通用的回答。\n", "\n", "现在让我们提供一个**明确和直接**的指令："]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["明确提示的回答:\n", "为了计算每个月的同比增长率，我们需要假设去年每个月的销售额。由于没有提供去年的数据，我将假设去年每个月的销售额与今年相同，以便展示表格格式和计算方法。\n", "\n", "假设去年每个月的销售额如下：\n", "- 去年1月：100万\n", "- 去年2月：120万\n", "- 去年3月：110万\n", "- 去年4月：130万\n", "\n", "根据这个假设，计算每个月的同比增长率：\n", "\n", "| 月份 | 销售额 (万元) | 同比增长率 |\n", "|------|--------------|------------|\n", "| 1月  | 100          | 0.00%      |\n", "| 2月  | 120          | 0.00%      |\n", "| 3月  | 110          | 0.00%      |\n", "| 4月  | 130          | 0.00%      |\n", "\n", "如果有去年的实际数据，请提供，以便进行准确的同比增长率计算。否则，以上表格仅用于展示格式。\n"]}], "source": ["# 明确和直接的提示\n", "PROMPT = \"\"\"分析以下销售数据，计算每个月的同比增长率：\n", "1月：100万\n", "2月：120万\n", "3月：110万\n", "4月：130万\n", "\n", "要求：\n", "- 以表格形式输出结果\n", "- 包含月份、销售额、同比增长率三列\n", "- 同比增长率保留2位小数，用百分比表示\"\"\"\n", "\n", "# 显示GPT回答\n", "print(\"明确提示的回答:\")\n", "print(get_completion(PROMPT))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["看到差别了吗？明确的指令让GPT能够准确理解任务，并按照要求的格式输出结果。\n", "\n", "#### 示例2：输出格式的明确指定\n", "\n", "让我们看另一个例子，关于如何明确指定输出格式。首先是一个**模糊的请求**："]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["模糊提示的回答:\n", "学习Python是一项非常有益的技能，无论你是初学者还是有经验的程序员。以下是一些建议，可以帮助你有效地学习Python：\n", "\n", "1. **设定明确的目标**：首先，明确你学习Python的目的。是为了数据分析、机器学习、Web开发还是自动化任务？根据你的目标选择合适的学习路径。\n", "\n", "2. **掌握基础知识**：确保你理解Python的基本语法和概念，包括变量、数据类型、操作符、条件语句、循环、函数和类等。\n", "\n", "3. **使用交互式学习平台**：像Codecademy、LeetCode、HackerRank和Kaggle等平台提供了交互式的Python练习，可以帮助你巩固基础知识。\n", "\n", "4. **阅读官方文档**：Python的官方文档是一个非常全面的资源，帮助你深入理解Python的功能和库。\n", "\n", "5. **动手实践**：理论学习固然重要，但实践才能让你真正掌握Python。尝试编写小项目，比如简单的计算器、数据分析脚本或网页爬虫。\n", "\n", "6. **学习Python库**：根据你的目标，学习相关的Python库。例如，数据分析可以学习Pandas和NumPy，机器学习可以学习Scikit-learn和TensorFlow，Web开发可以学习Flask和Django。\n", "\n", "7. **参与社区**：加入Python社区，比如Stack Overflow、Reddit的r/learnpython或GitHub，参与讨论，寻求帮助，分享你的项目。\n", "\n", "8. **定期练习**：编程技能需要不断练习。设定一个学习计划，每天或每周定期编写代码，解决问题。\n", "\n", "9. **解决实际问题**：尝试解决实际问题或挑战，比如自动化日常任务、分析数据集或开发一个简单的应用程序。\n", "\n", "10. **持续学习**：Python生态系统不断发展，保持学习新技术、新库和新工具的习惯。\n", "\n", "通过这些建议，你可以逐步提高你的Python技能，并应用到实际项目中。祝你学习愉快！\n", "\n", "==================================================\n", "\n"]}], "source": ["# 模糊的提示 - 没有指定输出格式\n", "PROMPT = \"给我一些Python学习建议\"\n", "\n", "# 显示GPT回答\n", "print(\"模糊提示的回答:\")\n", "print(get_completion(PROMPT))\n", "print(\"\\n\" + \"=\"*50 + \"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["GPT给出了一般性的建议，但格式不够结构化。现在让我们用**明确的格式要求**："]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["明确提示的回答:\n", "| 阶段 | 学习时长 | 核心概念 | 推荐资源 | 实战项目 |\n", "|------|----------|----------|----------|----------|\n", "| 初级 | 4周 | 变量与数据类型、基本运算符、条件语句、循环 | 《Python编程：从入门到实践》、Codecademy Python课程 | 计算器程序、猜数字游戏 |\n", "| 中级 | 6周 | 函数、列表与字典、文件操作、错误和异常处理 | 《流畅的Python》、LeetCode编程题 | 图书管理系统、数据分析脚本 |\n", "| 高级 | 8周 | 面向对象编程、模块与包、网络编程、数据库操作 | 《Python高级编程》、Coursera Python课程 | Web应用开发、自动化脚本工具 |\n"]}], "source": ["# 明确和直接的提示 - 指定了具体的输出格式\n", "PROMPT = \"\"\"为初学者制定一个Python学习计划。\n", "\n", "要求：\n", "1. 分为3个阶段：初级、中级、高级\n", "2. 每个阶段包含：学习时长、核心概念、推荐资源、实战项目\n", "3. 用表格形式呈现\n", "4. 不要添加额外解释，直接给出表格\n", "\n", "输出格式示例：\n", "| 阶段 | 学习时长 | 核心概念 | 推荐资源 | 实战项目 |\n", "|------|----------|----------|----------|----------|\n", "| 初级 | ... | ... | ... | ... |\"\"\"\n", "\n", "# 显示GPT回答\n", "print(\"明确提示的回答:\")\n", "print(get_completion(PROMPT))"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["#### 示例3：避免歧义的明确指令\n", "\n", "最后一个例子展示如何通过明确指令避免歧义。先看一个**容易产生歧义**的提示：\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["歧义提示的回答:\n", "机器学习是一种通过算法和统计模型使计算机系统能够从数据中自动学习和改进的技术。它是人工智能的一个分支，旨在开发能够从经验中学习的系统，而无需明确编程。机器学习的核心思想是利用数据来训练模型，使其能够识别模式、做出预测或决策。\n", "\n", "机器学习可以分为几种主要类型：\n", "\n", "1. **监督学习**：在这种类型中，模型从标记的数据中学习。数据集包含输入和对应的正确输出，模型通过学习输入与输出之间的关系来进行预测。常见的监督学习任务包括分类（如垃圾邮件检测）和回归（如房价预测）。\n", "\n", "2. **无监督学习**：模型从未标记的数据中寻找模式或结构。常见的无监督学习任务包括聚类（如客户分群）和降维（如主成分分析）。\n", "\n", "3. **半监督学习**：结合了监督学习和无监督学习，使用少量标记数据和大量未标记数据进行训练。这种方法在标记数据难以获取时特别有用。\n", "\n", "4. **强化学习**：模型通过与环境交互来学习策略，以最大化某种奖励。常用于机器人控制、游戏和自动驾驶等领域。\n", "\n", "机器学习的应用非常广泛，包括图像识别、自然语言处理、推荐系统、金融预测、医疗诊断等。随着数据量的增加和计算能力的提升，机器学习在各个领域的影响力和应用范围不断扩大。\n", "\n", "==================================================\n", "\n"]}], "source": ["# 容易产生歧义的提示\n", "PROMPT = \"解释一下机器学习\"\n", "\n", "# 显示GPT回答\n", "print(\"歧义提示的回答:\")\n", "print(get_completion(PROMPT))\n", "print(\"\\n\" + \"=\"*50 + \"\\n\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["这个回答可能过于宽泛或技术性。现在让我们提供一个**明确的上下文和要求**：\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["明确提示的回答:\n", "1. **监督学习**：想象你在学习如何识别水果。你的老师给你一篮子标有名称的水果（苹果、香蕉等），你通过观察这些标记来学习识别不同的水果。这就是监督学习，机器通过已知的输入和输出对来学习模式。应用场景包括垃圾邮件过滤和图像识别。\n", "\n", "2. **无监督学习**：假设你有一篮子没有标签的水果，你需要自己找出哪些水果相似。无监督学习就是这样，机器在没有明确答案的情况下寻找数据中的模式。常用于客户分群和推荐系统。\n", "\n", "3. **强化学习**：想象你在玩电子游戏，通过不断尝试和失败来学习如何过关。强化学习就是这样，机器通过试错和从环境中获得反馈来学习最佳策略。应用于机器人控制和自动驾驶。\n"]}], "source": ["# 明确的上下文和要求\n", "PROMPT = \"\"\"你是一名面向大学一年级计算机专业学生的讲师。用简单易懂的语言解释机器学习。\n", "\n", "要求：\n", "1. 用日常生活中的例子来说明概念\n", "2. 避免复杂的数学公式\n", "3. 重点说明机器学习的3个主要类型及其应用场景\n", "4. 控制在200字以内\n", "5. 用1-2-3的序号格式组织内容\"\"\"\n", "\n", "# 显示GPT回答\n", "print(\"明确提示的回答:\")\n", "print(get_completion(PROMPT))\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### 关键要点总结\n", "\n", "通过以上三个对比鲜明的例子，我们可以看到明确和直接的提示带来的巨大差异：\n", "\n", "**模糊提示的问题：**\n", "- 任务不明确：「帮我处理数据」vs「分析销售数据并计算同比增长率」\n", "- 格式不清晰：「给建议」vs「用表格形式输出学习计划」  \n", "- 缺乏上下文：「解释机器学习」vs「为大一学生用简单语言解释」\n", "\n", "**明确提示的要素：**\n", "1. **具体的任务描述** - 明确说明要做什么\n", "2. **清晰的输出格式** - 指定表格、列表、字数等要求\n", "3. **明确的上下文** - 说明目标受众和使用场景\n", "4. **具体的约束条件** - 字数限制、格式要求等\n", "\n", "记住：**GPT无法读懂你的想法，只能按照你给出的指令执行**。越明确越直接的指令，越能得到符合期望的结果。\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 4}
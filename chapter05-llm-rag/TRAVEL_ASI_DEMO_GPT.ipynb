!pip install openai -q
!pip install faiss-cpu -q
!pip install tiktoken -q
!pip install colab_env -q
!pip install --upgrade tiktoken -q

from IPython import get_ipython
from IPython.display import display
import openai
import faiss
import numpy as np
import tiktoken
import os
import json
import re
import datetime
import colab_env  # This might be specific to Google Colab

from openai import OpenAI
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# ---  No need for file handling ---
# The nyc_knowledge data will be included directly in the code

def get_embedding(text, model="text-embedding-ada-002"):
    """Gets the embedding for the given text using the specified OpenAI model."""
    text = text.replace("\n", " ")
    response = client.embeddings.create(input=[text], model=model)
    return response.data[0].embedding

class Agent:
    """Represents an AI agent assisting with travel planning, specifically for NYC."""

    def __init__(self, name, cache_size, embedding_dim=1536):
        self.name = name
        self.cache_size = cache_size
        self.embedding_dim = embedding_dim
        self.index = faiss.IndexFlatL2(embedding_dim)
        self.knowledge_embeddings = []
        self.knowledge_data = []
        self.embedding_cache = {}

    def load_knowledge(self, knowledge_data):
        """Loads NYC travel knowledge from a JSON object into the Faiss index."""

        for category, subcategories in knowledge_data.items():
            for key, value in subcategories.items():
                if key in self.embedding_cache:
                    embedding = self.embedding_cache[key]
                else:
                    embedding = get_embedding(key, model='text-embedding-ada-002')
                    self.embedding_cache[key] = embedding
                self.knowledge_embeddings.append(embedding)
                self.knowledge_data.append(value)

        self.index.add(np.array(self.knowledge_embeddings))

    def retrieve_knowledge(self, query):
        """Retrieves information from the Faiss index based on a query."""
        if query in self.embedding_cache:
            query_embedding = self.embedding_cache[query]
        else:
            query_embedding = get_embedding(query, model='text-embedding-ada-002')
            self.embedding_cache[query] = query_embedding

        D, I = self.index.search(np.array([query_embedding]), k=5)

        relevant_info = []
        for i in I[0]:
            relevant_info.append(self.knowledge_data[i])
        return relevant_info

    def plan_nyc_trip(self, travel_dates, budget, preferences, interests):
        """Generates a personalized NYC itinerary, including transportation and accommodation."""

        # --- Gather user preferences for transportation and accommodation ---
        transport_mode = input("Preferred mode of transport (airplane, train, bus, car): ")
        accommodation_type = input("Preferred accommodation type (hotel, Airbnb): ")

        relevant_knowledge = self.retrieve_knowledge(preferences)
        for interest in interests:
            relevant_knowledge += self.retrieve_knowledge(interest)

        response = client.chat.completions.create(
            model="gpt-4",  # Using GPT-4
            messages=[{
                "role": "system",
                "content": f"""You are an expert travel agent specializing in NYC.
                        Create a detailed itinerary that includes transportation
                        from Montreal to NYC and accommodation in NYC.
                        The preferred mode of transport is {transport_mode} and
                        the preferred accommodation type is {accommodation_type}.

                        The user wants to visit Yankee Stadium and prefers upscale
                        hotels and dining experiences.

                        For the Yankee Stadium visit, suggest one of the following
                        premium seating options: the Legends Suite Club, the Ford Field
                        MVP Club, or the Champion Suite. Do NOT suggest just a basic tour.

                        On the day of the Yankee Stadium visit, the dinner will be
                        INSIDE the stadium, either at the Legends Suite Club,
                        the Ford Field MVP Club, or the Champion Suite.

                        The Yankee Stadium visit should be scheduled in the AFTERNOON
                        or EVENING, as baseball games are not typically played in
                        the morning.

                        Use the following format:

                        **Transportation:**
                        * [Flight/Train details] ([Estimated Cost: $xxx])

                        **Accommodation:**
                        * [Hotel details] ([Estimated Cost per night: $xxx])

                        **Day 1:**
                        * **Morning:** [Activity 1] ([Estimated Cost: $xx]) - [Brief Description]
                        * **Afternoon:** [Activity 2] ([Estimated Cost: $xx]) - [Brief Description]
                        * **Evening:** [Activity 3] ([Estimated Cost: $xx]) - [Brief Description]
                        * **Dinner:** [Restaurant Suggestion] ([Estimated Cost per person: $xx])

                        **Day 2:**
                        * ... and so on ...

                        Include transportation suggestions, estimated costs, and practical tips.
                        Consider the user's budget: $10000

                        It's crucial that you provide specific cost estimations for EACH
                        item in the itinerary, including transportation, accommodation,
                        activities, meals, and shows.  Do NOT use general price ranges
                        like "expensive" or "$$$" as these are not helpful for budget
                        planning.  Instead, provide numerical estimates like "$25", "$150",
                        or "$40-$60".

                        At the end of the itinerary, please provide the following:
                        * **Total Estimated Cost:** $[total cost]
                        * **Remaining Budget:** $[remaining budget]
                        * **Budget Utilization:** [total cost]/[budget]
                        """
            }, {
                "role": "user",
                "content": f"""Plan a NYC trip from {travel_dates[0]} to {travel_dates[1]}.
                        The traveler's preferences are: {preferences} and their interests include: {', '.join(interests)}.

                        Here's some relevant information about NYC: {self.summarize_knowledge(relevant_knowledge)}
                        """
            }],
            max_tokens=4096
        )

        print('\n')
        print(f"LLM response:\n{response.choices[0].message.content}")

        self.present_itinerary(response.choices[0].message.content, budget, travel_dates)  # Pass budget and travel_dates to present_itinerary


    def present_itinerary(self, itinerary_text, budget, travel_dates):  # Add budget and travel_dates parameters
        """Processes the LLM response, presents the itinerary, and checks the budget."""

        # Split the itinerary by sections (transportation, accommodation, days)
        sections = re.split(r"(Transportation:|Accommodation:|Day\s+\d+:)", itinerary_text)
        itinerary = {}
        current_section = None
        for section in sections:
            section = section.strip()
            if not section:
                continue
            if section in ("Transportation:", "Accommodation:"):
                current_section = section
                itinerary[current_section] = []
            elif re.match(r"Day\s+\d+:", section):
                current_section = section
                itinerary[current_section] = []
            elif current_section:
                itinerary[current_section].append(section)

        # Calculate the number of days for the trip
        start_date = datetime.datetime.strptime(travel_dates[0], "%Y-%m-%d")
        end_date = datetime.datetime.strptime(travel_dates[1], "%Y-%m-%d")
        num_days = (end_date - start_date).days

        # Print the itinerary in a structured format
        for section, items in itinerary.items():
            print(f"\n{section}\n")
            for item in items:
                print(item)

            print("-" * 20)  # Separator between sections

        # ... (cost extraction logic - removed)

        # ... (total cost and budget utilization report - removed)

    def summarize_knowledge(self, knowledge_list):
        """Summarizes the relevant knowledge for the LLM prompt."""
        if not knowledge_list:
            return ""
        summary = ""
        for item in knowledge_list:
            name = list(item.keys())[0]
            summary += f"**{name}**\n"
            summary += f"Description: {item.get('description', 'N/A')}\n"
            if "address" in item:
                summary += f"Address: {item['address']}\n"
            if "website" in item:
                summary += f"Website: {item['website']}\n"
            if "tips" in item:
                summary += "Tips:\n" + "\n".join(item['tips']) + "\n"
            summary += "\n"
        return summary


# ---  Create the "nyc_knowledge.json" file with your data ---
# This is an example, replace with your actual data
nyc_knowledge = {
  "attractions": {
    "Empire State Building": {
      "description": "Iconic skyscraper with observation decks offering stunning views of the city.",
      "address": "350 Fifth Avenue, Manhattan",
      "website": "www.esbnyc.com",
      "tips": [
        "Purchase tickets online in advance to avoid long lines.",
        "Visit during the day and at night for different perspectives."
      ]
    },
    "Central Park": {
      "description": "A vast green oasis in the heart of Manhattan, perfect for picnics, walks, bike rides, and boating.",
      "activities": [
        "visit the Central Park Zoo",
        "rent a rowboat on The Lake",
        "see a performance at the Delacorte Theater",
        "have a picnic on the Great Lawn"
      ],
      "tips": [
        "Download a map of the park to navigate its many paths and attractions."
      ]
    },
    "The Metropolitan Museum of Art": {
      "description": "One of the world's largest and finest art museums.",
      "address": "1000 Fifth Avenue, Manhattan",
      "website": "www.metmuseum.org",
      "tips": [
        "Allow ample time to explore the vast collection.",
        "Consider purchasing a guided tour for a more in-depth experience."
      ]
    }
    # ... add more attractions and other categories ...
  }
}

# Create an agent
agent = Agent("NYCAI", cache_size=1000)

# Load the knowledge data
agent.load_knowledge(nyc_knowledge)

# Simulate a user requesting a trip plan
travel_dates = ["2024-04-10", "2024-04-15"]
budget = "$10000"  # Use the correct budget value
preferences = "I prefer a mix of sightseeing, cultural experiences, and trying local food. I like to walk a lot but also want to use public transport."
interests = ["museums", "Broadway shows", "Central Park"]

agent.plan_nyc_trip(travel_dates, budget, preferences, interests)
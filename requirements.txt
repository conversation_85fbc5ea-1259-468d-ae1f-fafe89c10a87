# ============================================
# LLM101 项目依赖包配置文件
# 面向大模型初级学习人员的详细说明
# ============================================

# ==================== API客户端 ====================
# OpenAI Python SDK - 与OpenAI API交互的官方客户端库
# 用途：调用GPT-4、GPT-3.5、DALL-E、Whisper等OpenAI模型
# 使用场景：
#   - 发送文本到GPT模型进行对话
#   - 调用函数调用(Function Calling)功能
#   - 使用嵌入(Embedding)模型生成文本向量
#   - 调用图像生成、语音识别等多模态功能
# 学习要点：掌握如何配置API密钥、发送请求、处理响应
openai==1.61.0

# ==================== 工具类库 ====================
# python-dotenv - 环境变量管理工具
# 用途：从.env文件中加载环境变量，保护敏感信息如API密钥
# 使用场景：
#   - 安全存储API密钥而不硬编码在代码中
#   - 管理不同环境(开发/测试/生产)的配置
#   - 避免敏感信息泄露到版本控制系统
# 学习要点：创建.env文件、使用load_dotenv()加载变量
python-dotenv==1.1.1

# Pydantic - 数据验证和设置管理库
# 用途：定义数据模型、验证输入数据、类型检查
# 使用场景：
#   - 定义API请求和响应的数据结构
#   - 验证用户输入的数据格式
#   - 配置管理和参数验证
#   - 与FastAPI框架深度集成
# 学习要点：BaseModel类的使用、字段验证、类型注解
pydantic==2.11.7

# tqdm - 进度条显示库
# 用途：为长时间运行的任务显示进度条
# 使用场景：
#   - 批量处理大量数据时显示进度
#   - 模型训练过程中的进度监控
#   - 文件上传/下载进度显示
#   - 数据预处理进度追踪
# 学习要点：在循环中使用tqdm()包装迭代器
tqdm==4.67.1

# ==================== 版本说明 ====================
# 版本号格式：主版本.次版本.修订版本
# ==：精确版本匹配，确保环境一致性
# 选择这些版本的原因：
#   - 稳定性：经过充分测试的稳定版本
#   - 兼容性：各库之间版本兼容
#   - 功能完整：包含项目所需的所有功能
# 
# 安装方法：
#   pip install -r requirements.txt
# 
# 升级方法：
#   pip install --upgrade -r requirements.txt

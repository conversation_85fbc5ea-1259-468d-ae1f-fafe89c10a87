# ===========================================
# LLM-101 环境变量配置模板
# ===========================================
# 请复制此文件为 .env 并填入您的实际配置

# ===========================================
# OpenAI API 地址和Keys 配置
# ===========================================
# OpenAI API 协议是一种通用接口标准，
#它让开发者能够以统一的方式调用包括 OpenAI 模型以及 DeepSeek、Qwen 等众多兼容模型的人工智能服务，
#从而极大简化了 AI 应用的开发和模型切换。
# OpenAI API 地址
# OpenAI API 地址(官网地址/也是默认地址)
# OPENAI_BASE_URL=https://api.openai.com/v1
# DeepSeek API 地址
OPENAI_BASE_URL=https://api.deepseek.com/v1
# OpenAI API 地址(国内代理)
# OPENAI_BASE_URL=https://vip.apiyi.com/v1

# OpenAI API Key
# OpenAI API Key 获取地址(官网): https://platform.openai.com/api-keys

# OpenAI API Key 获取地址(国内代理): https://www.apiyi.com/register/?aff_code=we80
# OPENAI_API_KEY=your_openai_api_key_here

# DeepSeek API Key 获取地址: https://platform.deepseek.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here


# ===========================================
# 模型配置
# ===========================================
# 默认使用的模型
# DEFAULT_MODEL=gpt-4o
DEFAULT_MODEL=deepseek-chat
# 模型参数配置
MAX_TOKENS=4096
TEMPERATURE=0.7
TOP_P=1.0

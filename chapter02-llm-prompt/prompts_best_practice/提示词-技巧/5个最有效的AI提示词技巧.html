<svg viewBox="0 0 1400 900" xmlns="http://www.w3.org/2000/svg">
              <!-- 定义渐变和样式 -->
              <defs>
                <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
                  <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
                </linearGradient>
                
                <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
                  <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
                </linearGradient>
                
                <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                  <feDropShadow dx="3" dy="3" stdDeviation="4" flood-color="#000" flood-opacity="0.2"/>
                </filter>
                
                <style>
                  .title-text { font-family: '宋体', serif; font-size: 28px; font-weight: bold; fill: white; }
                  .subtitle-text { font-family: '宋体', serif; font-size: 16px; fill: white; }
                  .method-title { font-family: '宋体', serif; font-size: 18px; font-weight: bold; fill: #1e40af; }
                  .method-desc { font-family: '宋体', serif; font-size: 14px; fill: #374151; }
                  .example-text { font-family: '宋体', serif; font-size: 12px; fill: #6b7280; }
                  .formula-text { font-family: 'Times New Roman', serif; font-size: 12px; fill: #dc2626; }
                  .arrow-text { font-family: '宋体', serif; font-size: 11px; fill: #4b5563; }
                </style>
              </defs>
              
              <!-- 背景 -->
              <rect width="1400" height="900" fill="#f1f5f9"/>
              
              <!-- 标题区域 -->
              <rect x="100" y="40" width="1200" height="80" fill="url(#headerGradient)" rx="12" filter="url(#shadow)"/>
              <text x="700" y="70" class="title-text" text-anchor="middle">提示词工程技巧</text>
              <text x="700" y="100" class="subtitle-text" text-anchor="middle">来自 
            @SanderSchulhoff
             在 AI Prompt Engineering in 2025 的分享</text>
              
              <!-- 核心公式 -->
              <rect x="500" y="140" width="400" height="60" fill="#fef3c7" stroke="#f59e0b" stroke-width="2" rx="8"/>
              <text x="700" y="165" class="formula-text" text-anchor="middle">有效性 = f(示例数量, 上下文信息, 问题分解)</text>
              <text x="700" y="185" class="formula-text" text-anchor="middle">可靠性 ∝ 自我批评 × 集成方法</text>
              
              <!-- 技巧1: 少量提示法 -->
              <rect x="80" y="240" width="280" height="280" fill="url(#cardGradient)" stroke="#3b82f6" stroke-width="3" rx="10" filter="url(#shadow)"/>
              <text x="220" y="270" class="method-title" text-anchor="middle">1. 少量提示法</text>
              <text x="220" y="295" class="method-desc" text-anchor="middle">提供2-5个示例指导模型</text>
              
              <text x="100" y="325" class="method-desc">核心原理：通过示例学习</text>
              <text x="100" y="345" class="example-text">示例：工单分类</text>
              <text x="100" y="365" class="example-text">"无法重置密码" → 账户访问</text>
              <text x="100" y="385" class="example-text">"应用程序崩溃" → 错误报告</text>
              <text x="100" y="405" class="example-text">"升级专业版" → 销售咨询</text>
              
              <rect x="110" y="430" width="220" height="40" fill="#dbeafe" stroke="#3b82f6" rx="5"/>
              <text x="220" y="445" class="formula-text" text-anchor="middle">准确率 = k × log(n+1)</text>
              <text x="220" y="460" class="formula-text" text-anchor="middle">其中 n = 示例数量 (2≤n≤5)</text>
              
              <text x="100" y="490" class="method-desc">适用场景：分类任务、格式化输出</text>
              <text x="100" y="510" class="method-desc">效果提升：准确率提高20-40%</text>
              
              <!-- 技巧2: 问题分解法 -->
              <rect x="400" y="240" width="280" height="280" fill="url(#cardGradient)" stroke="#10b981" stroke-width="3" rx="10" filter="url(#shadow)"/>
              <text x="540" y="270" class="method-title" text-anchor="middle">2. 问题分解法</text>
              <text x="540" y="295" class="method-desc" text-anchor="middle">将复杂问题拆分成子问题</text>
              
              <text x="420" y="325" class="method-desc">核心原理：分而治之</text>
              <text x="420" y="345" class="example-text">步骤1：识别子问题</text>
              <text x="420" y="365" class="example-text">"这个问题包含哪些子问题？"</text>
              <text x="420" y="385" class="example-text">步骤2：逐个解决</text>
              <text x="420" y="405" class="example-text">"现在解决每个子问题"</text>
              
              <rect x="430" y="430" width="220" height="40" fill="#d1fae5" stroke="#10b981" rx="5"/>
              <text x="540" y="445" class="formula-text" text-anchor="middle">复杂度降低 = Π(1/n_i)</text>
              <text x="540" y="460" class="formula-text" text-anchor="middle">其中 n_i = 子问题规模</text>
              
              <text x="420" y="490" class="method-desc">适用场景：多步推理、复杂计算</text>
              <text x="420" y="510" class="method-desc">效果提升：逻辑正确性提高30%</text>
              
              <!-- 技巧3: 自我批评法 -->
              <rect x="720" y="240" width="280" height="280" fill="url(#cardGradient)" stroke="#f59e0b" stroke-width="3" rx="10" filter="url(#shadow)"/>
              <text x="860" y="270" class="method-title" text-anchor="middle">3. 自我批评法</text>
              <text x="860" y="295" class="method-desc" text-anchor="middle">让模型反思和验证答案</text>
              
              <text x="740" y="325" class="method-desc">核心原理：自我验证机制</text>
              <text x="740" y="345" class="example-text">步骤1：生成初始答案</text>
              <text x="740" y="365" class="example-text">"请检查你的回答"</text>
              <text x="740" y="385" class="example-text">步骤2：提供批评意见</text>
              <text x="740" y="405" class="example-text">"对自己提出批评"</text>
              
              <rect x="750" y="430" width="220" height="40" fill="#fef3c7" stroke="#f59e0b" rx="5"/>
              <text x="860" y="445" class="formula-text" text-anchor="middle">可靠性 = 1 - e^(-αt)</text>
              <text x="860" y="460" class="formula-text" text-anchor="middle">其中 t = 反思时间</text>
              
              <text x="740" y="490" class="method-desc">适用场景：高风险任务、逻辑推理</text>
              <text x="740" y="510" class="method-desc">效果提升：错误率降低25%</text>
              
              <!-- 技巧4: 上下文增强法 -->
              <rect x="1040" y="240" width="280" height="280" fill="url(#cardGradient)" stroke="#8b5cf6" stroke-width="3" rx="10" filter="url(#shadow)"/>
              <text x="1180" y="270" class="method-title" text-anchor="middle">4. 上下文增强法</text>
              <text x="1180" y="295" class="method-desc" text-anchor="middle">提供详细的背景信息</text>
              
              <text x="1060" y="325" class="method-desc">核心原理：信息丰富度</text>
              <text x="1060" y="345" class="example-text">用户状态：付费客户</text>
              <text x="1060" y="365" class="example-text">语调要求：同理心</text>
              <text x="1060" y="385" class="example-text">约束条件：不提及升级</text>
              <text x="1060" y="405" class="example-text">操作指引：提供重试链接</text>
              
              <rect x="1070" y="430" width="220" height="40" fill="#ede9fe" stroke="#8b5cf6" rx="5"/>
              <text x="1180" y="445" class="formula-text" text-anchor="middle">性能 ∝ I(上下文)</text>
              <text x="1180" y="460" class="formula-text" text-anchor="middle">信息论原理</text>
              
              <text x="1060" y="490" class="method-desc">适用场景：客户服务、创作任务</text>
              <text x="1060" y="510" class="method-desc">效果提升：相关性提高35%</text>
              
              <!-- 技巧5: 集成提示法 -->
              <rect x="580" y="560" width="280" height="280" fill="url(#cardGradient)" stroke="#ef4444" stroke-width="3" rx="10" filter="url(#shadow)"/>
              <text x="720" y="590" class="method-title" text-anchor="middle">5. 集成提示法</text>
              <text x="720" y="615" class="method-desc" text-anchor="middle">多次运行并选择最优结果</text>
              
              <text x="600" y="645" class="method-desc">核心原理：统计学优化</text>
              <text x="600" y="665" class="example-text">步骤1：同一个问题问3-5遍</text>
              <text x="600" y="685" class="example-text">步骤2：比较所有答案</text>
              <text x="600" y="705" class="example-text">步骤3：排序或投票</text>
              <text x="600" y="725" class="example-text">步骤4：选择最佳结果</text>
              
              <rect x="610" y="750" width="220" height="50" fill="#fee2e2" stroke="#ef4444" rx="5"/>
              <text x="720" y="765" class="formula-text" text-anchor="middle">集成得分 = Σw_i × s_i</text>
              <text x="720" y="780" class="formula-text" text-anchor="middle">方差降低 = σ²/n</text>
              <text x="720" y="795" class="formula-text" text-anchor="middle">其中 n = 集成规模</text>
              
              <text x="600" y="820" class="method-desc">适用场景：高质量要求、创意任务</text>
              
              <!-- 连接线和流程 -->
              <defs>
                <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="10" refY="4" orient="auto">
                  <polygon points="0 0, 12 4, 0 8" fill="#6b7280"/>
                </marker>
              </defs>
              
              <!-- 从标题到各个技巧的连接线 -->
              <line x1="600" y1="200" x2="220" y2="240" stroke="#6b7280" stroke-width="2" marker-end="url(#arrowhead)"/>
              <line x1="650" y1="200" x2="540" y2="240" stroke="#6b7280" stroke-width="2" marker-end="url(#arrowhead)"/>
              <line x1="700" y1="200" x2="860" y2="240" stroke="#6b7280" stroke-width="2" marker-end="url(#arrowhead)"/>
              <line x1="750" y1="200" x2="1180" y2="240" stroke="#6b7280" stroke-width="2" marker-end="url(#arrowhead)"/>
              <line x1="700" y1="200" x2="720" y2="560" stroke="#6b7280" stroke-width="2" marker-end="url(#arrowhead)"/>
              
              <!-- 技巧间的协同关系 -->
              <path d="M 360 380 Q 460 450 580 380" stroke="#94a3b8" stroke-width="3" fill="none" stroke-dasharray="8,5"/>
              <text x="470" y="430" class="arrow-text" text-anchor="middle">协同增效</text>
              
              <path d="M 1000 380 Q 920 450 860 380" stroke="#94a3b8" stroke-width="3" fill="none" stroke-dasharray="8,5"/>
              <text x="930" y="430" class="arrow-text" text-anchor="middle">互补强化</text>
              
              <path d="M 540 520 Q 630 540 720 560" stroke="#94a3b8" stroke-width="3" fill="none" stroke-dasharray="8,5"/>
              <text x="630" y="545" class="arrow-text" text-anchor="middle">质量保证</text>
              
            </svg>
{"cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/FlyAIBox/LLM-101/blob/main/chapter03-llm-deploy/vllm/deepseek_r1_distill_qwen_fast_api.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "gPVYg0Z9SxCq"}, "source": ["# 基于vLLM部署DeepSeek-R1-Distill-Qwen-1.5B\n", "## 📖 关于 DeepSeek R1 蒸馏版 Qwen 1.5B 模型\n", "\n", "### 🧠 模型特点\n", "- **模型名称**: `deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B`\n", "- **参数规模**: 15亿参数，适合在显存有限的GPU（如Nvidia T4/4090） 上运行\n", "- **推理能力**: 继承了 DeepSeek R1 的强大推理能力\n", "- **蒸馏技术**: 通过知识蒸馏获得更小但高效的模型\n", "\n", "### 🔍 模型优势\n", "1. **轻量化**: 15亿参数，内存占用小\n", "2. **高效推理**: 优化的推理速度\n", "3. **强大能力**: 保持了大模型的推理能力\n", "4. **免费部署**: 适合在 Colab上免费的Nvidia T4 GPU 上运行"]}, {"cell_type": "markdown", "metadata": {"id": "Ojg0UqLRnp1i"}, "source": ["## 🎯 实验目标\n", "\n", "本实验旨在帮助大模型技术初学者：\n", "\n", "### 📚 学习内容\n", "1. **环境准备**: 了解如何检查和配置 Colab 环境\n", "2. **依赖安装**: 学习安装 VLLM、FastAPI 等关键库\n", "3. **模型部署**: 掌握使用 VLLM 部署大语言模型的方法\n", "4. **API 开发**: 创建 RESTful API 接口服务\n", "5. **实时交互**: 实现流式输出和实时对话功能\n", "\n", "### 💰 成本优势\n", "- **完全免费**: 使用 Google Colab 免费 T4 GPU (15GB 显存)\n", "- **零配置**: 无需本地环境配置，浏览器即可运行\n", "- **即开即用**: 一键启动，快速体验大模型部署\n", "\n", "### 🚀 期望收获\n", "通过本实验，您将掌握：\n", "- 大语言模型的基本部署流程\n", "- VLLM 推理引擎的使用方法\n", "- FastAPI Web 服务的开发技巧\n", "- 模型 API 的设计和实现"]}, {"cell_type": "markdown", "metadata": {"id": "0LtZRZL_7jzo"}, "source": ["## 🔧 第一步：环境信息检查\n", "\n", "在开始部署模型之前，我们需要了解当前的运行环境。这个步骤非常重要，因为：\n", "\n", "### 🎯 检查目的\n", "1. **硬件确认**: 确保有足够的 GPU 显存运行模型\n", "2. **系统兼容**: 验证操作系统和 Python 版本\n", "3. **资源评估**: 了解可用的 CPU、内存和存储空间\n", "4. **环境配置**: 检查 CUDA 版本和相关依赖\n", "\n", "### 📊 检查内容\n", "- **操作系统**: Linux 发行版和版本\n", "- **CPU 信息**: 处理器型号和核心数\n", "- **内存状态**: 总内存和可用内存\n", "- **GPU 配置**: 显卡型号和显存大小\n", "- **CUDA 版本**: 深度学习框架支持\n", "- **Python 环境**: 解释器版本\n", "- **磁盘空间**: 可用存储空间"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "VQMi5m-R7fLB", "outputId": "4d26085f-078f-4acb-954c-a4eb3ea31b3e"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: pandas==2.2.2 in /usr/local/lib/python3.11/dist-packages (2.2.2)\n", "Requirement already satisfied: numpy>=1.23.2 in /usr/local/lib/python3.11/dist-packages (from pandas==2.2.2) (2.0.2)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.11/dist-packages (from pandas==2.2.2) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas==2.2.2) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas==2.2.2) (2025.2)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.8.2->pandas==2.2.2) (1.17.0)\n", "### 环境信息\n", "| 项目         | 信息                                                               |\n", "|:-------------|:-------------------------------------------------------------------|\n", "| 操作系统     | Linux Ubuntu 22.04.4 LTS                                           |\n", "| CPU 信息     | Intel(R) Xeon(R) CPU @ 2.20GHz (1 physical cores, 2 logical cores) |\n", "| 内存信息     | 12.67 GB (Available: 11.50 GB)                                     |\n", "| GPU 信息     | Tesla T4 (15360 MiB)                                               |\n", "| CUDA 信息    | 12.5                                                               |\n", "| Python 版本  | 3.11.13                                                            |\n", "| Conda 版本   | Conda not found                                                    |\n", "| 物理磁盘空间 | Total: 112.64 GB, Used: 37.67 GB, Free: 74.95 GB                   |\n"]}], "source": ["# 🔍 环境信息检查脚本\n", "#\n", "# 本脚本的作用：\n", "# 1. 安装 pandas 库用于数据表格展示\n", "# 2. 检查系统的各项配置信息\n", "# 3. 生成详细的环境报告表格\n", "#\n", "# 对于初学者来说，这个步骤帮助您：\n", "# - 了解当前运行环境的硬件配置\n", "# - 确认是否满足模型运行的最低要求\n", "# - 学习如何通过代码获取系统信息\n", "\n", "# 安装 pandas 库 - 用于创建和展示数据表格\n", "# pandas 是 Python 中最流行的数据处理和分析库\n", "!pip install pandas==2.2.2\n", "\n", "import platform # 导入 platform 模块以获取系统信息\n", "import os # 导入 os 模块以与操作系统交互\n", "import subprocess # 导入 subprocess 模块以运行外部命令\n", "import pandas as pd # 导入 pandas 模块，通常用于数据处理，这里用于创建表格\n", "import shutil # 导入 shutil 模块以获取磁盘空间信息\n", "\n", "# 获取 CPU 信息的函数，包括核心数量\n", "def get_cpu_info():\n", "    cpu_info = \"\" # 初始化 CPU 信息字符串\n", "    physical_cores = \"N/A\"\n", "    logical_cores = \"N/A\"\n", "\n", "    if platform.system() == \"Windows\": # 如果是 Windows 系统\n", "        cpu_info = platform.processor() # 使用 platform.processor() 获取 CPU 信息\n", "        try:\n", "            # 获取 Windows 上的核心数量 (需要 WMI)\n", "            import wmi\n", "            c = wmi.WMI()\n", "            for proc in c.Win32_Processor():\n", "                physical_cores = proc.NumberOfCores\n", "                logical_cores = proc.NumberOfLogicalProcessors\n", "        except:\n", "            pass # 如果 WMI 不可用，忽略错误\n", "\n", "    elif platform.system() == \"Darwin\": # 如果是 macOS 系统\n", "        # 在 macOS 上使用 sysctl 命令获取 CPU 信息和核心数量\n", "        os.environ['PATH'] = os.environ['PATH'] + os.pathsep + '/usr/sbin' # 更新 PATH 环境变量\n", "        try:\n", "            process_brand = subprocess.Popen(['sysctl', \"machdep.cpu.brand_string\"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)\n", "            stdout_brand, stderr_brand = process_brand.communicate()\n", "            cpu_info = stdout_brand.decode().split(': ')[1].strip() if stdout_brand else \"Could not retrieve CPU info\"\n", "\n", "            process_physical = subprocess.Popen(['sysctl', \"hw.physicalcpu\"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)\n", "            stdout_physical, stderr_physical = process_physical.communicate()\n", "            physical_cores = stdout_physical.decode().split(': ')[1].strip() if stdout_physical else \"N/A\"\n", "\n", "            process_logical = subprocess.Popen(['sysctl', \"hw.logicalcpu\"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)\n", "            stdout_logical, stderr_logical = process_logical.communicate()\n", "            logical_cores = stdout_logical.decode().split(': ')[1].strip() if stdout_logical else \"N/A\"\n", "\n", "        except:\n", "            cpu_info = \"Could not retrieve CPU info\"\n", "            physical_cores = \"N/A\"\n", "            logical_cores = \"N/A\"\n", "\n", "    else:  # Linux 系统\n", "        try:\n", "            # 在 Linux 上读取 /proc/cpuinfo 文件获取 CPU 信息和核心数量\n", "            with open('/proc/cpuinfo') as f:\n", "                physical_cores_count = 0\n", "                logical_cores_count = 0\n", "                cpu_info_lines = []\n", "                for line in f:\n", "                    if line.startswith('model name'): # 查找以 'model name'开头的行\n", "                        if not cpu_info: # 只获取第一个 model name\n", "                            cpu_info = line.split(': ')[1].strip()\n", "                    elif line.startswith('cpu cores'): # 查找以 'cpu cores' 开头的行\n", "                        physical_cores_count = int(line.split(': ')[1].strip())\n", "                    elif line.startswith('processor'): # 查找以 'processor' 开头的行\n", "                        logical_cores_count += 1\n", "                physical_cores = str(physical_cores_count) if physical_cores_count > 0 else \"N/A\"\n", "                logical_cores = str(logical_cores_count) if logical_cores_count > 0 else \"N/A\"\n", "                if not cpu_info:\n", "                     cpu_info = \"Could not retrieve CPU info\"\n", "\n", "        except:\n", "            cpu_info = \"Could not retrieve CPU info\"\n", "            physical_cores = \"N/A\"\n", "            logical_cores = \"N/A\"\n", "\n", "    return f\"{cpu_info} ({physical_cores} physical cores, {logical_cores} logical cores)\" # 返回 CPU 信息和核心数量\n", "\n", "\n", "# 获取内存信息的函数\n", "def get_memory_info():\n", "    mem_info = \"\" # 初始化内存信息字符串\n", "    if platform.system() == \"Windows\":\n", "        # 在 Windows 上不容易通过标准库获取，需要外部库或 PowerShell\n", "        mem_info = \"Requires external tools on Windows\" # 设置提示信息\n", "    elif platform.system() == \"Darwin\": # 如果是 macOS 系统\n", "        # 在 macOS 上使用 sysctl 命令获取内存大小\n", "        process = subprocess.Popen(['sysctl', \"hw.memsize\"], stdout=subprocess.PIPE, stderr=subprocess.PIPE) # 运行 sysctl 命令\n", "        stdout, stderr = process.communicate() # 获取标准输出和标准错误\n", "        mem_bytes = int(stdout.decode().split(': ')[1].strip()) # 解析输出，获取内存大小（字节）\n", "        mem_gb = mem_bytes / (1024**3) # 转换为 GB\n", "        mem_info = f\"{mem_gb:.2f} GB\" # 格式化输出\n", "    else:  # Linux 系统\n", "        try:\n", "            # 在 Linux 上读取 /proc/meminfo 文件获取内存信息\n", "            with open('/proc/meminfo') as f:\n", "                total_mem_kb = 0\n", "                available_mem_kb = 0\n", "                for line in f:\n", "                    if line.startswith('MemTotal'): # 查找以 'MemTotal' 开头的行\n", "                        total_mem_kb = int(line.split(':')[1].strip().split()[0]) # 解析行，获取总内存（KB）\n", "                    elif line.startswith('MemAvailable'): # 查找以 'MemAvailable' 开头的行\n", "                         available_mem_kb = int(line.split(':')[1].strip().split()[0]) # 解析行，获取可用内存（KB）\n", "\n", "                if total_mem_kb > 0:\n", "                    total_mem_gb = total_mem_kb / (1024**2) # 转换为 GB\n", "                    mem_info = f\"{total_mem_gb:.2f} GB\" # 格式化输出总内存\n", "                    if available_mem_kb > 0:\n", "                        available_mem_gb = available_mem_kb / (1024**2)\n", "                        mem_info += f\" (Available: {available_mem_gb:.2f} GB)\" # 添加可用内存信息\n", "                else:\n", "                     mem_info = \"Could not retrieve memory info\" # 如果读取文件出错，设置错误信息\n", "\n", "        except:\n", "            mem_info = \"Could not retrieve memory info\" # 如果读取文件出错，设置错误信息\n", "    return mem_info # 返回内存信息\n", "\n", "# 获取 GPU 信息的函数，包括显存\n", "def get_gpu_info():\n", "    try:\n", "        # 尝试使用 nvidia-smi 获取 NVIDIA GPU 信息和显存\n", "        result = subprocess.run(['nvidia-smi', '--query-gpu=name,memory.total', '--format=csv,noheader'], capture_output=True, text=True)\n", "        if result.returncode == 0: # 如果命令成功执行\n", "            gpu_lines = result.stdout.strip().split('\\n') # 解析输出，获取 GPU 名称和显存\n", "            gpu_info_list = []\n", "            for line in gpu_lines:\n", "                name, memory = line.split(', ')\n", "                gpu_info_list.append(f\"{name} ({memory})\") # 格式化 GPU 信息\n", "            return \", \".join(gpu_info_list) if gpu_info_list else \"NVIDIA GPU found, but info not listed\" # 返回 GPU 信息或提示信息\n", "        else:\n", "             # 尝试使用 lshw 获取其他 GPU 信息 (需要安装 lshw)\n", "            try:\n", "                result_lshw = subprocess.run(['lshw', '-C', 'display'], capture_output=True, text=True)\n", "                if result_lshw.returncode == 0: # 如果命令成功执行\n", "                     # 简单解析输出中的 product 名称和显存\n", "                    gpu_info_lines = []\n", "                    current_gpu = {}\n", "                    for line in result_lshw.stdout.splitlines():\n", "                        if 'product:' in line:\n", "                             if current_gpu:\n", "                                 gpu_info_lines.append(f\"{current_gpu.get('product', 'GPU')} ({current_gpu.get('memory', 'N/A')})\")\n", "                             current_gpu = {'product': line.split('product:')[1].strip()}\n", "                        elif 'size:' in line and 'memory' in line:\n", "                             current_gpu['memory'] = line.split('size:')[1].strip()\n", "\n", "                    if current_gpu: # 添加最后一个 GPU 的信息\n", "                        gpu_info_lines.append(f\"{current_gpu.get('product', 'GPU')} ({current_gpu.get('memory', 'N/A')})\")\n", "\n", "                    return \", \".join(gpu_info_lines) if gpu_info_lines else \"GPU found (via lshw), but info not parsed\" # 如果找到 GPU 但信息无法解析，设置提示信息\n", "                else:\n", "                    return \"No GPU found (checked nvidia-smi and lshw)\" # 如果两个命令都找不到 GPU，设置提示信息\n", "            except FileNotFoundError:\n", "                 return \"No GPU found (checked nvidia-smi, lshw not found)\" # 如果找不到 lshw 命令，设置提示信息\n", "    except FileNotFoundError:\n", "        return \"No GPU found (nvidia-smi not found)\" # 如果找不到 nvidia-smi 命令，设置提示信息\n", "\n", "\n", "# 获取 CUDA 版本的函数\n", "def get_cuda_version():\n", "    try:\n", "        # 尝试使用 nvcc --version 获取 CUDA 版本\n", "        result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True)\n", "        if result.returncode == 0: # 如果命令成功执行\n", "            for line in result.stdout.splitlines():\n", "                if 'release' in line: # 查找包含 'release' 的行\n", "                    return line.split('release ')[1].split(',')[0] # 解析行，提取版本号\n", "        return \"CUDA not found or version not parsed\" # 如果找不到 CUDA 或版本无法解析，设置提示信息\n", "    except FileNotFoundError:\n", "        return \"CUDA not found\" # 如果找不到 nvcc 命令，设置提示信息\n", "\n", "# 获取 Python 版本的函数\n", "def get_python_version():\n", "    return platform.python_version() # 获取 Python 版本\n", "\n", "# 获取 Conda 版本的函数\n", "def get_conda_version():\n", "    try:\n", "        # 尝试使用 conda --version 获取 Conda 版本\n", "        result = subprocess.run(['conda', '--version'], capture_output=True, text=True)\n", "        if result.returncode == 0: # 如果命令成功执行\n", "            return result.stdout.strip() # 返回 Conda 版本\n", "        return \"Conda not found or version not parsed\" # 如果找不到 Conda 或版本无法解析，设置提示信息\n", "    except FileNotFoundError:\n", "        return \"Conda not found\" # 如果找不到 conda 命令，设置提示信息\n", "\n", "# 获取物理磁盘空间信息的函数\n", "def get_disk_space():\n", "    try:\n", "        total, used, free = shutil.disk_usage(\"/\") # 获取根目录的磁盘使用情况\n", "        total_gb = total / (1024**3) # 转换为 GB\n", "        used_gb = used / (1024**3) # 转换为 GB\n", "        free_gb = free / (1024**3) # 转换为 GB\n", "        return f\"Total: {total_gb:.2f} GB, Used: {used_gb:.2f} GB, Free: {free_gb:.2f} GB\" # 格式化输出\n", "    except Exception as e:\n", "        return f\"Could not retrieve disk info: {e}\" # 如果获取信息出错，设置错误信息\n", "\n", "# 获取环境信息\n", "os_name = platform.system() # 获取操作系统名称\n", "os_version = platform.release() # 获取操作系统版本\n", "if os_name == \"Linux\":\n", "    try:\n", "        # 在 Linux 上尝试获取发行版和版本\n", "        lsb_info = subprocess.run(['lsb_release', '-a'], capture_output=True, text=True)\n", "        if lsb_info.returncode == 0: # 如果命令成功执行\n", "            for line in lsb_info.stdout.splitlines():\n", "                if 'Description:' in line: # 查找包含 'Description:' 的行\n", "                    os_version = line.split('Description:')[1].strip() # 提取描述信息作为版本\n", "                    break # 找到后退出循环\n", "                elif 'Release:' in line: # 查找包含 'Release:' 的行\n", "                     os_version = line.split('Release:')[1].strip() # 提取版本号\n", "                     # 尝试获取 codename\n", "                     try:\n", "                         codename_info = subprocess.run(['lsb_release', '-c'], capture_output=True, text=True)\n", "                         if codename_info.returncode == 0:\n", "                             os_version += f\" ({codename_info.stdout.split(':')[1].strip()})\" # 将 codename 添加到版本信息中\n", "                     except:\n", "                         pass # 如果获取 codename 失败则忽略\n", "\n", "    except FileNotFoundError:\n", "        pass # lsb_release 可能未安装，忽略错误\n", "\n", "full_os_info = f\"{os_name} {os_version}\" # 组合完整的操作系统信息\n", "cpu_info = get_cpu_info() # 调用函数获取 CPU 信息和核心数量\n", "memory_info = get_memory_info() # 调用函数获取内存信息\n", "gpu_info = get_gpu_info() # 调用函数获取 GPU 信息和显存\n", "cuda_version = get_cuda_version() # 调用函数获取 CUDA 版本\n", "python_version = get_python_version() # 调用函数获取 Python 版本\n", "conda_version = get_conda_version() # 调用函数获取 Conda 版本\n", "disk_info = get_disk_space() # 调用函数获取物理磁盘空间信息\n", "\n", "\n", "# 创建用于存储数据的字典\n", "env_data = {\n", "    \"项目\": [ # 项目名称列表\n", "        \"操作系统\",\n", "        \"CPU 信息\",\n", "        \"内存信息\",\n", "        \"GPU 信息\",\n", "        \"CUDA 信息\",\n", "        \"Python 版本\",\n", "        \"Conda 版本\",\n", "        \"物理磁盘空间\" # 添加物理磁盘空间\n", "    ],\n", "    \"信息\": [ # 对应的信息列表\n", "        full_os_info,\n", "        cpu_info,\n", "        memory_info,\n", "        gpu_info,\n", "        cuda_version,\n", "        python_version,\n", "        conda_version,\n", "        disk_info # 添加物理磁盘空间信息\n", "    ]\n", "}\n", "\n", "# 创建一个 pandas DataFrame\n", "df = pd.DataFrame(env_data)\n", "\n", "# 打印表格\n", "print(\"### 环境信息\") # 打印标题\n", "print(df.to_markdown(index=False)) # 将 DataFrame 转换为 Markdown 格式并打印，不包含索引"]}, {"cell_type": "markdown", "metadata": {"id": "Bj_P8PUQjFFT"}, "source": ["## 📦 第二步：安装依赖包\n", "\n", "现在我们需要安装运行模型所需的关键 Python 包：\n", "\n", "### 🔧 核心依赖说明\n", "\n", "#### 1. **FastAPI (0.116.0)**\n", "- **作用**: 现代化的 Python Web 框架\n", "- **用途**: 创建 RESTful API 接口服务\n", "- **特点**: 自动生成 API 文档，支持异步处理\n", "\n", "#### 2. **nest-asyncio (1.6.0)**\n", "- **作用**: 允许在已有事件循环中运行异步代码\n", "- **用途**: 解决 Jupyter 环境中的异步兼容问题\n", "- **重要性**: 确保 FastAPI 在 Colab 中正常运行\n", "\n", "#### 3. **p<PERSON><PERSON><PERSON> (7.2.12)**\n", "- **作用**: Python 版本的 ngrok 客户端\n", "- **用途**: 创建公网隧道，让外部访问本地服务\n", "- **场景**: 将 Colab 中的 API 服务暴露给外部\n", "\n", "#### 4. **u<PERSON><PERSON> (0.35.0)**\n", "- **作用**: 高性能的 ASGI 服务器\n", "- **用途**: 运行 FastAPI 应用程序\n", "- **特点**: 支持异步处理，性能优异\n", "\n", "#### 5. **vllm (0.9.2)**\n", "- **作用**: 高性能大语言模型推理引擎\n", "- **用途**: 加载和运行 DeepSeek 模型\n", "- **优势**: 内存高效，推理速度快\n", "\n", "### ⚡ 安装过程\n", "下面的命令会安装所有必需的依赖包，请耐心等待安装完成。"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "collapsed": true, "id": "ebACUjhXSwzJ", "outputId": "128ed3db-53aa-48c7-ae04-2b2e4dc500de"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: fastapi==0.116.0 in /usr/local/lib/python3.11/dist-packages (0.116.0)\n", "Requirement already satisfied: nest-asyncio==1.6.0 in /usr/local/lib/python3.11/dist-packages (1.6.0)\n", "Collecting pyngrok==7.2.12\n", "  Downloading pyngrok-7.2.12-py3-none-any.whl.metadata (9.4 kB)\n", "Requirement already satisfied: uvicorn==0.35.0 in /usr/local/lib/python3.11/dist-packages (0.35.0)\n", "Collecting vllm==0.9.2\n", "  Downloading vllm-0.9.2-cp38-abi3-manylinux1_x86_64.whl.metadata (15 kB)\n", "Requirement already satisfied: starlette<0.47.0,>=0.40.0 in /usr/local/lib/python3.11/dist-packages (from fastapi==0.116.0) (0.46.2)\n", "Requirement already satisfied: pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4 in /usr/local/lib/python3.11/dist-packages (from fastapi==0.116.0) (2.11.7)\n", "Requirement already satisfied: typing-extensions>=4.8.0 in /usr/local/lib/python3.11/dist-packages (from fastapi==0.116.0) (4.14.1)\n", "Requirement already satisfied: PyYAML>=5.1 in /usr/local/lib/python3.11/dist-packages (from pyngrok==7.2.12) (6.0.2)\n", "Requirement already satisfied: click>=7.0 in /usr/local/lib/python3.11/dist-packages (from uvicorn==0.35.0) (8.2.1)\n", "Requirement already satisfied: h11>=0.8 in /usr/local/lib/python3.11/dist-packages (from uvicorn==0.35.0) (0.16.0)\n", "Requirement already satisfied: regex in /usr/local/lib/python3.11/dist-packages (from vllm==0.9.2) (2024.11.6)\n", "Requirement already satisfied: cachetools in /usr/local/lib/python3.11/dist-packages (from vllm==0.9.2) (5.5.2)\n", "Requirement already satisfied: psutil in /usr/local/lib/python3.11/dist-packages (from vllm==0.9.2) (5.9.5)\n", "Requirement already satisfied: sentencepiece in /usr/local/lib/python3.11/dist-packages (from vllm==0.9.2) (0.2.0)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (from vllm==0.9.2) (2.0.2)\n", "Requirement already satisfied: requests>=2.26.0 in /usr/local/lib/python3.11/dist-packages (from vllm==0.9.2) (2.32.3)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.11/dist-packages (from vllm==0.9.2) (4.67.1)\n", "Collecting blake3 (from vllm==0.9.2)\n", "  Downloading blake3-1.0.5-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.2 kB)\n", "Requirement already satisfied: py-cpuinfo in /usr/local/lib/python3.11/dist-packages (from vllm==0.9.2) (9.0.0)\n", "Requirement already satisfied: transformers>=4.51.1 in /usr/local/lib/python3.11/dist-packages (from vllm==0.9.2) (4.53.1)\n", "Requirement already satisfied: huggingface-hub>=0.33.0 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub[hf_xet]>=0.33.0->vllm==0.9.2) (0.33.2)\n", "Requirement already satisfied: tokenizers>=0.21.1 in /usr/local/lib/python3.11/dist-packages (from vllm==0.9.2) (0.21.2)\n", "Requirement already satisfied: protobuf in /usr/local/lib/python3.11/dist-packages (from vllm==0.9.2) (5.29.5)\n", "Requirement already satisfied: aiohttp in /usr/local/lib/python3.11/dist-packages (from vllm==0.9.2) (3.11.15)\n", "Collecting openai<=1.90.0,>=1.52.0 (from vllm==0.9.2)\n", "  Downloading openai-1.90.0-py3-none-any.whl.metadata (26 kB)\n", "Requirement already satisfied: prometheus_client>=0.18.0 in /usr/local/lib/python3.11/dist-packages (from vllm==0.9.2) (0.22.1)\n", "Requirement already satisfied: pillow in /usr/local/lib/python3.11/dist-packages (from vllm==0.9.2) (11.2.1)\n", "Collecting prometheus-fastapi-instrumentator>=7.0.0 (from vllm==0.9.2)\n", "  Downloading prometheus_fastapi_instrumentator-7.1.0-py3-none-any.whl.metadata (13 kB)\n", "Requirement already satisfied: tiktoken>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from vllm==0.9.2) (0.9.0)\n", "Collecting lm-format-enforcer<0.11,>=0.10.11 (from vllm==0.9.2)\n", "  Downloading lm_format_enforcer-0.10.11-py3-none-any.whl.metadata (17 kB)\n", "Collecting llguidance<0.8.0,>=0.7.11 (from vllm==0.9.2)\n", "  Downloading llguidance-0.7.30-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (10 kB)\n", "Collecting outlines==0.1.11 (from vllm==0.9.2)\n", "  Downloading outlines-0.1.11-py3-none-any.whl.metadata (17 kB)\n", "Collecting lark==1.2.2 (from vllm==0.9.2)\n", "  Downloading lark-1.2.2-py3-none-any.whl.metadata (1.8 kB)\n", "Collecting xgrammar==0.1.19 (from vllm==0.9.2)\n", "  Downloading xgrammar-0.1.19-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.6 kB)\n", "Requirement already satisfied: filelock>=3.16.1 in /usr/local/lib/python3.11/dist-packages (from vllm==0.9.2) (3.18.0)\n", "Collecting partial-json-parser (from vllm==0.9.2)\n", "  Downloading partial_json_parser-*******.post6-py3-none-any.whl.metadata (6.1 kB)\n", "Collecting pyzmq>=25.0.0 (from vllm==0.9.2)\n", "  Downloading pyzmq-27.0.0-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl.metadata (6.0 kB)\n", "Collecting msgspec (from vllm==0.9.2)\n", "  Downloading msgspec-0.19.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.9 kB)\n", "Collecting gguf>=0.13.0 (from vllm==0.9.2)\n", "  Downloading gguf-0.17.1-py3-none-any.whl.metadata (4.3 kB)\n", "Collecting mistral_common>=1.6.2 (from mistral_common[opencv]>=1.6.2->vllm==0.9.2)\n", "  Downloading mistral_common-1.7.0-py3-none-any.whl.metadata (3.3 kB)\n", "Requirement already satisfied: opencv-python-headless>=4.11.0 in /usr/local/lib/python3.11/dist-packages (from vllm==0.9.2) (4.12.0.88)\n", "Requirement already satisfied: einops in /usr/local/lib/python3.11/dist-packages (from vllm==0.9.2) (0.8.1)\n", "Collecting compressed-tensors==0.10.2 (from vllm==0.9.2)\n", "  Downloading compressed_tensors-0.10.2-py3-none-any.whl.metadata (7.0 kB)\n", "Collecting depyf==0.18.0 (from vllm==0.9.2)\n", "  Downloading depyf-0.18.0-py3-none-any.whl.metadata (7.1 kB)\n", "Requirement already satisfied: cloudpickle in /usr/local/lib/python3.11/dist-packages (from vllm==0.9.2) (3.1.1)\n", "Collecting watchfiles (from vllm==0.9.2)\n", "  Downloading watchfiles-1.1.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.9 kB)\n", "Collecting python-json-logger (from vllm==0.9.2)\n", "  Downloading python_json_logger-3.3.0-py3-none-any.whl.metadata (4.0 kB)\n", "Requirement already satisfied: scipy in /usr/local/lib/python3.11/dist-packages (from vllm==0.9.2) (1.15.3)\n", "Collecting ninja (from vllm==0.9.2)\n", "  Downloading ninja-********-py3-none-manylinux_2_12_x86_64.manylinux2010_x86_64.whl.metadata (5.0 kB)\n", "Collecting pybase64 (from vllm==0.9.2)\n", "  Downloading pybase64-1.4.1-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (8.4 kB)\n", "Collecting numba==0.61.2 (from vllm==0.9.2)\n", "  Downloading numba-0.61.2-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (2.8 kB)\n", "Collecting ray!=2.44.*,>=2.43.0 (from ray[cgraph]!=2.44.*,>=2.43.0->vllm==0.9.2)\n", "  Downloading ray-2.47.1-cp311-cp311-manylinux2014_x86_64.whl.metadata (20 kB)\n", "Collecting torch==2.7.0 (from vllm==0.9.2)\n", "  Downloading torch-2.7.0-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (29 kB)\n", "Collecting torchaudio==2.7.0 (from vllm==0.9.2)\n", "  Downloading torchaudio-2.7.0-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (6.6 kB)\n", "Collecting torchvision==0.22.0 (from vllm==0.9.2)\n", "  Downloading torchvision-0.22.0-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (6.1 kB)\n", "Collecting xformers==0.0.30 (from vllm==0.9.2)\n", "  Downloading xformers-0.0.30-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (1.0 kB)\n", "Collecting astor (from depyf==0.18.0->vllm==0.9.2)\n", "  Downloading astor-0.8.1-py2.py3-none-any.whl.metadata (4.2 kB)\n", "Requirement already satisfied: dill in /usr/local/lib/python3.11/dist-packages (from depyf==0.18.0->vllm==0.9.2) (0.3.7)\n", "Collecting llvmlite<0.45,>=0.44.0dev0 (from numba==0.61.2->vllm==0.9.2)\n", "  Downloading llvmlite-0.44.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.8 kB)\n", "Collecting interegular (from outlines==0.1.11->vllm==0.9.2)\n", "  Downloading interegular-0.3.3-py37-none-any.whl.metadata (3.0 kB)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from outlines==0.1.11->vllm==0.9.2) (3.1.6)\n", "Collecting diskcache (from outlines==0.1.11->vllm==0.9.2)\n", "  Downloading diskcache-5.6.3-py3-none-any.whl.metadata (20 kB)\n", "Requirement already satisfied: referencing in /usr/local/lib/python3.11/dist-packages (from outlines==0.1.11->vllm==0.9.2) (0.36.2)\n", "Requirement already satisfied: jsonschema in /usr/local/lib/python3.11/dist-packages (from outlines==0.1.11->vllm==0.9.2) (4.24.0)\n", "Collecting pycountry (from outlines==0.1.11->vllm==0.9.2)\n", "  Downloading pycountry-24.6.1-py3-none-any.whl.metadata (12 kB)\n", "Collecting airportsdata (from outlines==0.1.11->vllm==0.9.2)\n", "  Downloading airportsdata-********-py3-none-any.whl.metadata (9.1 kB)\n", "Collecting outlines_core==0.1.26 (from outlines==0.1.11->vllm==0.9.2)\n", "  Downloading outlines_core-0.1.26-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.8 kB)\n", "Collecting sympy>=1.13.3 (from torch==2.7.0->vllm==0.9.2)\n", "  Downloading sympy-1.14.0-py3-none-any.whl.metadata (12 kB)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch==2.7.0->vllm==0.9.2) (3.5)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch==2.7.0->vllm==0.9.2) (2025.3.2)\n", "Collecting nvidia-cuda-nvrtc-cu12==12.6.77 (from torch==2.7.0->vllm==0.9.2)\n", "  Downloading nvidia_cuda_nvrtc_cu12-12.6.77-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-runtime-cu12==12.6.77 (from torch==2.7.0->vllm==0.9.2)\n", "  Downloading nvidia_cuda_runtime_cu12-12.6.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-cupti-cu12==12.6.80 (from torch==2.7.0->vllm==0.9.2)\n", "  Downloading nvidia_cuda_cupti_cu12-12.6.80-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cudnn-cu12==9.5.1.17 (from torch==2.7.0->vllm==0.9.2)\n", "  Downloading nvidia_cudnn_cu12-9.5.1.17-py3-none-manylinux_2_28_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cublas-cu12==12.6.4.1 (from torch==2.7.0->vllm==0.9.2)\n", "  Downloading nvidia_cublas_cu12-12.6.4.1-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cufft-cu12==11.3.0.4 (from torch==2.7.0->vllm==0.9.2)\n", "  Downloading nvidia_cufft_cu12-11.3.0.4-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-curand-cu12==10.3.7.77 (from torch==2.7.0->vllm==0.9.2)\n", "  Downloading nvidia_curand_cu12-10.3.7.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cusolver-cu12==11.7.1.2 (from torch==2.7.0->vllm==0.9.2)\n", "  Downloading nvidia_cusolver_cu12-11.7.1.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cusparse-cu12==12.5.4.2 (from torch==2.7.0->vllm==0.9.2)\n", "  Downloading nvidia_cusparse_cu12-12.5.4.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cusparselt-cu12==0.6.3 (from torch==2.7.0->vllm==0.9.2)\n", "  Downloading nvidia_cusparselt_cu12-0.6.3-py3-none-manylinux2014_x86_64.whl.metadata (6.8 kB)\n", "Collecting nvidia-nccl-cu12==2.26.2 (from torch==2.7.0->vllm==0.9.2)\n", "  Downloading nvidia_nccl_cu12-2.26.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (2.0 kB)\n", "Collecting nvidia-nvtx-cu12==12.6.77 (from torch==2.7.0->vllm==0.9.2)\n", "  Downloading nvidia_nvtx_cu12-12.6.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-nvjitlink-cu12==12.6.85 (from torch==2.7.0->vllm==0.9.2)\n", "  Downloading nvidia_nvjitlink_cu12-12.6.85-py3-none-manylinux2010_x86_64.manylinux_2_12_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cufile-cu12==1.11.1.6 (from torch==2.7.0->vllm==0.9.2)\n", "  Downloading nvidia_cufile_cu12-1.11.1.6-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\n", "Collecting triton==3.3.0 (from torch==2.7.0->vllm==0.9.2)\n", "  Downloading triton-3.3.0-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl.metadata (1.5 kB)\n", "Requirement already satisfied: setuptools>=40.8.0 in /usr/local/lib/python3.11/dist-packages (from triton==3.3.0->torch==2.7.0->vllm==0.9.2) (75.2.0)\n", "Collecting fastapi-cli>=0.0.8 (from fastapi-cli[standard]>=0.0.8; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm==0.9.2)\n", "  Downloading fastapi_cli-0.0.8-py3-none-any.whl.metadata (6.3 kB)\n", "Requirement already satisfied: httpx>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from fastapi[standard]>=0.115.0->vllm==0.9.2) (0.28.1)\n", "Requirement already satisfied: python-multipart>=0.0.18 in /usr/local/lib/python3.11/dist-packages (from fastapi[standard]>=0.115.0->vllm==0.9.2) (0.0.20)\n", "Collecting email-validator>=2.0.0 (from fastapi[standard]>=0.115.0->vllm==0.9.2)\n", "  Downloading email_validator-2.2.0-py3-none-any.whl.metadata (25 kB)\n", "Requirement already satisfied: packaging>=20.9 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub>=0.33.0->huggingface-hub[hf_xet]>=0.33.0->vllm==0.9.2) (24.2)\n", "Requirement already satisfied: hf-xet<2.0.0,>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub>=0.33.0->huggingface-hub[hf_xet]>=0.33.0->vllm==0.9.2) (1.1.5)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.11/dist-packages (from openai<=1.90.0,>=1.52.0->vllm==0.9.2) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/local/lib/python3.11/dist-packages (from openai<=1.90.0,>=1.52.0->vllm==0.9.2) (1.9.0)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from openai<=1.90.0,>=1.52.0->vllm==0.9.2) (0.10.0)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.11/dist-packages (from openai<=1.90.0,>=1.52.0->vllm==0.9.2) (1.3.1)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4->fastapi==0.116.0) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /usr/local/lib/python3.11/dist-packages (from pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4->fastapi==0.116.0) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from pydantic!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0,>=1.7.4->fastapi==0.116.0) (0.4.1)\n", "Requirement already satisfied: msgpack<2.0.0,>=1.0.0 in /usr/local/lib/python3.11/dist-packages (from ray!=2.44.*,>=2.43.0->ray[cgraph]!=2.44.*,>=2.43.0->vllm==0.9.2) (1.1.1)\n", "Requirement already satisfied: cupy-cuda12x in /usr/local/lib/python3.11/dist-packages (from ray[cgraph]!=2.44.*,>=2.43.0->vllm==0.9.2) (13.3.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests>=2.26.0->vllm==0.9.2) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests>=2.26.0->vllm==0.9.2) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests>=2.26.0->vllm==0.9.2) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests>=2.26.0->vllm==0.9.2) (2025.7.9)\n", "Requirement already satisfied: safetensors>=0.4.3 in /usr/local/lib/python3.11/dist-packages (from transformers>=4.51.1->vllm==0.9.2) (0.5.3)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->vllm==0.9.2) (2.6.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from aiohttp->vllm==0.9.2) (1.4.0)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->vllm==0.9.2) (25.3.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.11/dist-packages (from aiohttp->vllm==0.9.2) (1.7.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.11/dist-packages (from aiohttp->vllm==0.9.2) (6.6.3)\n", "Requirement already satisfied: propcache>=0.2.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->vllm==0.9.2) (0.3.2)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->vllm==0.9.2) (1.20.1)\n", "Collecting dnspython>=2.0.0 (from email-validator>=2.0.0->fastapi[standard]>=0.115.0->vllm==0.9.2)\n", "  Downloading dnspython-2.7.0-py3-none-any.whl.metadata (5.8 kB)\n", "Requirement already satisfied: typer>=0.15.1 in /usr/local/lib/python3.11/dist-packages (from fastapi-cli>=0.0.8->fastapi-cli[standard]>=0.0.8; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm==0.9.2) (0.16.0)\n", "Collecting rich-toolkit>=0.14.8 (from fastapi-cli>=0.0.8->fastapi-cli[standard]>=0.0.8; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm==0.9.2)\n", "  Downloading rich_toolkit-0.14.8-py3-none-any.whl.metadata (999 bytes)\n", "Collecting fastapi-cloud-cli>=0.1.1 (from fastapi-cli[standard]>=0.0.8; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm==0.9.2)\n", "  Downloading fastapi_cloud_cli-0.1.4-py3-none-any.whl.metadata (3.2 kB)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx>=0.23.0->fastapi[standard]>=0.115.0->vllm==0.9.2) (1.0.9)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->outlines==0.1.11->vllm==0.9.2) (3.0.2)\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /usr/local/lib/python3.11/dist-packages (from jsonschema->outlines==0.1.11->vllm==0.9.2) (2025.4.1)\n", "Requirement already satisfied: rpds-py>=0.7.1 in /usr/local/lib/python3.11/dist-packages (from jsonschema->outlines==0.1.11->vllm==0.9.2) (0.26.0)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy>=1.13.3->torch==2.7.0->vllm==0.9.2) (1.3.0)\n", "Collecting httptools>=0.6.3 (from uvicorn[standard]>=0.12.0; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm==0.9.2)\n", "  Downloading httptools-0.6.4-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.6 kB)\n", "Collecting python-dotenv>=0.13 (from uvicorn[standard]>=0.12.0; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm==0.9.2)\n", "  Downloading python_dotenv-1.1.1-py3-none-any.whl.metadata (24 kB)\n", "Collecting uvloop>=0.15.1 (from uvicorn[standard]>=0.12.0; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm==0.9.2)\n", "  Downloading uvloop-0.21.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.9 kB)\n", "Requirement already satisfied: websockets>=10.4 in /usr/local/lib/python3.11/dist-packages (from uvicorn[standard]>=0.12.0; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm==0.9.2) (15.0.1)\n", "Requirement already satisfied: fastrlock>=0.5 in /usr/local/lib/python3.11/dist-packages (from cupy-cuda12x->ray[cgraph]!=2.44.*,>=2.43.0->vllm==0.9.2) (0.8.3)\n", "Collecting rignore>=0.5.1 (from fastapi-cloud-cli>=0.1.1->fastapi-cli[standard]>=0.0.8; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm==0.9.2)\n", "  Downloading rignore-0.6.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.8 kB)\n", "Requirement already satisfied: sentry-sdk>=2.20.0 in /usr/local/lib/python3.11/dist-packages (from fastapi-cloud-cli>=0.1.1->fastapi-cli[standard]>=0.0.8; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm==0.9.2) (2.32.0)\n", "Requirement already satisfied: rich>=13.7.1 in /usr/local/lib/python3.11/dist-packages (from rich-toolkit>=0.14.8->fastapi-cli>=0.0.8->fastapi-cli[standard]>=0.0.8; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm==0.9.2) (13.9.4)\n", "Requirement already satisfied: shellingham>=1.3.0 in /usr/local/lib/python3.11/dist-packages (from typer>=0.15.1->fastapi-cli>=0.0.8->fastapi-cli[standard]>=0.0.8; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm==0.9.2) (1.5.4)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /usr/local/lib/python3.11/dist-packages (from rich>=13.7.1->rich-toolkit>=0.14.8->fastapi-cli>=0.0.8->fastapi-cli[standard]>=0.0.8; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm==0.9.2) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /usr/local/lib/python3.11/dist-packages (from rich>=13.7.1->rich-toolkit>=0.14.8->fastapi-cli>=0.0.8->fastapi-cli[standard]>=0.0.8; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm==0.9.2) (2.19.2)\n", "Requirement already satisfied: mdurl~=0.1 in /usr/local/lib/python3.11/dist-packages (from markdown-it-py>=2.2.0->rich>=13.7.1->rich-toolkit>=0.14.8->fastapi-cli>=0.0.8->fastapi-cli[standard]>=0.0.8; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm==0.9.2) (0.1.2)\n", "Downloading pyngrok-7.2.12-py3-none-any.whl (26 kB)\n", "Downloading vllm-0.9.2-cp38-abi3-manylinux1_x86_64.whl (383.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m383.4/383.4 MB\u001b[0m \u001b[31m3.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading compressed_tensors-0.10.2-py3-none-any.whl (169 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m169.0/169.0 kB\u001b[0m \u001b[31m13.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading depyf-0.18.0-py3-none-any.whl (38 kB)\n", "Downloading lark-1.2.2-py3-none-any.whl (111 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m111.0/111.0 kB\u001b[0m \u001b[31m10.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading numba-0.61.2-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (3.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.8/3.8 MB\u001b[0m \u001b[31m107.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading outlines-0.1.11-py3-none-any.whl (87 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m87.6/87.6 kB\u001b[0m \u001b[31m8.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading torch-2.7.0-cp311-cp311-manylinux_2_28_x86_64.whl (865.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m865.2/865.2 MB\u001b[0m \u001b[31m1.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading torchaudio-2.7.0-cp311-cp311-manylinux_2_28_x86_64.whl (3.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.5/3.5 MB\u001b[0m \u001b[31m106.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading torchvision-0.22.0-cp311-cp311-manylinux_2_28_x86_64.whl (7.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m7.4/7.4 MB\u001b[0m \u001b[31m101.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading xformers-0.0.30-cp311-cp311-manylinux_2_28_x86_64.whl (31.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m31.5/31.5 MB\u001b[0m \u001b[31m33.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading xgrammar-0.1.19-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (5.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m5.8/5.8 MB\u001b[0m \u001b[31m76.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cublas_cu12-12.6.4.1-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (393.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m393.1/393.1 MB\u001b[0m \u001b[31m3.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_cupti_cu12-12.6.80-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (8.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m8.9/8.9 MB\u001b[0m \u001b[31m60.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_nvrtc_cu12-12.6.77-py3-none-manylinux2014_x86_64.whl (23.7 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m23.7/23.7 MB\u001b[0m \u001b[31m33.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_runtime_cu12-12.6.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (897 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m897.7/897.7 kB\u001b[0m \u001b[31m44.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cudnn_cu12-9.5.1.17-py3-none-manylinux_2_28_x86_64.whl (571.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m571.0/571.0 MB\u001b[0m \u001b[31m3.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cufft_cu12-11.3.0.4-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (200.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m200.2/200.2 MB\u001b[0m \u001b[31m5.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cufile_cu12-1.11.1.6-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (1.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.1/1.1 MB\u001b[0m \u001b[31m67.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_curand_cu12-10.3.7.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (56.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m13.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cusolver_cu12-11.7.1.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (158.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m158.2/158.2 MB\u001b[0m \u001b[31m7.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cusparse_cu12-12.5.4.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (216.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m216.6/216.6 MB\u001b[0m \u001b[31m6.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cusparselt_cu12-0.6.3-py3-none-manylinux2014_x86_64.whl (156.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m156.8/156.8 MB\u001b[0m \u001b[31m6.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_nccl_cu12-2.26.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (201.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m201.3/201.3 MB\u001b[0m \u001b[31m6.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_nvjitlink_cu12-12.6.85-py3-none-manylinux2010_x86_64.manylinux_2_12_x86_64.whl (19.7 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m19.7/19.7 MB\u001b[0m \u001b[31m98.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_nvtx_cu12-12.6.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (89 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m89.3/89.3 kB\u001b[0m \u001b[31m8.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading outlines_core-0.1.26-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (343 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m343.3/343.3 kB\u001b[0m \u001b[31m27.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading triton-3.3.0-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl (156.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m156.5/156.5 MB\u001b[0m \u001b[31m6.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading gguf-0.17.1-py3-none-any.whl (96 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m96.2/96.2 kB\u001b[0m \u001b[31m8.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading llguidance-0.7.30-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (15.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m15.0/15.0 MB\u001b[0m \u001b[31m113.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading lm_format_enforcer-0.10.11-py3-none-any.whl (44 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m44.2/44.2 kB\u001b[0m \u001b[31m4.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mistral_common-1.7.0-py3-none-any.whl (6.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.5/6.5 MB\u001b[0m \u001b[31m115.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading openai-1.90.0-py3-none-any.whl (734 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m734.6/734.6 kB\u001b[0m \u001b[31m54.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading prometheus_fastapi_instrumentator-7.1.0-py3-none-any.whl (19 kB)\n", "Downloading pyzmq-27.0.0-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl (856 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m856.6/856.6 kB\u001b[0m \u001b[31m59.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading ray-2.47.1-cp311-cp311-manylinux2014_x86_64.whl (68.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m68.9/68.9 MB\u001b[0m \u001b[31m10.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading blake3-1.0.5-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (385 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m385.5/385.5 kB\u001b[0m \u001b[31m29.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading msgspec-0.19.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (210 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m210.7/210.7 kB\u001b[0m \u001b[31m19.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading ninja-********-py3-none-manylinux_2_12_x86_64.manylinux2010_x86_64.whl (422 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m422.8/422.8 kB\u001b[0m \u001b[31m35.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading partial_json_parser-*******.post6-py3-none-any.whl (10 kB)\n", "Downloading pybase64-1.4.1-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (71 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m71.2/71.2 kB\u001b[0m \u001b[31m7.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading python_json_logger-3.3.0-py3-none-any.whl (15 kB)\n", "Downloading watchfiles-1.1.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (453 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m453.1/453.1 kB\u001b[0m \u001b[31m35.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading email_validator-2.2.0-py3-none-any.whl (33 kB)\n", "Downloading fastapi_cli-0.0.8-py3-none-any.whl (10 kB)\n", "Downloading interegular-0.3.3-py37-none-any.whl (23 kB)\n", "Downloading llvmlite-0.44.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (42.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m42.4/42.4 MB\u001b[0m \u001b[31m21.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading sympy-1.14.0-py3-none-any.whl (6.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.3/6.3 MB\u001b[0m \u001b[31m81.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading airportsdata-********-py3-none-any.whl (912 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m912.7/912.7 kB\u001b[0m \u001b[31m50.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading astor-0.8.1-py2.py3-none-any.whl (27 kB)\n", "Downloading diskcache-5.6.3-py3-none-any.whl (45 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m45.5/45.5 kB\u001b[0m \u001b[31m3.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pycountry-24.6.1-py3-none-any.whl (6.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.3/6.3 MB\u001b[0m \u001b[31m89.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading dnspython-2.7.0-py3-none-any.whl (313 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m313.6/313.6 kB\u001b[0m \u001b[31m27.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading fastapi_cloud_cli-0.1.4-py3-none-any.whl (18 kB)\n", "Downloading httptools-0.6.4-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (459 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m459.8/459.8 kB\u001b[0m \u001b[31m37.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading python_dotenv-1.1.1-py3-none-any.whl (20 kB)\n", "Downloading rich_toolkit-0.14.8-py3-none-any.whl (24 kB)\n", "Downloading uvloop-0.21.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (4.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4.0/4.0 MB\u001b[0m \u001b[31m76.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading rignore-0.6.2-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (950 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m950.5/950.5 kB\u001b[0m \u001b[31m54.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: nvidia-cusparselt-cu12, blake3, uvloop, triton, sympy, rignore, pyzmq, python-json-logger, python-dotenv, pyngrok, pycountry, pybase64, partial-json-parser, nvidia-nvtx-cu12, nvidia-nvjitlink-cu12, nvidia-nccl-cu12, nvidia-curand-cu12, nvidia-cufile-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, ninja, msgspec, llvmlite, llguidance, lark, interegular, httptools, gguf, dnspython, diskcache, astor, airportsdata, watchfiles, nvidia-cusparse-cu12, nvidia-cufft-cu12, nvidia-cudnn-cu12, numba, email-validator, depyf, rich-toolkit, prometheus-fastapi-instrumentator, openai, nvidia-cusolver-cu12, lm-format-enforcer, torch, ray, outlines_core, mistral_common, fastapi-cloud-cli, fastapi-cli, xgrammar, xformers, torchvision, torchaudio, outlines, compressed-tensors, vllm\n", "  Attempting uninstall: nvidia-cusparselt-cu12\n", "    Found existing installation: nvidia-cusparselt-cu12 0.6.2\n", "    Uninstalling nvidia-cusparselt-cu12-0.6.2:\n", "      Successfully uninstalled nvidia-cusparselt-cu12-0.6.2\n", "  Attempting uninstall: triton\n", "    Found existing installation: triton 3.2.0\n", "    Uninstalling triton-3.2.0:\n", "      Successfully uninstalled triton-3.2.0\n", "  Attempting uninstall: sympy\n", "    Found existing installation: sympy 1.13.1\n", "    Uninstalling sympy-1.13.1:\n", "      Successfully uninstalled sympy-1.13.1\n", "  Attempting uninstall: pyzmq\n", "    Found existing installation: pyzmq 24.0.1\n", "    Uninstalling pyzmq-24.0.1:\n", "      Successfully uninstalled pyzmq-24.0.1\n", "  Attempting uninstall: nvidia-nvtx-cu12\n", "    Found existing installation: nvidia-nvtx-cu12 12.4.127\n", "    Uninstalling nvidia-nvtx-cu12-12.4.127:\n", "      Successfully uninstalled nvidia-nvtx-cu12-12.4.127\n", "  Attempting uninstall: nvidia-nvjitlink-cu12\n", "    Found existing installation: nvidia-nvjitlink-cu12 12.5.82\n", "    Uninstalling nvidia-nvjitlink-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-nvjitlink-cu12-12.5.82\n", "  Attempting uninstall: nvidia-nccl-cu12\n", "    Found existing installation: nvidia-nccl-cu12 2.21.5\n", "    Uninstalling nvidia-nccl-cu12-2.21.5:\n", "      Successfully uninstalled nvidia-nccl-cu12-2.21.5\n", "  Attempting uninstall: nvidia-curand-cu12\n", "    Found existing installation: nvidia-curand-cu12 10.3.6.82\n", "    Uninstalling nvidia-curand-cu12-10.3.6.82:\n", "      Successfully uninstalled nvidia-curand-cu12-10.3.6.82\n", "  Attempting uninstall: nvidia-cuda-runtime-cu12\n", "    Found existing installation: nvidia-cuda-runtime-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-runtime-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-runtime-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cuda-nvrtc-cu12\n", "    Found existing installation: nvidia-cuda-nvrtc-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-nvrtc-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-nvrtc-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cuda-cupti-cu12\n", "    Found existing installation: nvidia-cuda-cupti-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-cupti-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-cupti-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cublas-cu12\n", "    Found existing installation: nvidia-cublas-cu12 12.5.3.2\n", "    Uninstalling nvidia-cublas-cu12-12.5.3.2:\n", "      Successfully uninstalled nvidia-cublas-cu12-12.5.3.2\n", "  Attempting uninstall: llvmlite\n", "    Found existing installation: llvmlite 0.43.0\n", "    Uninstalling llvmlite-0.43.0:\n", "      Successfully uninstalled llvmlite-0.43.0\n", "  Attempting uninstall: nvidia-cusparse-cu12\n", "    Found existing installation: nvidia-cusparse-cu12 12.5.1.3\n", "    Uninstalling nvidia-cusparse-cu12-12.5.1.3:\n", "      Successfully uninstalled nvidia-cusparse-cu12-12.5.1.3\n", "  Attempting uninstall: nvidia-cufft-cu12\n", "    Found existing installation: nvidia-cufft-cu12 11.2.3.61\n", "    Uninstalling nvidia-cufft-cu12-11.2.3.61:\n", "      Successfully uninstalled nvidia-cufft-cu12-11.2.3.61\n", "  Attempting uninstall: nvidia-cudnn-cu12\n", "    Found existing installation: nvidia-cudnn-cu12 9.3.0.75\n", "    Uninstalling nvidia-cudnn-cu12-9.3.0.75:\n", "      Successfully uninstalled nvidia-cudnn-cu12-9.3.0.75\n", "  Attempting uninstall: numba\n", "    Found existing installation: numba 0.60.0\n", "    Uninstalling numba-0.60.0:\n", "      Successfully uninstalled numba-0.60.0\n", "  Attempting uninstall: openai\n", "    Found existing installation: openai 1.93.3\n", "    Uninstalling openai-1.93.3:\n", "      Successfully uninstalled openai-1.93.3\n", "  Attempting uninstall: nvidia-cusolver-cu12\n", "    Found existing installation: nvidia-cusolver-cu12 11.6.3.83\n", "    Uninstalling nvidia-cusolver-cu12-11.6.3.83:\n", "      Successfully uninstalled nvidia-cusolver-cu12-11.6.3.83\n", "  Attempting uninstall: torch\n", "    Found existing installation: torch 2.6.0+cu124\n", "    Uninstalling torch-2.6.0+cu124:\n", "      Successfully uninstalled torch-2.6.0+cu124\n", "  Attempting uninstall: torchvision\n", "    Found existing installation: torchvision 0.21.0+cu124\n", "    Uninstalling torchvision-0.21.0+cu124:\n", "      Successfully uninstalled torchvision-0.21.0+cu124\n", "  Attempting uninstall: <PERSON><PERSON><PERSON>\n", "    Found existing installation: torchaudio 2.6.0+cu124\n", "    Uninstalling torchaudio-2.6.0+cu124:\n", "      Successfully uninstalled torchaudio-2.6.0+cu124\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "fastai 2.7.19 requires torch<2.7,>=1.10, but you have torch 2.7.0 which is incompatible.\n", "cuml-cu12 25.2.1 requires numba<0.61.0a0,>=0.59.1, but you have numba 0.61.2 which is incompatible.\n", "distributed-ucxx-cu12 0.42.0 requires numba<0.61.0a0,>=0.59.1, but you have numba 0.61.2 which is incompatible.\n", "dask-cuda 25.2.0 requires numba<0.61.0a0,>=0.59.1, but you have numba 0.61.2 which is incompatible.\n", "cudf-cu12 25.2.1 requires numba<0.61.0a0,>=0.59.1, but you have numba 0.61.2 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mSuccessfully installed airportsdata-******** astor-0.8.1 blake3-1.0.5 compressed-tensors-0.10.2 depyf-0.18.0 diskcache-5.6.3 dnspython-2.7.0 email-validator-2.2.0 fastapi-cli-0.0.8 fastapi-cloud-cli-0.1.4 gguf-0.17.1 httptools-0.6.4 interegular-0.3.3 lark-1.2.2 llguidance-0.7.30 llvmlite-0.44.0 lm-format-enforcer-0.10.11 mistral_common-1.7.0 msgspec-0.19.0 ninja-******** numba-0.61.2 nvidia-cublas-cu12-12.6.4.1 nvidia-cuda-cupti-cu12-12.6.80 nvidia-cuda-nvrtc-cu12-12.6.77 nvidia-cuda-runtime-cu12-12.6.77 nvidia-cudnn-cu12-9.5.1.17 nvidia-cufft-cu12-11.3.0.4 nvidia-cufile-cu12-1.11.1.6 nvidia-curand-cu12-10.3.7.77 nvidia-cusolver-cu12-11.7.1.2 nvidia-cusparse-cu12-12.5.4.2 nvidia-cusparselt-cu12-0.6.3 nvidia-nccl-cu12-2.26.2 nvidia-nvjitlink-cu12-12.6.85 nvidia-nvtx-cu12-12.6.77 openai-1.90.0 outlines-0.1.11 outlines_core-0.1.26 partial-json-parser-*******.post6 prometheus-fastapi-instrumentator-7.1.0 pybase64-1.4.1 pycountry-24.6.1 pyngrok-7.2.12 python-dotenv-1.1.1 python-json-logger-3.3.0 pyzmq-27.0.0 ray-2.47.1 rich-toolkit-0.14.8 rignore-0.6.2 sympy-1.14.0 torch-2.7.0 torchaudio-2.7.0 torchvision-0.22.0 triton-3.3.0 uvloop-0.21.0 vllm-0.9.2 watchfiles-1.1.0 xformers-0.0.30 xgrammar-0.1.19\n"]}, {"output_type": "display_data", "data": {"application/vnd.colab-display-data+json": {"pip_warning": {"packages": ["zmq"]}, "id": "aec6141d45bf426484821c05f328b2f4"}}, "metadata": {}}], "source": ["# 📦 批量安装依赖包\n", "#\n", "# 这里使用 pip install 命令一次性安装所有必需的包\n", "# 使用 \\ 符号可以将长命令分成多行，提高可读性\n", "#\n", "# 安装过程可能需要几分钟时间，请耐心等待\n", "# 如果出现版本冲突，系统会自动处理依赖关系\n", "\n", "!pip install \\\n", "    fastapi==0.116.0 \\\n", "    nest-asyncio==1.6.0 \\\n", "    pyngrok==7.2.12 \\\n", "    uvicorn==0.35.0 \\\n", "    vllm==0.9.2"]}, {"cell_type": "markdown", "metadata": {"id": "jFI6d95RjNzd"}, "source": ["## 🚀 第三步：启动 VLLM 模型服务\n", "\n", "现在我们将使用 VLLM 在后台启动 DeepSeek R1 蒸馏版模型服务。\n", "\n", "### 🎯 VLLM 服务启动说明\n", "\n", "#### 🔍 模型选择\n", "- **模型来源**: [Hugging Face DeepSeek AI](https://huggingface.co/deepseek-ai/DeepSeek-R1#3-model-downloads)\n", "- **当前模型**: `deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B`\n", "- **参数规模**: 15亿参数，适合 T4 GPU 运行\n", "- **替换选项**: 可以替换为其他 DeepSeek R1 系列模型\n", "\n", "#### ⚙️ VLLM 参数解释\n", "- `serve`: VLLM 的服务模式命令\n", "- `--trust-remote-code`: 允许执行远程代码（模型配置）\n", "- `--dtype half`: 使用半精度浮点数，节省显存\n", "- `--max-model-len 16384`: 最大序列长度为 16K tokens\n", "- `--tensor-parallel-size 1`: 使用单卡推理\n", "\n", "#### 🔄 后台运行\n", "模型将在后台启动，不会阻塞当前进程，这样我们可以继续执行其他代码。\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "E4FjRHNbcFYl", "outputId": "8373be37-9270-4159-c8e3-9e0ee77a1f24"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🚀 正在启动 VLLM 服务，模型: deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B\n", "⏳ 首次运行需要下载模型，请耐心等待...\n", "✅ VLLM 服务启动命令已执行，正在后台加载模型...\n", "📡 服务将在 http://localhost:8000 上运行\n"]}], "source": ["# 🚀 启动 VLLM 模型服务\n", "#\n", "# 这个单元格的作用：\n", "# 1. 导入必要的 Python 模块\n", "# 2. 配置模型参数\n", "# 3. 使用 subprocess 在后台启动 VLLM 服务\n", "\n", "import os\n", "import subprocess\n", "\n", "# 📝 可选：配置 Hugging Face 镜像源\n", "# 如果在中国大陆访问 Hugging Face 较慢，可以启用下面这行\n", "# os.environ[\"HF_ENDPOINT\"] = \"https://hf-mirror.com\"\n", "\n", "# 🎯 模型配置\n", "# 指定要使用的模型名称\n", "# 这里使用的是 DeepSeek R1 的蒸馏版本，参数量为 15亿\n", "model = 'deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B'\n", "\n", "# 🔧 启动 VLLM 服务器\n", "# 使用 subprocess.Popen 在后台启动服务，这样不会阻塞当前进程\n", "print(f\"🚀 正在启动 VLLM 服务，模型: {model}\")\n", "print(\"⏳ 首次运行需要下载模型，请耐心等待...\")\n", "\n", "vllm_process = subprocess.Popen([\n", "    'vllm',                      # VLLM 命令\n", "    'serve',                     # 服务模式\n", "    model,                       # 模型名称\n", "    '--trust-remote-code',       # 信任远程代码\n", "    '--dtype', 'half',           # 使用半精度浮点数\n", "    '--max-model-len', '16384',  # 最大序列长度\n", "    '--tensor-parallel-size', '1' # 单卡推理\n", "], stdout=subprocess.PIPE, stderr=subprocess.PIPE, start_new_session=True)\n", "\n", "print(\"✅ VLLM 服务启动命令已执行，正在后台加载模型...\")\n", "print(\"📡 服务将在 http://localhost:8000 上运行\")"]}, {"cell_type": "markdown", "metadata": {"id": "BCR-O8i9lEbb"}, "source": ["## 🔍 第四步：监控 VLLM 服务状态\n", "\n", "由于 VLLM 在后台运行，我们需要监控其启动状态。\n", "\n", "### 🎯 监控的重要性\n", "\n", "#### 🔄 为什么需要监控？\n", "- **异步启动**: VLLM 在后台启动，需要时间加载模型\n", "- **状态确认**: 确保服务正常运行后再进行后续操作\n", "- **错误诊断**: 及时发现和处理启动过程中的问题\n", "- **资源管理**: 监控进程状态，避免资源泄漏\n", "\n", "#### ⏱️ 启动时间说明\n", "- **首次运行**: 需要下载模型文件，可能需要 5-10 分钟\n", "- **后续运行**: 模型已缓存，启动时间约 1-2 分钟\n", "- **检查频率**: 每 5 秒检查一次服务状态\n", "\n", "#### 🚦 状态检查机制\n", "- **健康检查**: 通过 HTTP 请求检查服务是否可用\n", "- **进程监控**: 监控 VLLM 进程的运行状态\n", "- **日志输出**: 显示启动过程中的关键信息"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "bx5v9mXkvqPo"}, "outputs": [], "source": ["# 🔍 VLLM 服务监控函数\n", "#\n", "# 这个单元格定义了两个重要的监控函数：\n", "# 1. check_vllm_status: 检查 VLLM 服务是否可用\n", "# 2. monitor_vllm_process: 持续监控 VLLM 进程状态\n", "\n", "import requests\n", "import time\n", "from typing import Tuple\n", "import sys\n", "\n", "def check_vllm_status(url: str = \"http://localhost:8000/health\") -> bool:\n", "    \"\"\"\n", "    🏥 检查 VLLM 服务器健康状态\n", "\n", "    参数:\n", "        url: 健康检查的 URL 地址\n", "\n", "    返回:\n", "        bool: True 表示服务正常，False 表示服务不可用\n", "\n", "    工作原理:\n", "        向 VLLM 的健康检查端点发送 GET 请求\n", "        如果返回 200 状态码，说明服务正常运行\n", "    \"\"\"\n", "    try:\n", "        response = requests.get(url, timeout=5)\n", "        return response.status_code == 200\n", "    except requests.exceptions.ConnectionError:\n", "        return False\n", "    except requests.exceptions.Timeout:\n", "        return False\n", "    except Exception:\n", "        return False\n", "\n", "def monitor_vllm_process(vllm_process: subprocess.Popen, check_interval: int = 5) -> Tuple[bool, str, str]:\n", "    \"\"\"\n", "    📊 监控 VLLM 进程的启动状态\n", "\n", "    参数:\n", "        vllm_process: VLLM 进程对象\n", "        check_interval: 检查间隔时间（秒）\n", "\n", "    返回:\n", "        Tuple[bool, str, str]: (是否成功, 标准输出, 标准错误)\n", "\n", "    工作流程:\n", "        1. 循环检查进程是否还在运行\n", "        2. 定期检查服务健康状态\n", "        3. 输出进程的日志信息\n", "        4. 返回最终状态\n", "    \"\"\"\n", "    print(\"🔍 开始 VLLM 服务器监控...\")\n", "    print(\"⏳ 正在等待服务启动，请耐心等待...\")\n", "\n", "    while vllm_process.poll() is None:  # 当进程仍在运行时\n", "        # 检查服务是否已经可用\n", "        if check_vllm_status():\n", "            print(\"✅ VLLM 服务器已启动并运行！\")\n", "            print(\"🎉 服务地址: http://localhost:8000\")\n", "            return True, \"\", \"\"\n", "\n", "        print(\"⏳ 等待 VLLM 服务器启动...\")\n", "        time.sleep(check_interval)\n", "\n", "        # 检查并输出进程日志\n", "        if vllm_process.stdout and vllm_process.stdout.readable():\n", "            try:\n", "                stdout = vllm_process.stdout.read1(1024).decode('utf-8')\n", "                if stdout.strip():\n", "                    print(\"📝 标准输出:\", stdout.strip())\n", "            except Exception:\n", "                pass\n", "\n", "        if vllm_process.stderr and vllm_process.stderr.readable():\n", "            try:\n", "                stderr = vllm_process.stderr.read1(1024).decode('utf-8')\n", "                if stderr.strip():\n", "                    print(\"⚠️ 标准错误:\", stderr.strip())\n", "            except Exception:\n", "                pass\n", "\n", "    # 如果到达这里，进程已结束（可能是错误）\n", "    print(\"❌ VLLM 进程已结束\")\n", "    try:\n", "        stdout, stderr = vllm_process.communicate(timeout=5)\n", "        return False, stdout.decode('utf-8'), stderr.decode('utf-8')\n", "    except Exception:\n", "        return False, \"\", \"进程通信超时\""]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "qEbWUS2nvRJo", "outputId": "7b5c34c2-a741-41e3-fde4-30da45a703cb"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🎯 开始监控 VLLM 服务启动状态...\n", "💡 提示：首次运行可能需要 5-10 分钟下载模型\n", "⌨️  按 Ctrl+C 可以中断监控（但不会停止 VLLM 服务）\n", "🔍 开始 VLLM 服务器监控...\n", "⏳ 正在等待服务启动，请耐心等待...\n", "⏳ 等待 VLLM 服务器启动...\n", "📝 标准输出: INFO 07-13 14:03:11 [__init__.py:244] Automatically detected platform cuda.\n", "⚠️ 标准错误: 2025-07-13 14:03:01.477167: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:477] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered\n", "WARNING: All log messages before absl::InitializeLog() is called are written to STDERR\n", "E0000 00:00:1752415381.763531    1563 cuda_dnn.cc:8310] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered\n", "E0000 00:00:1752415381.841022    1563 cuda_blas.cc:1418] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered\n", "2025-07-13 14:03:02.449984: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "⏳ 等待 VLLM 服务器启动...\n", "📝 标准输出: INFO 07-13 14:03:16 [api_server.py:1395] vLLM API server version 0.9.2\n", "INFO 07-13 14:03:16 [cli_args.py:325] non-default args: {'model': 'deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B', 'trust_remote_code': True, 'dtype': 'half', 'max_model_len': 16384}\n", "⚠️ 标准错误: 2025-07-13 14:03:37.857388: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:477] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered\n", "⏳ 等待 VLLM 服务器启动...\n", "📝 标准输出: INFO 07-13 14:03:32 [config.py:841] This model supports multiple tasks: {'generate', 'embed', 'classify', 'reward'}. Defaulting to 'generate'.\n", "WARNING 07-13 14:03:32 [config.py:3371] Casting torch.bfloat16 to torch.float16.\n", "INFO 07-13 14:03:32 [config.py:1472] Using max model len 16384\n", "WARNING 07-13 14:03:32 [arg_utils.py:1735] Compute Capability < 8.0 is not supported by the V1 Engine. Falling back to V0. \n", "INFO 07-13 14:03:32 [api_server.py:268] Started engine process with PID 1814\n", "⚠️ 标准错误: WARNING: All log messages before absl::InitializeLog() is called are written to STDERR\n", "E0000 00:00:1752415417.880640    1814 cuda_dnn.cc:8310] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered\n", "E0000 00:00:1752415417.886727    1814 cuda_blas.cc:1418] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered\n", "⏳ 等待 VLLM 服务器启动...\n", "📝 标准输出: INFO 07-13 14:03:43 [__init__.py:244] Automatically detected platform cuda.\n", "INFO 07-13 14:03:46 [llm_engine.py:230] Initializing a V0 LLM engine (v0.9.2) with config: model='deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B', speculative_config=None, tokenizer='deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config={}, tokenizer_revision=None, trust_remote_code=True, dtype=torch.float16, max_seq_len=16384, download_dir=None, load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto,  device_config=cuda, decoding_config=DecodingConfig(backend='auto', disable_fallback=False, disable_any_whitespace=False, disable_additional_properties=False, reasoning_backend=''), observability_config=ObservabilityConfig(show_hidden_metrics_for_version=None, otlp_traces_endpoint=None, collect_detailed_traces=None), seed=0, served_model_name=deepseek-a\n", "⚠️ 标准错误: Loading safetensors checkpoint shards:   0% Completed | 0/1 [00:00<?, ?it/s]\n", "⏳ 等待 VLLM 服务器启动...\n", "📝 标准输出: i/DeepSeek-R1-Distill-Qwen-1.5B, num_scheduler_steps=1, multi_step_stream_outputs=True, enable_prefix_caching=None, chunked_prefill_enabled=False, use_async_output_proc=True, pooler_config=None, compilation_config={\"level\":0,\"debug_dump_path\":\"\",\"cache_dir\":\"\",\"backend\":\"\",\"custom_ops\":[],\"splitting_ops\":[],\"use_inductor\":true,\"compile_sizes\":[],\"inductor_compile_config\":{\"enable_auto_functionalized_v2\":false},\"inductor_passes\":{},\"use_cudagraph\":true,\"cudagraph_num_of_warmups\":0,\"cudagraph_capture_sizes\":[256,248,240,232,224,216,208,200,192,184,176,168,160,152,144,136,128,120,112,104,96,88,80,72,64,56,48,40,32,24,16,8,4,2,1],\"cudagraph_copy_inputs\":false,\"full_cuda_graph\":false,\"max_capture_size\":256,\"local_cache_dir\":null}, use_cached_outputs=True, \n", "INFO 07-13 14:03:47 [cuda.py:311] Cannot use FlashAttention-2 backend for Volta and Turing GPUs.\n", "INFO 07-13 14:03:47 [cuda.py:360] Using XFormers backend.\n", "INFO 07-13 14:03:48 [parallel_state.py:1076] rank 0 in world size 1 is assigned as DP rank 0, PP rank 0, TP\n", "⚠️ 标准错误: Loading safetensors checkpoint shards: 100% Completed | 1/1 [00:10<00:00, 10.85s/it]\n", "⏳ 等待 VLLM 服务器启动...\n", "📝 标准输出: rank 0, EP rank 0\n", "INFO 07-13 14:03:48 [model_runner.py:1171] Starting to load model deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B...\n", "INFO 07-13 14:03:49 [weight_utils.py:292] Using model weights format ['*.safetensors']\n", "INFO 07-13 14:05:27 [weight_utils.py:308] Time spent downloading weights for deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B: 97.577624 seconds\n", "INFO 07-13 14:05:27 [weight_utils.py:345] No model.safetensors.index.json found in remote.\n", "INFO 07-13 14:05:38 [default_loader.py:272] Loading weights took 10.96 seconds\n", "INFO 07-13 14:05:39 [model_runner.py:1203] Model loading took 3.3461 GiB and 109.257677 seconds\n", "INFO 07-13 14:05:42 [worker.py:294] Memory profiling takes 3.11 seconds\n", "INFO 07-13 14:05:42 [worker.py:294] the current vLLM instance can use total_gpu_memory (14.74GiB) x gpu_memory_utilization (0.90) = 13.27GiB\n", "INFO 07-13 14:05:42 [worker.py:294] model weights take 3.35GiB; non_torch_memory takes 0.05GiB; PyTorch activation peak memory takes 1.43GiB; the rest of the memory reserved for KV Cache i\n", "⚠️ 标准错误: Loading safetensors checkpoint shards: 100% Completed | 1/1 [00:10<00:00, 10.85s/it]\n", "⏳ 等待 VLLM 服务器启动...\n", "📝 标准输出: s 8.44GiB.\n", "INFO 07-13 14:05:43 [executor_base.py:113] # cuda blocks: 19758, # CPU blocks: 9362\n", "INFO 07-13 14:05:43 [executor_base.py:118] Maximum concurrency for 16384 tokens per request: 19.29x\n", "⚠️ 标准错误: Capturing CUDA graph shapes:   0%|          | 0/35 [00:00<?, ?it/s]\n", "⏳ 等待 VLLM 服务器启动...\n", "📝 标准输出: INFO 07-13 14:05:49 [model_runner.py:1513] Capturing cudagraphs for decoding. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI. If out-of-memory error occurs during cudagraph capture, consider decreasing `gpu_memory_utilization` or switching to eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.\n", "Capturing CUDA graph shapes:  11%|█▏        | 4/35 [00:04<00:30,  1.02it/s]\n", "⏳ 等待 VLLM 服务器启动...\n", "📝 标准输出: INFO 07-13 14:06:26 [model_runner.py:1671] Graph capturing finished in 37 secs, took 0.19 GiB\n", "Capturing CUDA graph s\n", "⏳ 等待 VLLM 服务器启动...\n", "📝 标准输出: INFO 07-13 14:06:26 [llm_engine.py:428] init engine (profile, create kv cache, warmup model) took 47.41 seconds\n", "WARNING 07-13 14:06:28 [config.py:1392] Default sampling parameters have been overridden by the model's Hugging Face generation config recommended from the model creator. If this is not intended, please relaunch vLLM instance with `--generation-config vllm`.\n", "INFO 07-13 14:06:28 [serving_chat.py:125] Using default chat sampling params from model: {'temperature': 0.6, 'top_p': 0.95}\n", "INFO 07-13 14:06:28 [serving_completion.py:72] Using default completion sampling params from model: {'temperature': 0.6, 'top_p': 0.95}\n", "INFO 07-13 14:06:28 [api_server.py:1457] Starting vLLM API server 0 on http://0.0.0.0:8000\n", "INFO 07-13 14:06:28 [launcher.py:29] Available routes are:\n", "INFO 07-13 14:06:28 [launcher.py:37] Route: /openapi.json, Methods: GET, HEAD\n", "INFO 07-13 14:06:28 [launcher.py:37] Route: /docs, Methods: GET, HEAD\n", "INFO 07-13 14:06:28 [launcher.py:37] Route: /docs/oauth2-redirect, Methods: GET, HEAD\n", "INFO 07-\n", "✅ VLLM 服务器已启动并运行！\n", "🎉 服务地址: http://localhost:8000\n", "\n", "🎉 VLLM 服务启动成功！\n", "📡 API 服务地址: http://localhost:8000\n", "📚 API 文档地址: http://localhost:8000/docs\n", "✅ 现在可以继续运行后续单元格\n"]}], "source": ["# 🚀 执行 VLLM 服务监控\n", "#\n", "# 这个单元格的作用：\n", "# 1. 调用监控函数，等待 VLLM 服务启动\n", "# 2. 处理启动成功和失败的情况\n", "# 3. 支持用户中断操作\n", "\n", "print(\"🎯 开始监控 VLLM 服务启动状态...\")\n", "print(\"💡 提示：首次运行可能需要 5-10 分钟下载模型\")\n", "print(\"⌨️  按 Ctrl+C 可以中断监控（但不会停止 VLLM 服务）\")\n", "\n", "try:\n", "    # 调用监控函数，等待服务启动\n", "    success, stdout, stderr = monitor_vllm_process(vllm_process)\n", "\n", "    if not success:\n", "        print(\"\\n❌ VLLM 服务器启动失败！\")\n", "        print(\"\\n📋 完整标准输出:\")\n", "        print(stdout)\n", "        print(\"\\n🚨 完整标准错误:\")\n", "        print(stderr)\n", "        print(\"\\n🔧 可能的解决方案:\")\n", "        print(\"1. 检查 GPU 内存是否足够\")\n", "        print(\"2. 确认模型名称是否正确\")\n", "        print(\"3. 重新运行安装依赖包的单元格\")\n", "        sys.exit(1)\n", "    else:\n", "        print(\"\\n🎉 VLLM 服务启动成功！\")\n", "        print(\"📡 API 服务地址: http://localhost:8000\")\n", "        print(\"📚 API 文档地址: http://localhost:8000/docs\")\n", "        print(\"✅ 现在可以继续运行后续单元格\")\n", "\n", "except KeyboardInterrupt:\n", "    print(\"\\n⚠️ 用户中断监控\")\n", "    print(\"💡 注意：VLLM 服务仍在后台运行\")\n", "    print(\"🔄 如果需要停止 VLLM 服务，请重启 Colab 运行时\")\n", "\n", "    # 可选：强制停止 VLLM 进程\n", "    # 取消下面的注释可以在中断时停止服务\n", "    # print(\"🛑 正在停止 VLLM 服务...\")\n", "    # vllm_process.terminate()\n", "    # try:\n", "    #     vllm_process.wait(timeout=5)\n", "    #     print(\"✅ VLLM 服务已停止\")\n", "    # except subprocess.TimeoutExpired:\n", "    #     vllm_process.kill()\n", "    #     print(\"⚡ 强制终止 VLLM 服务\")\n", "\n", "    # 输出最终日志信息\n", "    try:\n", "        stdout, stderr = vllm_process.communicate(timeout=2)\n", "        if stdout:\n", "            print(\"\\n📝 最终标准输出:\")\n", "            print(stdout.decode('utf-8'))\n", "        if stderr:\n", "            print(\"\\n⚠️ 最终标准错误:\")\n", "            print(stderr.decode('utf-8'))\n", "    except:\n", "        print(\"📝 无法获取最终日志\")\n", "\n", "    sys.exit(0)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Qb6UVM067WHp", "outputId": "6a6e3888-3c22-4805-e7bc-5040dfb05d47"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🧪 测试模型推理功能...\n", "📝 发送测试问题: 法国的首都是什么？\n", "\n", "✅ 模型推理成功！\n", "📋 完整响应:\n", "{\n", "  \"id\": \"chatcmpl-b6b5c8a3d87e4c5abaa8d160da68f9da\",\n", "  \"object\": \"chat.completion\",\n", "  \"created\": 1752415594,\n", "  \"model\": \"deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B\",\n", "  \"choices\": [\n", "    {\n", "      \"index\": 0,\n", "      \"message\": {\n", "        \"role\": \"assistant\",\n", "        \"reasoning_content\": null,\n", "        \"content\": \"好，我现在要回答用户关于“法国的首都是什么？”的问题。首先，我知道法国的首都有两个，分别是巴黎和里德蒙特。巴黎是首都，而里德蒙特是省会。我需要确认这两个城市的位置和历史背景，这样回答才会准确。\\n\\n巴黎是法国的首都，也是全球最大的城市之一，拥有丰富的文化和历史。它位于巴黎海，是欧洲最繁华的城市之一。里德蒙特作为法国南部的一个城市，位于法国西南部，靠近波斯湾，也是一个重要的城市中心，尤其是在军事和农业方面。\\n\\n用户可能对法国的首都不太了解，或者是在做相关的研究，需要准确的信息。所以，我应该先介绍巴黎作为首都，然后提到里德蒙特，说明它们各自的位置和特点。这样用户可以全面了解法国的两个首都。\\n\\n另外，考虑到用户可能对法国的历史不太熟悉，我应该简要介绍一下每个城市的历史，比如巴黎的文艺复兴时期，里德蒙特的工业革命和军事历史。这样回答会更全面，满足用户的需求。\\n\\n最后，确保语言简洁明了，避免使用过于复杂的术语，让信息容易理解。同时，保持回答的结构清晰，分点列出，让用户一目了然。\\n</think>\\n\\n法国的首都有两个主要城市，分别是巴黎和里德蒙特（Riedler）。巴黎是法国的首都，也是全球最大的城市之一，位于巴黎海，是欧洲最繁华的城市之一。里德蒙特则位于法国西南部，靠近波斯湾，是法国南部的重要城市中心，尤其是在军事和农业方面。\\n\\n巴黎作为法国的首都，拥有丰富的历史和文化，是文艺复兴时期的中心之一。而里德蒙特则在工业革命和军事历史中占有重要地位，是法国南部的重要城市中心。\",\n", "        \"tool_calls\": []\n", "      },\n", "      \"logprobs\": null,\n", "      \"finish_reason\": \"stop\",\n", "      \"stop_reason\": null\n", "    }\n", "  ],\n", "  \"usage\": {\n", "    \"prompt_tokens\": 11,\n", "    \"total_tokens\": 385,\n", "    \"completion_tokens\": 374,\n", "    \"prompt_tokens_details\": null\n", "  },\n", "  \"prompt_logprobs\": null,\n", "  \"kv_transfer_params\": null\n", "}\n", "\n", "🤖 模型回答: 好，我现在要回答用户关于“法国的首都是什么？”的问题。首先，我知道法国的首都有两个，分别是巴黎和里德蒙特。巴黎是首都，而里德蒙特是省会。我需要确认这两个城市的位置和历史背景，这样回答才会准确。\n", "\n", "巴黎是法国的首都，也是全球最大的城市之一，拥有丰富的文化和历史。它位于巴黎海，是欧洲最繁华的城市之一。里德蒙特作为法国南部的一个城市，位于法国西南部，靠近波斯湾，也是一个重要的城市中心，尤其是在军事和农业方面。\n", "\n", "用户可能对法国的首都不太了解，或者是在做相关的研究，需要准确的信息。所以，我应该先介绍巴黎作为首都，然后提到里德蒙特，说明它们各自的位置和特点。这样用户可以全面了解法国的两个首都。\n", "\n", "另外，考虑到用户可能对法国的历史不太熟悉，我应该简要介绍一下每个城市的历史，比如巴黎的文艺复兴时期，里德蒙特的工业革命和军事历史。这样回答会更全面，满足用户的需求。\n", "\n", "最后，确保语言简洁明了，避免使用过于复杂的术语，让信息容易理解。同时，保持回答的结构清晰，分点列出，让用户一目了然。\n", "</think>\n", "\n", "法国的首都有两个主要城市，分别是巴黎和里德蒙特（Riedler）。巴黎是法国的首都，也是全球最大的城市之一，位于巴黎海，是欧洲最繁华的城市之一。里德蒙特则位于法国西南部，靠近波斯湾，是法国南部的重要城市中心，尤其是在军事和农业方面。\n", "\n", "巴黎作为法国的首都，拥有丰富的历史和文化，是文艺复兴时期的中心之一。而里德蒙特则在工业革命和军事历史中占有重要地位，是法国南部的重要城市中心。\n", "\n", "==================================================\n", "✅ 推理函数定义完成，可以继续下一步！\n"]}], "source": ["# 🧪 模型推理测试函数\n", "#\n", "# 这个单元格定义了两个核心函数：\n", "# 1. ask_model: 发送问题并获取完整回答\n", "# 2. stream_llm_response: 实现流式响应功能\n", "\n", "import requests\n", "import json\n", "from fastapi import FastAPI, HTTPException\n", "from pydantic import BaseModel\n", "from fastapi.responses import StreamingResponse\n", "\n", "# 📝 定义请求数据模型\n", "class QuestionRequest(BaseModel):\n", "    \"\"\"\n", "    API 请求的数据模型\n", "\n", "    属性:\n", "        question (str): 用户提出的问题\n", "\n", "    说明:\n", "        使用 Pydantic 模型确保数据类型安全\n", "        后续 FastAPI 会自动验证请求数据\n", "    \"\"\"\n", "    question: str\n", "\n", "def ask_model(question: str):\n", "    \"\"\"\n", "    🤖 向 VLLM 模型发送问题并获取完整回答\n", "\n", "    参数:\n", "        question (str): 用户提出的问题\n", "\n", "    返回:\n", "        dict: 包含模型回答的 JSON 响应\n", "\n", "    工作流程:\n", "        1. 构造符合 OpenAI API 格式的请求\n", "        2. 发送 POST 请求到 VLLM 服务\n", "        3. 处理响应并返回结果\n", "    \"\"\"\n", "    # VLLM 的 OpenAI 兼容 API 端点\n", "    url = \"http://localhost:8000/v1/chat/completions\"\n", "\n", "    # 设置请求头\n", "    headers = {\"Content-Type\": \"application/json\"}\n", "\n", "    # 构造请求数据（OpenAI 格式）\n", "    data = {\n", "        \"model\": model,  # 使用全局模型变量\n", "        \"messages\": [\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": question\n", "            }\n", "        ],\n", "        \"max_tokens\": 2048,  # 最大生成长度\n", "        \"temperature\": 0.7,  # 生成的随机性\n", "        \"top_p\": 0.9         # 核采样参数\n", "    }\n", "\n", "    try:\n", "        # 发送请求\n", "        response = requests.post(url, headers=headers, json=data, timeout=60)\n", "        response.raise_for_status()  # 检查 HTTP 错误\n", "        return response.json()\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"❌ 请求失败: {e}\")\n", "        return None\n", "\n", "def stream_llm_response(question: str):\n", "    \"\"\"\n", "    🌊 流式响应生成器 - 实时获取模型输出\n", "\n", "    参数:\n", "        question (str): 用户提出的问题\n", "\n", "    生成:\n", "        str: 逐行返回模型的生成内容\n", "\n", "    特点:\n", "        - 实时显示生成过程\n", "        - 降低等待时间\n", "        - 提供更好的用户体验\n", "    \"\"\"\n", "    url = \"http://localhost:8000/v1/chat/completions\"\n", "    headers = {\"Content-Type\": \"application/json\"}\n", "\n", "    # 启用流式传输\n", "    data = {\n", "        \"model\": model,\n", "        \"messages\": [{\"role\": \"user\", \"content\": question}],\n", "        \"stream\": True,      # 🔥 关键：启用流式传输\n", "        \"max_tokens\": 2048,\n", "        \"temperature\": 0.7\n", "    }\n", "\n", "    try:\n", "        with requests.post(url, headers=headers, json=data, stream=True, timeout=60) as response:\n", "            response.raise_for_status()\n", "\n", "            for line in response.iter_lines():\n", "                if line:\n", "                    # OpenAI 风格的流式响应以 \"data: \" 为前缀\n", "                    decoded_line = line.decode(\"utf-8\")\n", "                    if decoded_line.startswith(\"data: \"):\n", "                        decoded_line = decoded_line[6:]  # 移除 \"data: \" 前缀\n", "                    yield decoded_line + \"\\n\"\n", "    except requests.exceptions.RequestException as e:\n", "        yield f\"❌ 流式请求失败: {e}\\n\"\n", "\n", "# 🧪 测试基础推理功能\n", "print(\"🧪 测试模型推理功能...\")\n", "print(\"📝 发送测试问题: 法国的首都是什么？\")\n", "\n", "try:\n", "    result = ask_model(\"法国的首都是什么？\")\n", "    if result:\n", "        print(\"\\n✅ 模型推理成功！\")\n", "        print(\"📋 完整响应:\")\n", "        print(json.dumps(result, indent=2, ensure_ascii=False))\n", "\n", "        # 提取并显示模型回答\n", "        if \"choices\" in result and len(result[\"choices\"]) > 0:\n", "            answer = result[\"choices\"][0][\"message\"][\"content\"]\n", "            print(f\"\\n🤖 模型回答: {answer}\")\n", "    else:\n", "        print(\"❌ 模型推理失败\")\n", "except Exception as e:\n", "    print(f\"❌ 测试过程中出现错误: {e}\")\n", "\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"✅ 推理函数定义完成，可以继续下一步！\")\n"]}, {"cell_type": "markdown", "metadata": {"id": "RZ0mnu1nl9Ko"}, "source": ["## 🌐 第六步：创建 FastAPI Web 服务\n", "\n", "现在我们将创建一个 FastAPI Web 服务，将 VLLM 模型封装成易于使用的 REST API。\n", "\n", "### 🎯 FastAPI 服务说明\n", "\n", "#### 🔧 核心功能\n", "1. **RESTful API**: 提供标准的 HTTP 接口\n", "2. **自动文档**: 自动生成 Swagger UI 文档\n", "3. **数据验证**: 使用 Pydantic 进行请求验证\n", "4. **异步支持**: 支持高并发请求处理\n", "\n", "#### 📡 API 端点设计\n", "- **根路径** (`/`): 健康检查端点\n", "- **生成回答** (`/api/v1/generate-response`): 获取完整回答\n", "- **流式回答** (`/api/v1/generate-response-stream`): 实时流式输出\n", "\n", "#### 🔒 CORS 配置\n", "- 允许跨域访问，支持前端调用\n", "- 支持所有 HTTP 方法和头部\n", "- 便于与不同前端框架集成\n", "\n", "### 🚀 服务特点\n", "- **高性能**: 基于 ASGI 的异步框架\n", "- **易用性**: 简洁的 API 设计\n", "- **可扩展**: 支持添加更多功能\n", "- **标准化**: 遵循 REST API 设计规范"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2lAE878DSaSk", "outputId": "1a97ba07-13b3-42d5-d7c8-14f88559899e"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["✅ FastAPI 应用创建完成！\n", "📚 API 文档将在启动后访问: http://localhost:8081/docs\n", "🔄 准备启动 Web 服务...\n"]}], "source": ["# 🌐 创建 FastAPI Web 服务\n", "#\n", "# 这个单元格的作用：\n", "# 1. 初始化 FastAPI 应用\n", "# 2. 配置 CORS 跨域支持\n", "# 3. 定义 API 端点和路由\n", "\n", "from fastapi import FastAPI\n", "from fastapi.middleware.cors import CORSMiddleware\n", "import nest_asyncio\n", "from pyngrok import ngrok\n", "import uvicorn\n", "\n", "# 🚀 创建 FastAPI 应用实例\n", "app = FastAPI(\n", "    title=\"DeepSeek R1 API 服务\",\n", "    description=\"基于 VLLM 的 DeepSeek R1 蒸馏版模型 API 服务\",\n", "    version=\"1.0.0\",\n", "    docs_url=\"/docs\",  # Swagger UI 文档地址\n", "    redoc_url=\"/redoc\"  # ReDoc 文档地址\n", ")\n", "\n", "# 🔒 配置 CORS 跨域中间件\n", "# 允许前端应用从不同域名访问 API\n", "app.add_middleware(\n", "    CORSMiddleware,\n", "    allow_origins=['*'],        # 允许所有域名（生产环境应限制）\n", "    allow_credentials=True,     # 允许携带凭据\n", "    allow_methods=['*'],        # 允许所有 HTTP 方法\n", "    allow_headers=['*'],        # 允许所有请求头\n", ")\n", "\n", "# 🏠 根路径 - 健康检查端点\n", "@app.get('/')\n", "async def root():\n", "    \"\"\"\n", "    🏥 健康检查端点\n", "\n", "    返回:\n", "        dict: 包含服务状态信息\n", "    \"\"\"\n", "    return {\n", "        'status': 'healthy',\n", "        'message': 'DeepSeek R1 API 服务正在运行',\n", "        'model': model,\n", "        'version': '1.0.0'\n", "    }\n", "\n", "# 🤖 生成完整回答的 API 端点\n", "@app.post(\"/api/v1/generate-response\")\n", "def generate_response(request: QuestionRequest):\n", "    \"\"\"\n", "    📝 生成完整回答的 API 端点\n", "\n", "    参数:\n", "        request (QuestionRequest): 包含用户问题的请求对象\n", "\n", "    返回:\n", "        dict: 包含模型回答的响应\n", "\n", "    异常:\n", "        HTTPException: 当模型推理失败时抛出 500 错误\n", "    \"\"\"\n", "    try:\n", "        print(f\"📝 收到问题: {request.question}\")\n", "\n", "        # 调用模型推理函数\n", "        response = ask_model(request.question)\n", "\n", "        if response is None:\n", "            raise HTTPException(status_code=500, detail=\"模型推理失败\")\n", "\n", "        print(\"✅ 推理完成\")\n", "        return {\"response\": response}\n", "\n", "    except Exception as e:\n", "        print(f\"❌ 推理过程中出现错误: {str(e)}\")\n", "        raise HTTPException(status_code=500, detail=f\"推理失败: {str(e)}\")\n", "\n", "# 🌊 流式响应的 API 端点\n", "@app.post(\"/api/v1/generate-response-stream\")\n", "def stream_response(request: QuestionRequest):\n", "    \"\"\"\n", "    🌊 流式响应 API 端点\n", "\n", "    参数:\n", "        request (QuestionRequest): 包含用户问题的请求对象\n", "\n", "    返回:\n", "        StreamingResponse: 实时流式响应\n", "\n", "    特点:\n", "        - 实时返回生成内容\n", "        - 降低用户等待时间\n", "        - 提供更好的交互体验\n", "    \"\"\"\n", "    try:\n", "        print(f\"🌊 收到流式请求: {request.question}\")\n", "\n", "        # 调用流式响应生成器\n", "        response_generator = stream_llm_response(request.question)\n", "\n", "        return StreamingResponse(\n", "            response_generator,\n", "            media_type=\"text/event-stream\",\n", "            headers={\n", "                \"Cache-Control\": \"no-cache\",\n", "                \"Connection\": \"keep-alive\",\n", "                \"Access-Control-Allow-Origin\": \"*\"\n", "            }\n", "        )\n", "\n", "    except Exception as e:\n", "        print(f\"❌ 流式响应过程中出现错误: {str(e)}\")\n", "        raise HTTPException(status_code=500, detail=f\"流式响应失败: {str(e)}\")\n", "\n", "print(\"✅ FastAPI 应用创建完成！\")\n", "print(\"📚 API 文档将在启动后访问: http://localhost:8081/docs\")\n", "print(\"🔄 准备启动 Web 服务...\")"]}, {"cell_type": "markdown", "metadata": {"id": "uYuy3A6HmCma"}, "source": ["## 🌐 第六步：Ngrok 注册与配置\n", "\n", "### 🎯 什么是 Ngrok？\n", "\n", "Ngrok 是一个强大的内网穿透工具，可以将本地运行的服务暴露到公网上，让外部用户可以访问。在我们的场景中，它可以让其他人通过公网 URL 访问您在 Colab 中部署的模型 API。\n", "\n", "### 🔧 Ngrok 的作用\n", "\n", "#### 📡 核心功能\n", "1. **内网穿透**: 将本地服务映射到公网域名\n", "2. **HTTPS 支持**: 自动提供 HTTPS 加密连接\n", "3. **域名分配**: 分配一个临时的公网域名\n", "4. **流量监控**: 提供请求日志和监控功能\n", "\n", "#### 🎯 使用场景\n", "- **API 分享**: 与团队成员分享 API 接口\n", "- **远程测试**: 在不同设备上测试服务\n", "- **演示展示**: 向客户展示项目效果\n", "- **Webhook 接收**: 接收第三方服务的回调\n", "\n", "### 📝 Ngrok 注册流程\n", "\n", "#### 步骤 1：访问官网注册\n", "1. 打开 Ngrok 官网：[https://ngrok.com/](https://ngrok.com/)\n", "2. 点击右上角的 **\"Sign up\"** 按钮\n", "3. 选择注册方式：\n", "   - **GitHub 账号**: 推荐，一键登录\n", "   - **Google 账号**: 方便快捷\n", "   - **邮箱注册**: 传统方式\n", "\n", "#### 步骤 2：验证邮箱（如果使用邮箱注册）\n", "1. 填写邮箱地址和密码\n", "2. 查收验证邮件\n", "3. 点击邮件中的验证链接\n", "\n", "#### 步骤 3：完成账号设置\n", "1. 填写基本信息（可选）\n", "2. 选择使用目的（个人/商业）\n", "3. 完成注册流程\n", "\n", "### 🔑 获取 Auth<PERSON>en\n", "\n", "#### 方法 1：Dashboard 获取\n", "1. 登录后进入 [Ngrok Dashboard](https://dashboard.ngrok.com/)\n", "2. 在左侧导航栏找到 **\"Your Authtoken\"** 或 **\"Getting Started\"**\n", "3. 复制显示的 authtoken（格式类似：`1ABC2def3GHI4jkl5MNO6pqr7STU8vwx9YZ`）\n", "\n", "#### 方法 2：直接访问链接\n", "访问：[https://dashboard.ngrok.com/get-started/your-authtoken](https://dashboard.ngrok.com/get-started/your-authtoken)\n", "\n", "### ⚠️ Authtoken 安全提示\n", "\n", "#### 🔒 安全注意事项\n", "1. **保密性**: Token 相当于您的账号密码，不要公开分享\n", "2. **定期更换**: 建议定期重置 token 以确保安全\n", "3. **权限控制**: 免费账号有使用限制，付费账号功能更多\n", "4. **监控使用**: 定期检查 Dashboard 中的使用情况\n", "\n", "#### 📊 免费账号限制\n", "- **并发隧道**: 1个\n", "- **连接数**: 40个/分钟\n", "- **域名**: 随机分配\n", "- **会话时长**: 8小时\n", "\n", "### 🛠️ Token 配置方法\n", "\n", "下面的单元格将演示如何配置您的 authtoken："]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "HG8QGlyjU61y", "outputId": "083a64f2-4d3d-4616-be83-3f161e2e4fd0"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["✅ Ngrok authtoken 配置成功！\n", "🎉 现在可以创建公网隧道了\n"]}], "source": ["# 🔑 配置 <PERSON><PERSON>\n", "#\n", "# ⚠️ 重要提示：请将下面的 YOUR_AUTHTOKEN_HERE 替换为您从 Ngrok Dashboard 获取的真实 token\n", "#\n", "# 🔗 获取 token 的步骤：\n", "# 1. 访问：https://dashboard.ngrok.com/get-started/your-authtoken\n", "# 2. 登录您的 Ngrok 账号\n", "# 3. 复制显示的 authtoken\n", "# 4. 替换下面代码中的 YOUR_AUTHTOKEN_HERE\n", "\n", "# 📝 示例 token 格式（请替换为您的真实 token）：\n", "# YOUR_AUTHTOKEN = \"1ABC2def3GHI4jkl5MNO6pqr7STU8vwx9YZ\"\n", "\n", "# 🚨 请在下面填入您的真实 authtoken\n", "YOUR_AUTHTOKEN = \"Your Ngrok AuthToken\"  # 👈 请替换这里\n", "\n", "# 验证 token 是否已设置\n", "if YOUR_AUTHTOKEN == \"YOUR_AUTHTOKEN_HERE\":\n", "    print(\"❌ 请先设置您的 Ngrok authtoken！\")\n", "    print(\"📝 步骤：\")\n", "    print(\"1. 访问 https://dashboard.ngrok.com/get-started/your-authtoken\")\n", "    print(\"2. 登录并复制您的 authtoken\")\n", "    print(\"3. 将上面的 YOUR_AUTHTOKEN_HERE 替换为您的真实 token\")\n", "    print(\"4. 重新运行此单元格\")\n", "else:\n", "    # 配置 authtoken\n", "    import subprocess\n", "    result = subprocess.run(['ngrok', 'config', 'add-authtoken', YOUR_AUTHTOKEN],\n", "                          capture_output=True, text=True)\n", "    if result.returncode == 0:\n", "        print(\"✅ Ngrok authtoken 配置成功！\")\n", "        print(\"🎉 现在可以创建公网隧道了\")\n", "    else:\n", "        print(f\"❌ 配置失败: {result.stderr}\")\n", "        print(\"💡 请检查 token 是否正确\")"]}, {"cell_type": "raw", "metadata": {"id": "W3T6xnsWRuI9", "vscode": {"languageId": "raw"}}, "source": ["## 🚀 第七步：启动 Ngrok 隧道\n", "\n", "### 🌐 创建公网隧道\n", "\n", "配置好 authtoken 后，我们就可以创建 ngrok 隧道，将本地的 FastAPI 服务暴露到公网。\n", "\n", "### 🔧 Ngrok 隧道工作原理\n", "\n", "#### 📡 隧道机制\n", "1. **本地服务**: FastAPI 在 localhost:8081 运行\n", "2. **Ngrok 客户端**: 连接到 Ngrok 服务器\n", "3. **公网域名**: <PERSON><PERSON> 分配一个临时域名\n", "4. **流量转发**: 外部请求通过域名转发到本地服务\n", "\n", "#### 🌍 访问流程\n", "```\n", "外部用户 → https://abc123.ngrok.io → Ngrok服务器 → 本地FastAPI服务\n", "```\n", "\n", "### 📊 Ngrok 功能特性\n", "\n", "#### ✨ 主要功能\n", "- **HTTPS 加密**: 自动提供 SSL 证书\n", "- **实时监控**: Web 界面查看请求日志\n", "- **多种协议**: 支持 HTTP、HTTPS、TCP 等\n", "- **自定义域名**: 付费版支持自定义域名\n", "\n", "#### 🎯 适用场景\n", "- **API 测试**: 让前端开发者测试 API\n", "- **移动端调试**: 手机直接访问本地服务\n", "- **第三方集成**: 接收 Webhook 回调\n", "- **临时演示**: 快速分享项目成果\n", "\n", "### ⚠️ 注意事项\n", "\n", "#### 🔒 安全提醒\n", "1. **临时使用**: 隧道域名是临时的，重启后会变化\n", "2. **流量限制**: 免费版有并发和流量限制\n", "3. **安全风险**: 公网可访问，注意数据安全\n", "4. **监控访问**: 定期检查访问日志\n", "\n", "#### 📈 性能考虑\n", "- **延迟增加**: 通过 Ngrok 会增加网络延迟\n", "- **带宽限制**: 免费版有带宽限制\n", "- **稳定性**: 网络不稳定可能导致连接中断\n", "\n", "下面的单元格将创建 Ngrok 隧道：\n"]}, {"cell_type": "markdown", "metadata": {"id": "SHI9RCHdmJDf"}, "source": ["### 🌐 创建 Ngrok 隧道和启动服务\n", "\n", "现在我们将同时：\n", "1. 创建 Ngrok 隧道，将本地服务暴露到公网\n", "2. 启动 FastAPI 服务，提供 API 接口\n", "\n", "#### 🔄 执行顺序\n", "- 先创建 Ngrok 隧道（获取公网 URL）\n", "- 然后启动 FastAPI 服务（在指定端口运行）\n", "- 外部用户可以通过公网 URL 访问 API\n", "\n", "#### 💡 使用提示\n", "- 隧道创建成功后会显示公网 URL\n", "- 请保存这个 URL，用于外部访问\n", "- 服务启动后会阻塞当前单元格，这是正常现象\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "QQ3NYyCgTUdj", "outputId": "2b6b6d33-c18c-46db-8169-eeff6322f211"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["🚀 准备在端口 8081 上启动服务...\n", "🔗 正在创建 Ngrok 隧道...\n", "✅ Ngrok 隧道创建成功！\n", "🌍 公网访问地址: https://e77e931d6128.ngrok-free.app\n", "🔗 本地地址: http://127.0.0.1:8081\n", "\n", "📚 API 文档地址:\n", "   • Swagger UI: https://e77e931d6128.ngrok-free.app/docs\n", "   • ReDoc: https://e77e931d6128.ngrok-free.app/redoc\n", "\n", "🧪 API 端点:\n", "   • 健康检查: https://e77e931d6128.ngrok-free.app/\n", "   • 生成回答: https://e77e931d6128.ngrok-free.app/api/v1/generate-response\n", "   • 流式回答: https://e77e931d6128.ngrok-free.app/api/v1/generate-response-stream\n", "\n", "💡 提示：请保存上面的公网地址，用于外部访问\n", "\n", "📊 当前活跃隧道:\n", "   • http-8081-5bbfbe7c-96f3-469e-bfb1-93d55af1124c: https://e77e931d6128.ngrok-free.app -> http://localhost:8081\n"]}], "source": ["# 🌐 创建 Ngrok 隧道\n", "#\n", "# 这个单元格的作用：\n", "# 1. 设置 FastAPI 服务的端口\n", "# 2. 创建 Ngrok 隧道连接到该端口\n", "# 3. 获取公网访问 URL\n", "\n", "import time\n", "from pyngrok import ngrok\n", "\n", "# 📡 设置服务端口\n", "port = 8081\n", "print(f\"🚀 准备在端口 {port} 上启动服务...\")\n", "\n", "try:\n", "    # 🌐 创建 Ngrok 隧道\n", "    print(\"🔗 正在创建 Ngrok 隧道...\")\n", "\n", "    # 创建 HTTP 隧道\n", "    public_url = ngrok.connect(port).public_url\n", "\n", "    print(\"✅ Ngrok 隧道创建成功！\")\n", "    print(f\"🌍 公网访问地址: {public_url}\")\n", "    print(f\"🔗 本地地址: http://127.0.0.1:{port}\")\n", "    print()\n", "    print(\"📚 API 文档地址:\")\n", "    print(f\"   • Swagger UI: {public_url}/docs\")\n", "    print(f\"   • ReDoc: {public_url}/redoc\")\n", "    print()\n", "    print(\"🧪 API 端点:\")\n", "    print(f\"   • 健康检查: {public_url}/\")\n", "    print(f\"   • 生成回答: {public_url}/api/v1/generate-response\")\n", "    print(f\"   • 流式回答: {public_url}/api/v1/generate-response-stream\")\n", "    print()\n", "    print(\"💡 提示：请保存上面的公网地址，用于外部访问\")\n", "\n", "    # 🔍 显示 Ngrok 监控信息\n", "    tunnels = ngrok.get_tunnels()\n", "    if tunnels:\n", "        print(\"\\n📊 当前活跃隧道:\")\n", "        for tunnel in tunnels:\n", "            print(f\"   • {tunnel.name}: {tunnel.public_url} -> {tunnel.config['addr']}\")\n", "\n", "except Exception as e:\n", "    print(f\"❌ 创建 Ngrok 隧道失败: {e}\")\n", "    print(\"🔧 可能的解决方案:\")\n", "    print(\"1. 检查 authtoken 是否正确配置\")\n", "    print(\"2. 确认网络连接正常\")\n", "    print(\"3. 检查是否超出免费版限制\")\n", "    print(\"4. 重新运行 authtoken 配置单元格\")\n", "\n", "    # 显示当前配置的 authtoken（部分遮蔽）\n", "    try:\n", "        import subprocess\n", "        result = subprocess.run(['ngrok', 'config', 'check'], capture_output=True, text=True)\n", "        if result.returncode == 0:\n", "            print(\"\\n📋 当前 Ngrok 配置状态: 正常\")\n", "        else:\n", "            print(f\"\\n❌ Ngrok 配置检查失败: {result.stderr}\")\n", "    except:\n", "        print(\"\\n⚠️ 无法检查 Ngrok 配置状态\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "H_3xOk2mY55g", "outputId": "408ad98f-6044-4f4d-d683-31ecf6b3d6cf"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["INFO:     Started server process [239]\n", "INFO:     Waiting for application startup.\n", "INFO:     Application startup complete.\n", "INFO:     <PERSON><PERSON><PERSON> running on http://0.0.0.0:8081 (Press CTRL+C to quit)\n"]}, {"output_type": "stream", "name": "stdout", "text": ["🚀 启动 FastAPI Web 服务...\n", "📡 本地服务端口: 8081\n", "🌍 公网访问地址: https://e77e931d6128.ngrok-free.app\n", "📚 API 文档: https://e77e931d6128.ngrok-free.app/docs\n", "\n", "🎯 服务功能:\n", "   • 健康检查 API\n", "   • 模型问答 API（同步）\n", "   • 模型问答 API（流式）\n", "   • 自动生成的 API 文档\n", "\n", "💡 提示：\n", "   • 服务启动后会阻塞当前单元格（这是正常现象）\n", "   • 可以在新标签页中访问 API 文档进行测试\n", "   • 按 Ctrl+C 或中断内核可以停止服务\n", "   • 停止服务后 Ngrok 隧道也会关闭\n", "\n", "🔄 正在启动服务...\n", "INFO:     23.142.200.79:0 - \"GET / HTTP/1.1\" 200 OK\n", "INFO:     23.142.200.79:0 - \"GET /favicon.ico HTTP/1.1\" 404 Not Found\n", "INFO:     23.142.200.79:0 - \"GET /redoc HTTP/1.1\" 200 OK\n", "INFO:     23.142.200.79:0 - \"GET /openapi.json HTTP/1.1\" 200 OK\n", "INFO:     23.142.200.79:0 - \"GET /api/v1/generate-response HTTP/1.1\" 405 Method Not Allowed\n", "INFO:     23.142.200.79:0 - \"GET /docs HTTP/1.1\" 200 OK\n", "INFO:     23.142.200.79:0 - \"GET /openapi.json HTTP/1.1\" 200 OK\n", "📝 收到问题: 巴黎在哪里？\n", "✅ 推理完成\n", "INFO:     127.0.0.1:55166 - \"POST /api/v1/generate-response HTTP/1.1\" 200 OK\n", "🌊 收到流式请求: 请详细介绍一下人工智能的发展历史\n", "INFO:     127.0.0.1:40494 - \"POST /api/v1/generate-response-stream HTTP/1.1\" 200 OK\n"]}], "source": ["# 🚀 启动 FastAPI Web 服务\n", "#\n", "# 这个单元格的作用：\n", "# 1. 应用 nest_asyncio 解决 Jupyter 环境的异步问题\n", "# 2. 使用 uvicorn 启动 FastAPI 应用\n", "# 3. 在指定端口上运行 Web 服务，通过 Ngrok 隧道对外提供服务\n", "\n", "print(\"🚀 启动 FastAPI Web 服务...\")\n", "print(f\"📡 本地服务端口: {port}\")\n", "if 'public_url' in globals():\n", "    print(f\"🌍 公网访问地址: {public_url}\")\n", "    print(f\"📚 API 文档: {public_url}/docs\")\n", "else:\n", "    print(\"⚠️ 未检测到 Ngrok 隧道，请先运行上一个单元格\")\n", "\n", "print()\n", "print(\"🎯 服务功能:\")\n", "print(\"   • 健康检查 API\")\n", "print(\"   • 模型问答 API（同步）\")\n", "print(\"   • 模型问答 API（流式）\")\n", "print(\"   • 自动生成的 API 文档\")\n", "print()\n", "print(\"💡 提示：\")\n", "print(\"   • 服务启动后会阻塞当前单元格（这是正常现象）\")\n", "print(\"   • 可以在新标签页中访问 API 文档进行测试\")\n", "print(\"   • 按 Ctrl+C 或中断内核可以停止服务\")\n", "print(\"   • 停止服务后 Ngrok 隧道也会关闭\")\n", "\n", "# 应用 nest_asyncio 以在 Jupyter 环境中运行异步代码\n", "nest_asyncio.apply()\n", "\n", "try:\n", "    # 启动 FastAPI 应用\n", "    # host=\"0.0.0.0\" 允许外部访问（通过 Ngrok 隧道）\n", "    # port=port 使用之前定义的端口\n", "    print(f\"\\n🔄 正在启动服务...\")\n", "    uvicorn.run(app, host=\"0.0.0.0\", port=port)\n", "except KeyboardInterrupt:\n", "    print(\"\\n🛑 服务已停止\")\n", "    print(\"💡 如需重新启动，请重新运行此单元格\")\n", "except Exception as e:\n", "    print(f\"\\n❌ 服务启动失败: {e}\")\n", "    print(\"🔧 可能的解决方案:\")\n", "    print(\"1. 检查端口是否被占用\")\n", "    print(\"2. 确认 VLLM 服务正在运行\")\n", "    print(\"3. 重新运行依赖安装单元格\")\n", "finally:\n", "    # 清理 Ngrok 隧道\n", "    try:\n", "        ngrok.kill()\n", "        print(\"🧹 Ngrok 隧道已清理\")\n", "    except:\n", "        pass"]}, {"cell_type": "markdown", "metadata": {"id": "-YRmAQ7Emovh"}, "source": ["## 🧪 第八步：API 使用示例\n", "\n", "服务启动成功后，您可以通过多种方式调用 API。\n", "\n", "### 📡 API 调用方式\n", "\n", "#### 1. 🌐 浏览器访问\n", "- **本地访问**:\n", "  - API 文档: `http://localhost:8081/docs` (Swagger UI)\n", "  - 健康检查: `http://localhost:8081/`\n", "- **公网访问**（通过 Ngrok）:\n", "  - API 文档: `https://您的ngrok地址/docs`\n", "  - 健康检查: `https://您的ngrok地址/`\n", "\n", "#### 2. 📱 命令行调用 (cURL)\n", "使用 cURL 命令行工具测试 API 接口\n", "\n", "#### 3. 🐍 Python 调用\n", "使用 requests 库或其他 HTTP 客户端\n", "\n", "#### 4. 🌍 移动端/远程访问\n", "通过 Ngrok 提供的公网 URL，可以在任何设备上访问\n", "\n", "### 🔧 请求格式说明\n", "- **Content-Type**: `application/json`\n", "- **请求体**: JSON 格式，包含 `question` 字段\n", "- **响应**: JSON 格式，包含模型回答\n", "\n", "### 🌐 Ngrok 公网访问优势\n", "\n", "#### ✨ 主要优势\n", "1. **跨设备访问**: 手机、平板、其他电脑都可以访问\n", "2. **团队协作**: 团队成员可以直接测试您的 API\n", "3. **真实环境**: 模拟真实的网络环境和延迟\n", "4. **HTTPS 支持**: 自动提供 SSL 加密，安全可靠\n", "\n", "#### 📱 使用场景\n", "- **移动端测试**: 在手机上直接测试 API\n", "- **远程演示**: 向客户或同事展示项目\n", "- **前端集成**: 前端开发者可以直接调用 API\n", "- **第三方集成**: 支持 Webhook 等第三方服务\n", "\n", "### 💡 使用提示\n", "- 优先使用 Ngrok 提供的 HTTPS 地址\n", "- 本地测试可以使用 localhost 地址\n", "- 注意请求和响应的 JSON 格式\n", "- 可以通过 Swagger UI 进行交互式测试\n", "- Ngrok 地址每次重启都会变化，注意更新"]}, {"cell_type": "markdown", "metadata": {"id": "SE5HRXATmrWa"}, "source": ["### 📱 cURL 命令示例\n", "\n", "#### 🌐 本地访问 - 生成完整回答\n", "```bash\n", "curl --location 'http://localhost:8081/api/v1/generate-response' \\\n", "--header 'Content-Type: application/json' \\\n", "--data '{\n", "    \"question\": \"巴黎在哪里？\"\n", "}'\n", "```\n", "\n", "#### 🌍 公网访问示例（需要替换为实际的 ngrok 地址）\n", "```bash\n", "curl --location 'https://你的ngrok地址/api/v1/generate-response' \\\n", "--header 'Content-Type: application/json' \\\n", "--data '{\n", "    \"question\": \"巴黎在哪里？\"\n", "}'\n", "```\n", "\n", "#### 🌊 流式响应示例\n", "```bash\n", "curl --location 'http://localhost:8081/api/v1/generate-response-stream' \\\n", "--header 'Content-Type: application/json' \\\n", "--data '{\n", "    \"question\": \"请详细介绍一下人工智能的发展历史\"\n", "}'\n", "```\n", "\n", "#### 🔧 参数说明\n", "- `--location`: 跟随 HTTP 重定向\n", "- `--header`: 设置请求头，指定内容类型\n", "- `--data`: 发送 JSON 格式的请求体"]}, {"cell_type": "markdown", "metadata": {"id": "ZYXWde1knE2T"}, "source": ["## 📋 API 响应示例\n", "\n", "### 🤖 完整响应格式\n", "\n", "当您调用 `/api/v1/generate-response` 端点时，会收到如下格式的 JSON 响应：\n", "\n", "#### 📊 响应结构说明\n", "- **id**: 请求的唯一标识符\n", "- **object**: 响应对象类型\n", "- **created**: 响应创建时间戳\n", "- **model**: 使用的模型名称\n", "- **choices**: 模型生成的选择列表\n", "  - **index**: 选择的索引\n", "  - **message**: 消息内容\n", "    - **role**: 角色（assistant）\n", "    - **content**: 模型生成的回答\n", "  - **finish_reason**: 完成原因（stop 表示正常结束）\n", "- **usage**: 令牌使用统计\n", "  - **prompt_tokens**: 输入令牌数\n", "  - **completion_tokens**: 生成令牌数\n", "  - **total_tokens**: 总令牌数"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "EXziLO52nH1e"}, "outputs": [], "source": ["{\n", "  \"response\": {\n", "    \"id\": \"chatcmpl-13e29c35212b486ead18d91aa0668886\",\n", "    \"object\": \"chat.completion\",\n", "    \"created\": 1752386782,\n", "    \"model\": \"deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B\",\n", "    \"choices\": [\n", "      {\n", "        \"index\": 0,\n", "        \"message\": {\n", "          \"role\": \"assistant\",\n", "          \"reasoning_content\": null,\n", "          \"content\": \"好，用户发来“呦呦鹿鸣”这个词，看起来像是在玩手机的语音合成功能。我应该回复用户一个友好又有趣的回应，比如“嗯，看到你这么说，我好像也听到了，鹿鸣的声音很温柔啊！”这样既回应了他们的提问，又让语气更生动。另外，可以用一些轻松的语气，让用户觉得有趣又不觉得压力。可能用户想了解这句话的含义，或者只是想在聊天。所以，我需要保持自然，不显得太生硬。另外，可能用户是想测试一下语音合成的功能，或者是想了解一些有趣的话题。不管怎样，回应要友好，同时带点趣味，让用户感到愉快。\\n</think>\\n\\n嗯，看到你这么说，我好像也听到了，鹿鸣的声音很温柔啊！\",\n", "          \"tool_calls\": []\n", "        },\n", "        \"logprobs\": null,\n", "        \"finish_reason\": \"stop\",\n", "        \"stop_reason\": null\n", "      }\n", "    ],\n", "    \"usage\": {\n", "      \"prompt_tokens\": 9,\n", "      \"total_tokens\": 176,\n", "      \"completion_tokens\": 167,\n", "      \"prompt_tokens_details\": null\n", "    },\n", "    \"prompt_logprobs\": null,\n", "    \"kv_transfer_params\": null\n", "  }\n", "}"]}, {"cell_type": "markdown", "metadata": {"id": "eRer8bosm041"}, "source": ["### 🌊 流式响应格式\n", "\n", "当您调用 `/api/v1/generate-response-stream` 端点时，会收到一系列 JSON 对象，每个对象代表生成过程中的一个步骤：\n", "\n", "#### 📡 流式响应特点\n", "- **实时性**: 逐步返回生成内容，无需等待完整回答\n", "- **低延迟**: 用户可以立即看到模型开始生成\n", "- **更好体验**: 适合长文本生成和实时对话\n", "\n", "#### 🔄 流式数据格式\n", "每行数据都是一个独立的 JSON 对象，包含：\n", "- **id**: 请求标识符（整个流中保持一致）\n", "- **object**: \"chat.completion.chunk\"\n", "- **created**: 时间戳\n", "- **model**: 模型名称\n", "- **choices**: 当前生成的内容块\n", "  - **index**: 选择索引\n", "  - **delta**: 增量内容\n", "    - **content**: 新生成的文本片段\n", "  - **finish_reason**: 结束原因（null 表示继续，\"stop\" 表示结束）\n", "\n", "#### 💡 使用建议\n", "- 适合需要实时反馈的应用场景\n", "- 可以实现打字机效果的用户界面\n", "- 对于长文本生成特别有用"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "l51K5q5Cmwu_"}, "outputs": [], "source": ["{\"id\":\"chatcmpl-72594106be2541269cc68e8b37123051\",\"object\":\"chat.completion.chunk\",\"created\":1752386825,\"model\":\"deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B\",\"choices\":[{\"index\":0,\"delta\":{\"role\":\"assistant\",\"content\":\"\"},\"logprobs\":null,\"finish_reason\":null}]}\n", "{\"id\":\"chatcmpl-72594106be2541269cc68e8b37123051\",\"object\":\"chat.completion.chunk\",\"created\":1752386825,\"model\":\"deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\"嗯\"},\"logprobs\":null,\"finish_reason\":null}]}\n", "{\"id\":\"chatcmpl-72594106be2541269cc68e8b37123051\",\"object\":\"chat.completion.chunk\",\"created\":1752386825,\"model\":\"deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\"，\"},\"logprobs\":null,\"finish_reason\":null}]}\n", "{\"id\":\"chatcmpl-72594106be2541269cc68e8b37123051\",\"object\":\"chat.completion.chunk\",\"created\":1752386825,\"model\":\"deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\"我现在\"},\"logprobs\":null,\"finish_reason\":null}]}\n", "{\"id\":\"chatcmpl-72594106be2541269cc68e8b37123051\",\"object\":\"chat.completion.chunk\",\"created\":1752386825,\"model\":\"deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\"在\"},\"logprobs\":null,\"finish_reason\":null}]}\n", "{\"id\":\"chatcmpl-72594106be2541269cc68e8b37123051\",\"object\":\"chat.completion.chunk\",\"created\":1752386825,\"model\":\"deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\"学习\"},\"logprobs\":null,\"finish_reason\":null}]}\n", "{\"id\":\"chatcmpl-72594106be2541269cc68e8b37123051\",\"object\":\"chat.completion.chunk\",\"created\":1752386825,\"model\":\"deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\"大\"},\"logprobs\":null,\"finish_reason\":null}]}\n", "{\"id\":\"chatcmpl-72594106be2541269cc68e8b37123051\",\"object\":\"chat.completion.chunk\",\"created\":1752386825,\"model\":\"deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\"模型\"},\"logprobs\":null,\"finish_reason\":null}]}\n", "{\"id\":\"chatcmpl-72594106be2541269cc68e8b37123051\",\"object\":\"chat.completion.chunk\",\"created\":1752386825,\"model\":\"deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\"，\"},\"logprobs\":null,\"finish_reason\":null}]}\n", "{\"id\":\"chatcmpl-72594106be2541269cc68e8b37123051\",\"object\":\"chat.completion.chunk\",\"created\":1752386825,\"model\":\"deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B\",\"choices\":[{\"index\":0,\"delta\":{\"content\":\"比如\"},\"logprobs\":null,\"finish_reason\":null}]}\n", "...\n", "[DONE]\n"]}, {"cell_type": "markdown", "metadata": {"id": "ZbY6mDWCmTsf"}, "source": ["### Kill the VLLM"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "MQbgsTX83j8i"}, "outputs": [], "source": ["vllm_process.terminate()\n", "vllm_process.wait()  # Wait for process to terminate"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": [], "toc_visible": true, "include_colab_link": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}
{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyMGOhibQ5D+nSCLg9IB5Zli", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "title-section"}, "source": ["# AI智能旅行规划系统详解\n", "\n", "## 项目概述\n", "\n", "本项目展示了一个基于大语言模型(LLM)的智能旅行规划系统，该系统结合了以下核心技术：\n", "\n", "### 🔧 核心技术栈\n", "- **OpenAI GPT-4**: 强大的大语言模型，负责生成个性化旅行计划\n", "- **OpenAI Embeddings**: 文本向量化技术，将文本转换为数值向量\n", "- **Faiss**: Facebook开发的高效向量相似性搜索库\n", "- **Cache-Augmented Generation (CAG)**: 缓存增强生成技术\n", "\n", "### 🎯 学习目标\n", "通过本教程，您将学会：\n", "1. 理解向量嵌入(Embeddings)的基本概念和应用\n", "2. 掌握Faiss向量数据库的使用方法\n", "3. 了解RAG(检索增强生成)和CAG技术的区别\n", "4. 学会构建一个完整的AI智能体系统\n", "5. 理解如何将知识库与大语言模型结合\n", "\n", "### 📚 适用人群\n", "- 大模型技术初学者\n", "- 对AI应用开发感兴趣的开发者\n", "- 希望了解RAG技术的学习者"]}, {"cell_type": "markdown", "metadata": {"id": "theory-section"}, "source": ["## 理论基础\n", "\n", "### 1. 向量嵌入(Embeddings)是什么？\n", "\n", "**向量嵌入**是将文本转换为数值向量的技术。想象一下：\n", "- 文本：\"我喜欢旅行\"\n", "- 向量：[0.1, -0.3, 0.8, 0.2, ...] (通常有1536个维度)\n", "\n", "**为什么需要向量嵌入？**\n", "- 计算机无法直接理解文本的语义\n", "- 向量可以进行数学运算，计算相似度\n", "- 语义相近的文本会有相似的向量表示\n", "\n", "### 2. <PERSON><PERSON><PERSON>向量数据库\n", "\n", "**Faiss**是Facebook开发的向量相似性搜索库：\n", "- 可以快速在百万级向量中找到最相似的向量\n", "- 支持GPU加速\n", "- 内存效率高\n", "\n", "### 3. RAG vs CAG\n", "\n", "**RAG (检索增强生成)**:\n", "- 实时从外部知识库检索信息\n", "- 检索 → 增强 → 生成\n", "\n", "**CAG (缓存增强生成)**:\n", "- 预先将相关信息加载到LLM的上下文中\n", "- 减少实时检索的延迟\n", "- 本项目采用的方法"]}, {"cell_type": "markdown", "metadata": {"id": "installation-section"}, "source": ["## 环境准备\n", "\n", "### 安装必要的Python包\n", "\n", "运行下面的代码安装所需的依赖包："]}, {"cell_type": "code", "source": ["# 安装必要的Python包\n", "# -q 参数表示静默安装，不显示详细输出\n", "\n", "!pip install openai -q          # OpenAI API客户端\n", "!pip install faiss-cpu -q       # Faiss向量搜索库(CPU版本)\n", "!pip install tiktoken -q        # OpenAI的分词器\n", "!pip install colab_env -q       # Google Colab环境变量管理\n", "!pip install --upgrade tiktoken -q  # 升级tiktoken到最新版本\n", "\n", "print(\"✅ 所有依赖包安装完成！\")"], "metadata": {"id": "installation-cell"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "imports-section"}, "source": ["## 导入必要的库\n", "\n", "让我们导入项目所需的所有Python库，并了解每个库的作用："]}, {"cell_type": "code", "source": ["# 导入必要的库\n", "from IPython import get_ipython\n", "from IPython.display import display\n", "import openai                    # OpenAI API\n", "import faiss                     # Facebook AI相似性搜索库\n", "import numpy as np               # 数值计算库\n", "import tiktoken                  # OpenAI分词器\n", "import os                        # 操作系统接口\n", "import json                      # JSON数据处理\n", "import re                        # 正则表达式\n", "import datetime                  # 日期时间处理\n", "import colab_env                 # Google Colab环境变量\n", "\n", "# 导入OpenAI客户端\n", "from openai import OpenAI\n", "\n", "# 初始化OpenAI客户端\n", "# 注意：您需要设置OPENAI_API_KEY环境变量\n", "client = OpenAI(api_key=os.getenv(\"OPENAI_API_KEY\"))\n", "\n", "print(\"📚 所有库导入成功！\")\n", "print(\"🔑 请确保您已设置OPENAI_API_KEY环境变量\")"], "metadata": {"id": "imports-cell"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "embedding-section"}, "source": ["## 向量嵌入函数\n", "\n", "这是整个系统的基础函数，负责将文本转换为向量："]}, {"cell_type": "code", "source": ["def get_embedding(text, model=\"text-embedding-ada-002\"):\n", "    \"\"\"\n", "    获取文本的向量嵌入表示\n", "    \n", "    参数说明:\n", "    - text: 需要转换为向量的文本\n", "    - model: 使用的嵌入模型，默认为OpenAI的text-embedding-ada-002\n", "    \n", "    返回值:\n", "    - 一个1536维的浮点数列表，表示文本的向量嵌入\n", "    \n", "    工作原理:\n", "    1. 清理文本：移除换行符，避免影响嵌入质量\n", "    2. 调用OpenAI API获取嵌入向量\n", "    3. 返回向量数据\n", "    \"\"\"\n", "    # 预处理：将换行符替换为空格，确保文本格式一致\n", "    text = text.replace(\"\\n\", \" \")\n", "    \n", "    # 调用OpenAI API获取嵌入向量\n", "    response = client.embeddings.create(\n", "        input=[text],           # 输入文本列表\n", "        model=model            # 指定嵌入模型\n", "    )\n", "    \n", "    # 返回第一个(也是唯一一个)文本的嵌入向量\n", "    return response.data[0].embedding\n", "\n", "# 测试嵌入函数\n", "print(\"🧪 测试向量嵌入函数...\")\n", "test_text = \"我喜欢在纽约旅行\"\n", "test_embedding = get_embedding(test_text)\n", "print(f\"✅ 文本: '{test_text}'\")\n", "print(f\"✅ 向量维度: {len(test_embedding)}\")\n", "print(f\"✅ 向量前5个值: {test_embedding[:5]}\")"], "metadata": {"id": "embedding-cell"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "agent-class-section"}, "source": ["## AI智能体类设计\n", "\n", "现在我们来构建核心的AI智能体类。这个类将整合所有功能：\n", "\n", "### Agent类的主要功能：\n", "1. **知识库管理**: 加载和存储旅行相关知识\n", "2. **向量索引**: 使用Faiss建立高效的向量搜索索引\n", "3. **知识检索**: 根据用户查询检索相关信息\n", "4. **行程规划**: 调用GPT-4生成个性化旅行计划\n", "5. **结果展示**: 格式化并展示最终的旅行计划"]}, {"cell_type": "code", "source": ["class Agent:\n", "    \"\"\"\n", "    AI旅行规划智能体\n", "    \n", "    这个类实现了一个完整的AI旅行助手，具备以下核心能力：\n", "    1. 知识库管理和向量化存储\n", "    2. 基于语义相似性的信息检索\n", "    3. 个性化旅行计划生成\n", "    4. 智能预算管理和成本估算\n", "    \"\"\"\n", "\n", "    def __init__(self, name, cache_size, embedding_dim=1536):\n", "        \"\"\"\n", "        初始化AI智能体\n", "        \n", "        参数说明:\n", "        - name: 智能体名称\n", "        - cache_size: 缓存大小限制\n", "        - embedding_dim: 嵌入向量维度(OpenAI默认1536维)\n", "        \"\"\"\n", "        self.name = name\n", "        self.cache_size = cache_size\n", "        self.embedding_dim = embedding_dim\n", "        \n", "        # 初始化Faiss向量索引\n", "        # IndexFlatL2: 使用L2距离(欧几里得距离)进行精确搜索\n", "        self.index = faiss.IndexFlatL2(embedding_dim)\n", "        \n", "        # 存储知识库的向量和原始数据\n", "        self.knowledge_embeddings = []  # 存储所有知识点的向量\n", "        self.knowledge_data = []        # 存储对应的原始知识数据\n", "        \n", "        # 嵌入向量缓存，避免重复计算相同文本的向量\n", "        self.embedding_cache = {}\n", "        \n", "        print(f\"🤖 AI智能体 '{name}' 初始化完成！\")\n", "        print(f\"📊 向量维度: {embedding_dim}\")\n", "        print(f\"💾 缓存大小: {cache_size}\")\n", "\n", "    def load_knowledge(self, knowledge_data):\n", "        \"\"\"\n", "        加载知识库数据到向量索引中\n", "        \n", "        这个方法的工作流程：\n", "        1. 遍历知识库中的每个条目\n", "        2. 为每个知识点生成向量嵌入\n", "        3. 将向量添加到Faiss索引中\n", "        4. 同时保存原始数据用于后续检索\n", "        \n", "        参数:\n", "        - knowledge_data: 包含旅行知识的字典数据\n", "        \"\"\"\n", "        print(\"📚 开始加载知识库...\")\n", "        \n", "        # 遍历知识库的每个类别(如attractions, restaurants等)\n", "        for category, subcategories in knowledge_data.items():\n", "            print(f\"  📂 处理类别: {category}\")\n", "            \n", "            # 遍历每个类别下的具体项目\n", "            for key, value in subcategories.items():\n", "                # 检查缓存中是否已有该文本的向量\n", "                if key in self.embedding_cache:\n", "                    embedding = self.embedding_cache[key]\n", "                    print(f\"    🔄 从缓存获取: {key}\")\n", "                else:\n", "                    # 生成新的向量嵌入\n", "                    embedding = get_embedding(key, model='text-embedding-ada-002')\n", "                    # 保存到缓存\n", "                    self.embedding_cache[key] = embedding\n", "                    print(f\"    ✨ 新生成向量: {key}\")\n", "                \n", "                # 存储向量和对应的数据\n", "                self.knowledge_embeddings.append(embedding)\n", "                self.knowledge_data.append(value)\n", "\n", "        # 将所有向量添加到Faiss索引中\n", "        # 注意：需要转换为numpy数组格式\n", "        embeddings_array = np.array(self.knowledge_embeddings)\n", "        self.index.add(embeddings_array)\n", "        \n", "        print(f\"✅ 知识库加载完成！\")\n", "        print(f\"📊 总共加载了 {len(self.knowledge_data)} 个知识点\")\n", "        print(f\"🔍 Faiss索引大小: {self.index.ntotal}\")\n", "\n", "print(\"🏗️ Agent类定义完成！\")"], "metadata": {"id": "agent-class-cell"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "retrieval-section"}, "source": ["## 知识检索功能\n", "\n", "这是RAG系统的核心部分 - 根据用户查询检索相关知识："]}, {"cell_type": "code", "source": ["    def retrieve_knowledge(self, query):\n", "        \"\"\"\n", "        基于语义相似性检索相关知识\n", "        \n", "        这是RAG(检索增强生成)的核心功能：\n", "        1. 将用户查询转换为向量\n", "        2. 在知识库中搜索最相似的向量\n", "        3. 返回对应的知识内容\n", "        \n", "        参数:\n", "        - query: 用户的查询文本\n", "        \n", "        返回:\n", "        - 最相关的5个知识点列表\n", "        \"\"\"\n", "        print(f\"🔍 检索查询: '{query}'\")\n", "        \n", "        # 获取查询的向量表示\n", "        if query in self.embedding_cache:\n", "            query_embedding = self.embedding_cache[query]\n", "            print(\"  📋 使用缓存的查询向量\")\n", "        else:\n", "            query_embedding = get_embedding(query, model='text-embedding-ada-002')\n", "            self.embedding_cache[query] = query_embedding\n", "            print(\"  ✨ 生成新的查询向量\")\n", "\n", "        # 在Faiss索引中搜索最相似的k个向量\n", "        # D: 距离数组(越小越相似)\n", "        # I: 索引数组(对应knowledge_data中的位置)\n", "        k = 5  # 检索前5个最相关的结果\n", "        D, I = self.index.search(np.array([query_embedding]), k=k)\n", "        \n", "        print(f\"  📊 找到 {len(I[0])} 个相关结果\")\n", "        \n", "        # 收集相关信息\n", "        relevant_info = []\n", "        for idx, distance in zip(I[0], D[0]):\n", "            if idx < len(self.knowledge_data):  # 确保索引有效\n", "                relevant_info.append(self.knowledge_data[idx])\n", "                print(f\"    📍 相似度距离: {distance:.4f}\")\n", "        \n", "        return relevant_info\n", "\n", "# 将这个方法添加到Agent类中\n", "Agent.retrieve_knowledge = retrieve_knowledge\n", "print(\"🔍 知识检索功能添加完成！\")"], "metadata": {"id": "retrieval-cell"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "planning-section"}, "source": ["## 核心功能：智能旅行规划\n", "\n", "这是整个系统的核心功能，展示了如何将检索到的知识与GPT-4结合生成个性化旅行计划："]}, {"cell_type": "code", "source": ["    def plan_nyc_trip(self, travel_dates, budget, preferences, interests):\n", "        \"\"\"\n", "        生成个性化的纽约旅行计划\n", "        \n", "        这个方法展示了完整的CAG(缓存增强生成)流程：\n", "        1. 收集用户偏好信息\n", "        2. 检索相关知识\n", "        3. 构建详细的提示词\n", "        4. 调用GPT-4生成计划\n", "        5. 处理和展示结果\n", "        \n", "        参数:\n", "        - travel_dates: 旅行日期列表 [开始日期, 结束日期]\n", "        - budget: 预算金额\n", "        - preferences: 用户偏好描述\n", "        - interests: 兴趣爱好列表\n", "        \"\"\"\n", "        print(\"🎯 开始规划纽约旅行...\")\n", "        print(f\"📅 旅行日期: {travel_dates[0]} 到 {travel_dates[1]}\")\n", "        print(f\"💰 预算: {budget}\")\n", "        \n", "        # 收集用户的交通和住宿偏好\n", "        print(\"\\n📋 请提供您的偏好信息:\")\n", "        transport_mode = input(\"首选交通方式 (airplane/train/bus/car): \")\n", "        accommodation_type = input(\"首选住宿类型 (hotel/Airbnb): \")\n", "        \n", "        print(f\"✈️ 交通方式: {transport_mode}\")\n", "        print(f\"🏨 住宿类型: {accommodation_type}\")\n", "\n", "        # 基于用户偏好和兴趣检索相关知识\n", "        print(\"\\n🔍 检索相关旅行知识...\")\n", "        relevant_knowledge = self.retrieve_knowledge(preferences)\n", "        \n", "        # 为每个兴趣点检索额外信息\n", "        for interest in interests:\n", "            print(f\"  🎯 检索兴趣: {interest}\")\n", "            relevant_knowledge += self.retrieve_knowledge(interest)\n", "\n", "        # 构建给GPT-4的详细提示词\n", "        # 这是CAG技术的核心：将检索到的知识直接嵌入到提示中\n", "        system_prompt = f\"\"\"\n", "        您是一位专业的纽约旅行规划专家。请根据以下要求创建详细的旅行计划：\n", "        \n", "        🎯 核心要求:\n", "        - 包含从蒙特利尔到纽约的交通安排\n", "        - 首选交通方式: {transport_mode}\n", "        - 首选住宿类型: {accommodation_type}\n", "        - 用户希望参观洋基体育场，偏好高档酒店和餐饮体验\n", "        \n", "        🏟️ 洋基体育场特殊要求:\n", "        - 建议以下高级座位选项之一：传奇套房俱乐部(Legends Suite Club)、\n", "          福特球场MVP俱乐部(Ford Field MVP Club)或冠军套房(Champion Suite)\n", "        - 不要只建议基础参观\n", "        - 参观当天的晚餐安排在体育场内的高级餐厅\n", "        - 安排在下午或晚上(棒球比赛通常不在上午举行)\n", "        \n", "        📋 输出格式要求:\n", "        **交通安排:**\n", "        * [航班/火车详情] (预估费用: $xxx)\n", "        \n", "        **住宿安排:**\n", "        * [酒店详情] (每晚预估费用: $xxx)\n", "        \n", "        **第1天:**\n", "        * **上午:** [活动1] (预估费用: $xx) - [简要描述]\n", "        * **下午:** [活动2] (预估费用: $xx) - [简要描述]\n", "        * **晚上:** [活动3] (预估费用: $xx) - [简要描述]\n", "        * **晚餐:** [餐厅推荐] (人均预估费用: $xx)\n", "        \n", "        💰 预算管理:\n", "        - 考虑用户预算: {budget}\n", "        - 为每个项目提供具体的费用估算\n", "        - 避免使用\"昂贵\"或\"$$$\"等模糊描述\n", "        - 使用具体数字如\"$25\"、\"$150\"或\"$40-$60\"\n", "        \n", "        📊 最终总结:\n", "        * **总预估费用:** $[总费用]\n", "        * **剩余预算:** $[剩余预算]\n", "        * **预算利用率:** [总费用]/[预算]\n", "        \"\"\"\n", "        \n", "        # 构建用户消息，包含检索到的知识(CAG技术的体现)\n", "        user_message = f\"\"\"\n", "        请为我规划一次从{travel_dates[0]}到{travel_dates[1]}的纽约之旅。\n", "        \n", "        🎯 我的偏好: {preferences}\n", "        🎨 我的兴趣: {', '.join(interests)}\n", "        \n", "        📚 相关纽约旅行信息:\n", "        {self.summarize_knowledge(relevant_knowledge)}\n", "        \"\"\"\n", "        \n", "        print(\"\\n🤖 正在调用GPT-4生成旅行计划...\")\n", "        \n", "        # 调用GPT-4 API\n", "        response = client.chat.completions.create(\n", "            model=\"gpt-4\",  # 使用GPT-4模型\n", "            messages=[\n", "                {\"role\": \"system\", \"content\": system_prompt},\n", "                {\"role\": \"user\", \"content\": user_message}\n", "            ],\n", "            max_tokens=4096,  # 最大输出长度\n", "            temperature=0.7   # 创造性参数(0-1，越高越有创意)\n", "        )\n", "\n", "        print('\\n📋 GPT-4生成的旅行计划:')\n", "        print('=' * 50)\n", "        print(response.choices[0].message.content)\n", "        print('=' * 50)\n", "\n", "        # 处理和展示最终结果\n", "        self.present_itinerary(\n", "            response.choices[0].message.content, \n", "            budget, \n", "            travel_dates\n", "        )\n", "\n", "# 将方法添加到Agent类\n", "Agent.plan_nyc_trip = plan_nyc_trip\n", "print(\"🎯 旅行规划功能添加完成！\")"], "metadata": {"id": "planning-cell"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "utility-section"}, "source": ["## 辅助功能：结果处理和知识总结\n", "\n", "这些辅助函数帮助我们处理和展示AI生成的结果："]}, {"cell_type": "code", "source": ["    def present_itinerary(self, itinerary_text, budget, travel_dates):\n", "        \"\"\"\n", "        处理和展示旅行计划\n", "        \n", "        这个方法负责：\n", "        1. 解析GPT-4生成的文本\n", "        2. 按照结构化格式展示\n", "        3. 计算旅行天数\n", "        4. 提取费用信息(可扩展)\n", "        \n", "        参数:\n", "        - itinerary_text: GPT-4生成的原始文本\n", "        - budget: 用户预算\n", "        - travel_dates: 旅行日期\n", "        \"\"\"\n", "        print(\"\\n📊 处理旅行计划结果...\")\n", "        \n", "        # 使用正则表达式分割不同部分\n", "        # 匹配\"交通安排:\"、\"住宿安排:\"、\"第X天:\"等标题\n", "        sections = re.split(\n", "            r\"(交通安排:|住宿安排:|第\\s*\\d+\\s*天:|Transportation:|Accommodation:|Day\\s+\\d+:)\", \n", "            itinerary_text\n", "        )\n", "        \n", "        itinerary = {}\n", "        current_section = None\n", "        \n", "        # 解析每个部分\n", "        for section in sections:\n", "            section = section.strip()\n", "            if not section:\n", "                continue\n", "                \n", "            # 识别标题行\n", "            if any(keyword in section for keyword in \n", "                   [\"交通安排:\", \"住宿安排:\", \"Transportation:\", \"Accommodation:\"]):\n", "                current_section = section\n", "                itinerary[current_section] = []\n", "            elif re.match(r\"第\\s*\\d+\\s*天:|Day\\s+\\d+:\", section):\n", "                current_section = section\n", "                itinerary[current_section] = []\n", "            elif current_section:\n", "                itinerary[current_section].append(section)\n", "\n", "        # 计算旅行天数\n", "        try:\n", "            start_date = datetime.datetime.strptime(travel_dates[0], \"%Y-%m-%d\")\n", "            end_date = datetime.datetime.strptime(travel_dates[1], \"%Y-%m-%d\")\n", "            num_days = (end_date - start_date).days\n", "            print(f\"📅 旅行总天数: {num_days}天\")\n", "        except:\n", "            print(\"⚠️ 无法解析旅行日期\")\n", "\n", "        # 结构化展示旅行计划\n", "        print(\"\\n📋 结构化旅行计划:\")\n", "        print(\"=\" * 60)\n", "        \n", "        for section, items in itinerary.items():\n", "            print(f\"\\n🔸 {section}\")\n", "            print(\"-\" * 40)\n", "            for item in items:\n", "                # 简单格式化每个项目\n", "                formatted_item = item.replace(\"**\", \"\")\n", "                print(f\"  {formatted_item}\")\n", "            print(\"-\" * 40)\n", "\n", "        print(\"\\n✅ 旅行计划处理完成！\")\n", "\n", "    def summarize_knowledge(self, knowledge_list):\n", "        \"\"\"\n", "        将检索到的知识转换为适合LLM的格式\n", "        \n", "        这个方法是CAG技术的关键部分：\n", "        将结构化的知识数据转换为自然语言描述，\n", "        以便GPT-4能够理解和使用。\n", "        \n", "        参数:\n", "        - knowledge_list: 检索到的知识点列表\n", "        \n", "        返回:\n", "        - 格式化的知识总结文本\n", "        \"\"\"\n", "        if not knowledge_list:\n", "            return \"暂无相关知识信息。\"\n", "        \n", "        summary = \"\\n📚 相关知识库信息:\\n\"\n", "        \n", "        for i, item in enumerate(knowledge_list, 1):\n", "            if isinstance(item, dict):\n", "                # 获取第一个键作为名称\n", "                if item:\n", "                    name = list(item.keys())[0] if item.keys() else f\"项目{i}\"\n", "                    summary += f\"\\n🏛️ **{name}**\\n\"\n", "                    \n", "                    # 添加描述信息\n", "                    if \"description\" in item:\n", "                        summary += f\"   📝 描述: {item['description']}\\n\"\n", "                    \n", "                    # 添加地址信息\n", "                    if \"address\" in item:\n", "                        summary += f\"   📍 地址: {item['address']}\\n\"\n", "                    \n", "                    # 添加网站信息\n", "                    if \"website\" in item:\n", "                        summary += f\"   🌐 网站: {item['website']}\\n\"\n", "                    \n", "                    # 添加小贴士\n", "                    if \"tips\" in item and isinstance(item['tips'], list):\n", "                        summary += \"   💡 小贴士:\\n\"\n", "                        for tip in item['tips']:\n", "                            summary += f\"      • {tip}\\n\"\n", "                    \n", "                    # 添加活动信息\n", "                    if \"activities\" in item and isinstance(item['activities'], list):\n", "                        summary += \"   🎯 推荐活动:\\n\"\n", "                        for activity in item['activities']:\n", "                            summary += f\"      • {activity}\\n\"\n", "            else:\n", "                summary += f\"\\n📌 {item}\\n\"\n", "        \n", "        return summary\n", "\n", "# 将方法添加到Agent类\n", "Agent.present_itinerary = present_itinerary\n", "Agent.summarize_knowledge = summarize_knowledge\n", "print(\"🛠️ 辅助功能添加完成！\")"], "metadata": {"id": "utility-cell"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "knowledge-section"}, "source": ["## 知识库构建\n", "\n", "这里我们构建一个包含纽约旅行信息的知识库。在实际应用中，这个知识库可以：\n", "- 从外部API获取实时数据\n", "- 从数据库中加载\n", "- 定期更新以保持信息的准确性\n", "\n", "### 知识库结构说明：\n", "- **attractions**: 景点信息\n", "- **restaurants**: 餐厅信息  \n", "- **hotels**: 酒店信息\n", "- **transportation**: 交通信息"]}, {"cell_type": "code", "source": ["# 构建纽约旅行知识库\n", "# 这是一个结构化的JSON对象，包含了纽约的主要旅行信息\n", "\n", "nyc_knowledge = {\n", "    \"attractions\": {\n", "        \"Empire State Building\": {\n", "            \"description\": \"标志性摩天大楼，拥有观景台，可俯瞰整个城市的壮丽景色。这座装饰艺术风格的建筑是纽约天际线的象征。\",\n", "            \"address\": \"350 Fifth Avenue, Manhattan\",\n", "            \"website\": \"www.esbnyc.com\",\n", "            \"tips\": [\n", "                \"建议提前在线购票以避免排长队\",\n", "                \"白天和夜晚都值得参观，可以看到不同的城市景观\",\n", "                \"86楼和102楼都有观景台，102楼视野更佳但票价更高\"\n", "            ]\n", "        },\n", "        \"Central Park\": {\n", "            \"description\": \"位于曼哈顿心脏地带的巨大绿色绿洲，是野餐、散步、骑自行车和划船的完美场所。占地843英亩，是纽约人和游客的休闲天堂。\",\n", "            \"activities\": [\n", "                \"参观中央公园动物园\",\n", "                \"在湖上租船划行\",\n", "                \"在德拉科特剧院观看演出\",\n", "                \"在大草坪野餐\",\n", "                \"参观贝塞斯达喷泉\",\n", "                \"在草莓园纪念约翰·列侬\"\n", "            ],\n", "            \"tips\": [\n", "                \"下载公园地图应用以便导航\",\n", "                \"春夏季节最适合参观\",\n", "                \"注意安全，避免夜晚独自前往偏僻区域\"\n", "            ]\n", "        },\n", "        \"The Metropolitan Museum of Art\": {\n", "            \"description\": \"世界上最大、最优秀的艺术博物馆之一，收藏了来自世界各地数千年的艺术珍品。从古埃及文物到现代艺术应有尽有。\",\n", "            \"address\": \"1000 Fifth Avenue, Manhattan\",\n", "            \"website\": \"www.metmuseum.org\",\n", "            \"tips\": [\n", "                \"预留充足时间探索庞大的收藏\",\n", "                \"考虑购买导览服务以获得更深入的体验\",\n", "                \"周五和周六晚上开放至晚上9点\",\n", "                \"屋顶花园在夏季开放，景色绝佳\"\n", "            ]\n", "        },\n", "        \"Statue of Liberty\": {\n", "            \"description\": \"美国自由的象征，位于自由岛上。这座法国赠送的雕像是纽约港的标志性景观，代表着自由和民主的理想。\",\n", "            \"address\": \"Liberty Island, New York Harbor\",\n", "            \"tips\": [\n", "                \"需要乘坐渡轮前往，建议提前预订\",\n", "                \"登顶需要额外预订，名额有限\",\n", "                \"可以与埃利斯岛移民博物馆一起参观\",\n", "                \"携带身份证件进行安检\"\n", "            ]\n", "        },\n", "        \"Yankee Stadium\": {\n", "            \"description\": \"纽约洋基队的主场，被称为'棒球大教堂'。这里不仅可以观看精彩的棒球比赛，还能体验美国体育文化。\",\n", "            \"address\": \"1 E 161st St, Bronx, NY\",\n", "            \"premium_options\": [\n", "                \"Legends Suite Club - 包含高级餐饮和专属服务\",\n", "                \"Ford Field MVP Club - 室内外座位选择，含餐饮\",\n", "                \"Champion Suite - 私人包厢体验\"\n", "            ],\n", "            \"tips\": [\n", "                \"比赛通常在下午或晚上举行\",\n", "                \"高级座位包含餐饮服务\",\n", "                \"可以参观洋基博物馆\",\n", "                \"地铁4、6、B、D线可直达\"\n", "            ]\n", "        }\n", "    },\n", "    \"restaurants\": {\n", "        \"Eleven Madison Park\": {\n", "            \"description\": \"米其林三星餐厅，提供以纽约农产品为主的多道式品尝菜单。被誉为世界最佳餐厅之一。\",\n", "            \"cuisine\": \"现代美式\",\n", "            \"price_range\": \"$300-400人均\",\n", "            \"tips\": [\n", "                \"需要提前数月预订\",\n", "                \"提供素食菜单\",\n", "                \"用餐时间约3-4小时\"\n", "            ]\n", "        },\n", "        \"Daniel\": {\n", "            \"description\": \"<PERSON> Boulud主厨的同名餐厅，提供精致的法式料理。优雅的环境和卓越的服务使其成为特殊场合的完美选择。\",\n", "            \"cuisine\": \"法式\",\n", "            \"price_range\": \"$250-350人均\",\n", "            \"address\": \"60 E 65th St, Manhattan\"\n", "        }\n", "    },\n", "    \"hotels\": {\n", "        \"The Plaza Hotel\": {\n", "            \"description\": \"位于第五大道的标志性豪华酒店，毗邻中央公园。这座历史悠久的酒店提供世界级的服务和优越的地理位置。\",\n", "            \"address\": \"768 5th Ave, Manhattan\",\n", "            \"price_range\": \"$600-1000每晚\",\n", "            \"amenities\": [\n", "                \"24小时礼宾服务\",\n", "                \"多个餐厅和酒吧\",\n", "                \"健身中心和水疗\",\n", "                \"商务中心\"\n", "            ]\n", "        }\n", "    }\n", "}\n", "\n", "print(\"📚 纽约旅行知识库构建完成！\")\n", "print(f\"📊 知识库统计:\")\n", "for category, items in nyc_knowledge.items():\n", "    print(f\"  🔸 {category}: {len(items)} 个项目\")\n", "print(f\"📈 总计: {sum(len(items) for items in nyc_knowledge.values())} 个知识点\")"], "metadata": {"id": "knowledge-cell"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "demo-section"}, "source": ["## 完整示例演示\n", "\n", "现在让我们运行完整的AI旅行规划系统！\n", "\n", "### 运行步骤：\n", "1. 创建AI智能体实例\n", "2. 加载知识库\n", "3. 设置旅行参数\n", "4. 生成个性化旅行计划\n", "\n", "**注意**: 运行此代码前，请确保您已设置OpenAI API密钥！"]}, {"cell_type": "code", "source": ["# 🚀 完整系统演示\n", "\n", "def run_travel_planning_demo():\n", "    \"\"\"\n", "    运行完整的AI旅行规划演示\n", "    \n", "    这个函数展示了整个系统的工作流程：\n", "    1. 初始化AI智能体\n", "    2. 加载知识库\n", "    3. 设置用户参数\n", "    4. 生成旅行计划\n", "    \"\"\"\n", "    print(\"🎬 开始AI旅行规划系统演示\")\n", "    print(\"=\" * 50)\n", "    \n", "    # 步骤1: 创建AI智能体\n", "    print(\"\\n🤖 步骤1: 创建AI智能体\")\n", "    agent = Agent(\n", "        name=\"纽约旅行AI助手\",     # 智能体名称\n", "        cache_size=1000,          # 缓存大小\n", "        embedding_dim=1536        # OpenAI嵌入维度\n", "    )\n", "    \n", "    # 步骤2: 加载知识库\n", "    print(\"\\n📚 步骤2: 加载旅行知识库\")\n", "    agent.load_knowledge(nyc_knowledge)\n", "    \n", "    # 步骤3: 设置旅行参数\n", "    print(\"\\n⚙️ 步骤3: 设置旅行参数\")\n", "    travel_dates = [\"2024-04-10\", \"2024-04-15\"]  # 5天4夜的旅行\n", "    budget = 10000  # 预算$10,000\n", "    preferences = \"\"\"\n", "    我喜欢文化体验和美食探索的结合。希望能够深度体验纽约的艺术氛围，\n", "    同时品尝当地的特色美食。我偏好步行探索城市，但也愿意使用公共交通。\n", "    希望住宿和餐饮都是高品质的体验。\n", "    \"\"\"\n", "    interests = [\"博物馆\", \"百老汇演出\", \"中央公园\", \"美食\", \"建筑\"]\n", "    \n", "    print(f\"📅 旅行日期: {travel_dates[0]} 至 {travel_dates[1]}\")\n", "    print(f\"💰 预算: ${budget:,}\")\n", "    print(f\"🎯 兴趣爱好: {', '.join(interests)}\")\n", "    \n", "    # 步骤4: 生成旅行计划\n", "    print(\"\\n🎯 步骤4: 开始生成个性化旅行计划\")\n", "    print(\"=\" * 50)\n", "    \n", "    try:\n", "        # 调用核心规划功能\n", "        agent.plan_nyc_trip(\n", "            travel_dates=travel_dates,\n", "            budget=budget,\n", "            preferences=preferences,\n", "            interests=interests\n", "        )\n", "        \n", "        print(\"\\n🎉 旅行规划完成！\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ 生成旅行计划时出错: {str(e)}\")\n", "        print(\"💡 请检查您的OpenAI API密钥是否正确设置\")\n", "        return False\n", "    \n", "    return True\n", "\n", "# 运行演示\n", "print(\"🎬 准备运行AI旅行规划系统演示...\")\n", "print(\"\\n⚠️ 重要提醒:\")\n", "print(\"1. 请确保已设置OPENAI_API_KEY环境变量\")\n", "print(\"2. 运行过程中需要输入交通和住宿偏好\")\n", "print(\"3. 生成计划可能需要几分钟时间\")\n", "print(\"\\n按回车键开始演示...\")\n", "input()\n", "\n", "# 执行演示\n", "success = run_travel_planning_demo()\n", "\n", "if success:\n", "    print(\"\\n✅ 演示成功完成！\")\n", "else:\n", "    print(\"\\n❌ 演示未能完成，请检查配置\")"], "metadata": {"id": "demo-cell"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "summary-section"}, "source": ["## 技术总结与学习要点\n", "\n", "### 🔧 核心技术栈回顾\n", "\n", "1. **向量嵌入(Embeddings)**\n", "   - 将文本转换为1536维的数值向量\n", "   - 使用OpenAI的`text-embedding-ada-002`模型\n", "   - 语义相似的文本具有相似的向量表示\n", "\n", "2. **Faiss向量数据库**\n", "   - 高效的向量相似性搜索\n", "   - 支持大规模向量检索\n", "   - 使用L2距离计算相似度\n", "\n", "3. **Cache-Augmented Generation (CAG)**\n", "   - 预先将相关知识加载到LLM上下文中\n", "   - 减少实时检索延迟\n", "   - 提高响应效率\n", "\n", "4. **GPT-4集成**\n", "   - 强大的自然语言生成能力\n", "   - 结构化提示词工程\n", "   - 个性化内容生成\n", "\n", "### 📚 关键学习点\n", "\n", "#### 1. RAG vs CAG的区别\n", "- **RAG**: 实时检索 → 增强 → 生成\n", "- **CAG**: 预加载知识 → 缓存 → 生成\n", "- **优势**: CAG减少延迟，提高效率\n", "\n", "#### 2. 向量检索的工作原理\n", "```python\n", "# 查询向量化\n", "query_vector = get_embedding(\"用户查询\")\n", "\n", "# 相似性搜索\n", "distances, indices = faiss_index.search(query_vector, k=5)\n", "\n", "# 获取相关知识\n", "relevant_knowledge = [knowledge_base[i] for i in indices]\n", "```\n", "\n", "#### 3. 提示词工程的重要性\n", "- 清晰的角色定义\n", "- 具体的输出格式要求\n", "- 相关知识的有效整合\n", "- 约束条件的明确表达\n", "\n", "### 🚀 扩展方向\n", "\n", "1. **实时数据集成**\n", "   - 连接实时API获取最新信息\n", "   - 动态更新知识库\n", "   - 价格和可用性实时查询\n", "\n", "2. **多模态支持**\n", "   - 图像识别和描述\n", "   - 语音输入输出\n", "   - 视频内容分析\n", "\n", "3. **个性化学习**\n", "   - 用户偏好学习\n", "   - 历史行为分析\n", "   - 推荐算法优化\n", "\n", "4. **多语言支持**\n", "   - 多语言知识库\n", "   - 跨语言检索\n", "   - 本地化内容生成\n", "\n", "### 💡 最佳实践\n", "\n", "1. **向量缓存策略**\n", "   - 避免重复计算相同文本的向量\n", "   - 合理设置缓存大小限制\n", "   - 定期清理过期缓存\n", "\n", "2. **错误处理**\n", "   - API调用失败的优雅降级\n", "   - 网络异常的重试机制\n", "   - 用户输入验证\n", "\n", "3. **性能优化**\n", "   - 批量处理向量计算\n", "   - 异步API调用\n", "   - 结果缓存策略\n", "\n", "### 🎯 学习建议\n", "\n", "1. **深入理解向量嵌入**\n", "   - 尝试不同的嵌入模型\n", "   - 理解向量维度的影响\n", "   - 实验相似度计算方法\n", "\n", "2. **掌握提示词工程**\n", "   - 练习编写清晰的系统提示\n", "   - 学习Few-shot学习技巧\n", "   - 理解上下文长度限制\n", "\n", "3. **实践项目扩展**\n", "   - 尝试其他领域的应用\n", "   - 集成更多外部数据源\n", "   - 优化用户交互体验\n", "\n", "---\n", "\n", "**🎉 恭喜您完成了AI智能旅行规划系统的学习！**\n", "\n", "这个项目展示了现代AI技术的强大能力，通过结合向量检索、知识管理和大语言模型，我们创建了一个能够理解用户需求并生成个性化解决方案的智能系统。\n", "\n", "继续探索和实验，您将能够构建更加复杂和实用的AI应用！"]}]}
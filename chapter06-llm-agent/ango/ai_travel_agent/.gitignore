# AI旅行助手项目 - Git忽略文件配置

# API密钥和敏感配置文件
.streamlit/secrets.toml
.env
*.env
.env.local
.env.production
secrets.json
config.json

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/
conda-env/
.conda/

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Jupyter Notebook
.ipynb_checkpoints

# 日志文件
*.log
logs/
log/

# 缓存文件
.cache/
.pytest_cache/
.coverage
htmlcov/

# 临时文件
tmp/
temp/
*.tmp
*.temp

# 数据文件（如果包含敏感信息）
data/
*.csv
*.json
*.xlsx
*.db
*.sqlite

# 输出文件
output/
results/
generated/

# Docker相关
.dockerignore
docker-compose.override.yml

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 备份文件
*.bak
*.backup
*.orig

# 压缩文件
*.zip
*.tar.gz
*.rar

# 模型文件（如果很大）
*.pkl
*.model
*.bin
models/

# Streamlit特定
# .streamlit/config.toml 
# 脚本
start_app.sh
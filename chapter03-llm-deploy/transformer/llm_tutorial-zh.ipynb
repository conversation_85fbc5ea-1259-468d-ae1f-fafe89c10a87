{"cells": [{"cell_type": "markdown", "source": ["## 🔧 第一步：环境信息检查\n", "\n", "在开始部署模型之前，我们需要了解当前的运行环境。这个步骤非常重要，因为：\n", "\n", "### 🎯 检查目的\n", "1. **硬件确认**: 确保有足够的 GPU 显存运行模型\n", "2. **系统兼容**: 验证操作系统和 Python 版本\n", "3. **资源评估**: 了解可用的 CPU、内存和存储空间\n", "4. **环境配置**: 检查 CUDA 版本和相关依赖\n", "\n", "### 📊 检查内容\n", "- **操作系统**: Linux 发行版和版本\n", "- **CPU 信息**: 处理器型号和核心数\n", "- **内存状态**: 总内存和可用内存\n", "- **GPU 配置**: 显卡型号和显存大小\n", "- **CUDA 版本**: 深度学习框架支持\n", "- **Python 环境**: 解释器版本\n", "- **磁盘空间**: 可用存储空间"], "metadata": {"id": "SnR5kLU5jPot"}}, {"cell_type": "code", "source": ["# 🔍 环境信息检查脚本\n", "#\n", "# 本脚本的作用：\n", "# 1. 安装 pandas 库用于数据表格展示\n", "# 2. 检查系统的各项配置信息\n", "# 3. 生成详细的环境报告表格\n", "#\n", "# 对于初学者来说，这个步骤帮助您：\n", "# - 了解当前运行环境的硬件配置\n", "# - 确认是否满足模型运行的最低要求\n", "# - 学习如何通过代码获取系统信息\n", "\n", "# 安装 pandas 库 - 用于创建和展示数据表格\n", "# pandas 是 Python 中最流行的数据处理和分析库\n", "!pip install pandas==2.2.2\n", "\n", "import platform # 导入 platform 模块以获取系统信息\n", "import os # 导入 os 模块以与操作系统交互\n", "import subprocess # 导入 subprocess 模块以运行外部命令\n", "import pandas as pd # 导入 pandas 模块，通常用于数据处理，这里用于创建表格\n", "import shutil # 导入 shutil 模块以获取磁盘空间信息\n", "\n", "# 获取 CPU 信息的函数，包括核心数量\n", "def get_cpu_info():\n", "    cpu_info = \"\" # 初始化 CPU 信息字符串\n", "    physical_cores = \"N/A\"\n", "    logical_cores = \"N/A\"\n", "\n", "    if platform.system() == \"Windows\": # 如果是 Windows 系统\n", "        cpu_info = platform.processor() # 使用 platform.processor() 获取 CPU 信息\n", "        try:\n", "            # 获取 Windows 上的核心数量 (需要 WMI)\n", "            import wmi\n", "            c = wmi.WMI()\n", "            for proc in c.Win32_Processor():\n", "                physical_cores = proc.NumberOfCores\n", "                logical_cores = proc.NumberOfLogicalProcessors\n", "        except:\n", "            pass # 如果 WMI 不可用，忽略错误\n", "\n", "    elif platform.system() == \"Darwin\": # 如果是 macOS 系统\n", "        # 在 macOS 上使用 sysctl 命令获取 CPU 信息和核心数量\n", "        os.environ['PATH'] = os.environ['PATH'] + os.pathsep + '/usr/sbin' # 更新 PATH 环境变量\n", "        try:\n", "            process_brand = subprocess.Popen(['sysctl', \"machdep.cpu.brand_string\"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)\n", "            stdout_brand, stderr_brand = process_brand.communicate()\n", "            cpu_info = stdout_brand.decode().split(': ')[1].strip() if stdout_brand else \"Could not retrieve CPU info\"\n", "\n", "            process_physical = subprocess.Popen(['sysctl', \"hw.physicalcpu\"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)\n", "            stdout_physical, stderr_physical = process_physical.communicate()\n", "            physical_cores = stdout_physical.decode().split(': ')[1].strip() if stdout_physical else \"N/A\"\n", "\n", "            process_logical = subprocess.Popen(['sysctl', \"hw.logicalcpu\"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)\n", "            stdout_logical, stderr_logical = process_logical.communicate()\n", "            logical_cores = stdout_logical.decode().split(': ')[1].strip() if stdout_logical else \"N/A\"\n", "\n", "        except:\n", "            cpu_info = \"Could not retrieve CPU info\"\n", "            physical_cores = \"N/A\"\n", "            logical_cores = \"N/A\"\n", "\n", "    else:  # Linux 系统\n", "        try:\n", "            # 在 Linux 上读取 /proc/cpuinfo 文件获取 CPU 信息和核心数量\n", "            with open('/proc/cpuinfo') as f:\n", "                physical_cores_count = 0\n", "                logical_cores_count = 0\n", "                cpu_info_lines = []\n", "                for line in f:\n", "                    if line.startswith('model name'): # 查找以 'model name'开头的行\n", "                        if not cpu_info: # 只获取第一个 model name\n", "                            cpu_info = line.split(': ')[1].strip()\n", "                    elif line.startswith('cpu cores'): # 查找以 'cpu cores' 开头的行\n", "                        physical_cores_count = int(line.split(': ')[1].strip())\n", "                    elif line.startswith('processor'): # 查找以 'processor' 开头的行\n", "                        logical_cores_count += 1\n", "                physical_cores = str(physical_cores_count) if physical_cores_count > 0 else \"N/A\"\n", "                logical_cores = str(logical_cores_count) if logical_cores_count > 0 else \"N/A\"\n", "                if not cpu_info:\n", "                     cpu_info = \"Could not retrieve CPU info\"\n", "\n", "        except:\n", "            cpu_info = \"Could not retrieve CPU info\"\n", "            physical_cores = \"N/A\"\n", "            logical_cores = \"N/A\"\n", "\n", "    return f\"{cpu_info} ({physical_cores} physical cores, {logical_cores} logical cores)\" # 返回 CPU 信息和核心数量\n", "\n", "\n", "# 获取内存信息的函数\n", "def get_memory_info():\n", "    mem_info = \"\" # 初始化内存信息字符串\n", "    if platform.system() == \"Windows\":\n", "        # 在 Windows 上不容易通过标准库获取，需要外部库或 PowerShell\n", "        mem_info = \"Requires external tools on Windows\" # 设置提示信息\n", "    elif platform.system() == \"Darwin\": # 如果是 macOS 系统\n", "        # 在 macOS 上使用 sysctl 命令获取内存大小\n", "        process = subprocess.Popen(['sysctl', \"hw.memsize\"], stdout=subprocess.PIPE, stderr=subprocess.PIPE) # 运行 sysctl 命令\n", "        stdout, stderr = process.communicate() # 获取标准输出和标准错误\n", "        mem_bytes = int(stdout.decode().split(': ')[1].strip()) # 解析输出，获取内存大小（字节）\n", "        mem_gb = mem_bytes / (1024**3) # 转换为 GB\n", "        mem_info = f\"{mem_gb:.2f} GB\" # 格式化输出\n", "    else:  # Linux 系统\n", "        try:\n", "            # 在 Linux 上读取 /proc/meminfo 文件获取内存信息\n", "            with open('/proc/meminfo') as f:\n", "                total_mem_kb = 0\n", "                available_mem_kb = 0\n", "                for line in f:\n", "                    if line.startswith('MemTotal'): # 查找以 'MemTotal' 开头的行\n", "                        total_mem_kb = int(line.split(':')[1].strip().split()[0]) # 解析行，获取总内存（KB）\n", "                    elif line.startswith('MemAvailable'): # 查找以 'MemAvailable' 开头的行\n", "                         available_mem_kb = int(line.split(':')[1].strip().split()[0]) # 解析行，获取可用内存（KB）\n", "\n", "                if total_mem_kb > 0:\n", "                    total_mem_gb = total_mem_kb / (1024**2) # 转换为 GB\n", "                    mem_info = f\"{total_mem_gb:.2f} GB\" # 格式化输出总内存\n", "                    if available_mem_kb > 0:\n", "                        available_mem_gb = available_mem_kb / (1024**2)\n", "                        mem_info += f\" (Available: {available_mem_gb:.2f} GB)\" # 添加可用内存信息\n", "                else:\n", "                     mem_info = \"Could not retrieve memory info\" # 如果读取文件出错，设置错误信息\n", "\n", "        except:\n", "            mem_info = \"Could not retrieve memory info\" # 如果读取文件出错，设置错误信息\n", "    return mem_info # 返回内存信息\n", "\n", "# 获取 GPU 信息的函数，包括显存\n", "def get_gpu_info():\n", "    try:\n", "        # 尝试使用 nvidia-smi 获取 NVIDIA GPU 信息和显存\n", "        result = subprocess.run(['nvidia-smi', '--query-gpu=name,memory.total', '--format=csv,noheader'], capture_output=True, text=True)\n", "        if result.returncode == 0: # 如果命令成功执行\n", "            gpu_lines = result.stdout.strip().split('\\n') # 解析输出，获取 GPU 名称和显存\n", "            gpu_info_list = []\n", "            for line in gpu_lines:\n", "                name, memory = line.split(', ')\n", "                gpu_info_list.append(f\"{name} ({memory})\") # 格式化 GPU 信息\n", "            return \", \".join(gpu_info_list) if gpu_info_list else \"NVIDIA GPU found, but info not listed\" # 返回 GPU 信息或提示信息\n", "        else:\n", "             # 尝试使用 lshw 获取其他 GPU 信息 (需要安装 lshw)\n", "            try:\n", "                result_lshw = subprocess.run(['lshw', '-C', 'display'], capture_output=True, text=True)\n", "                if result_lshw.returncode == 0: # 如果命令成功执行\n", "                     # 简单解析输出中的 product 名称和显存\n", "                    gpu_info_lines = []\n", "                    current_gpu = {}\n", "                    for line in result_lshw.stdout.splitlines():\n", "                        if 'product:' in line:\n", "                             if current_gpu:\n", "                                 gpu_info_lines.append(f\"{current_gpu.get('product', 'GPU')} ({current_gpu.get('memory', 'N/A')})\")\n", "                             current_gpu = {'product': line.split('product:')[1].strip()}\n", "                        elif 'size:' in line and 'memory' in line:\n", "                             current_gpu['memory'] = line.split('size:')[1].strip()\n", "\n", "                    if current_gpu: # 添加最后一个 GPU 的信息\n", "                        gpu_info_lines.append(f\"{current_gpu.get('product', 'GPU')} ({current_gpu.get('memory', 'N/A')})\")\n", "\n", "                    return \", \".join(gpu_info_lines) if gpu_info_lines else \"GPU found (via lshw), but info not parsed\" # 如果找到 GPU 但信息无法解析，设置提示信息\n", "                else:\n", "                    return \"No GPU found (checked nvidia-smi and lshw)\" # 如果两个命令都找不到 GPU，设置提示信息\n", "            except FileNotFoundError:\n", "                 return \"No GPU found (checked nvidia-smi, lshw not found)\" # 如果找不到 lshw 命令，设置提示信息\n", "    except FileNotFoundError:\n", "        return \"No GPU found (nvidia-smi not found)\" # 如果找不到 nvidia-smi 命令，设置提示信息\n", "\n", "\n", "# 获取 CUDA 版本的函数\n", "def get_cuda_version():\n", "    try:\n", "        # 尝试使用 nvcc --version 获取 CUDA 版本\n", "        result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True)\n", "        if result.returncode == 0: # 如果命令成功执行\n", "            for line in result.stdout.splitlines():\n", "                if 'release' in line: # 查找包含 'release' 的行\n", "                    return line.split('release ')[1].split(',')[0] # 解析行，提取版本号\n", "        return \"CUDA not found or version not parsed\" # 如果找不到 CUDA 或版本无法解析，设置提示信息\n", "    except FileNotFoundError:\n", "        return \"CUDA not found\" # 如果找不到 nvcc 命令，设置提示信息\n", "\n", "# 获取 Python 版本的函数\n", "def get_python_version():\n", "    return platform.python_version() # 获取 Python 版本\n", "\n", "# 获取 Conda 版本的函数\n", "def get_conda_version():\n", "    try:\n", "        # 尝试使用 conda --version 获取 Conda 版本\n", "        result = subprocess.run(['conda', '--version'], capture_output=True, text=True)\n", "        if result.returncode == 0: # 如果命令成功执行\n", "            return result.stdout.strip() # 返回 Conda 版本\n", "        return \"Conda not found or version not parsed\" # 如果找不到 Conda 或版本无法解析，设置提示信息\n", "    except FileNotFoundError:\n", "        return \"Conda not found\" # 如果找不到 conda 命令，设置提示信息\n", "\n", "# 获取物理磁盘空间信息的函数\n", "def get_disk_space():\n", "    try:\n", "        total, used, free = shutil.disk_usage(\"/\") # 获取根目录的磁盘使用情况\n", "        total_gb = total / (1024**3) # 转换为 GB\n", "        used_gb = used / (1024**3) # 转换为 GB\n", "        free_gb = free / (1024**3) # 转换为 GB\n", "        return f\"Total: {total_gb:.2f} GB, Used: {used_gb:.2f} GB, Free: {free_gb:.2f} GB\" # 格式化输出\n", "    except Exception as e:\n", "        return f\"Could not retrieve disk info: {e}\" # 如果获取信息出错，设置错误信息\n", "\n", "# 获取环境信息\n", "os_name = platform.system() # 获取操作系统名称\n", "os_version = platform.release() # 获取操作系统版本\n", "if os_name == \"Linux\":\n", "    try:\n", "        # 在 Linux 上尝试获取发行版和版本\n", "        lsb_info = subprocess.run(['lsb_release', '-a'], capture_output=True, text=True)\n", "        if lsb_info.returncode == 0: # 如果命令成功执行\n", "            for line in lsb_info.stdout.splitlines():\n", "                if 'Description:' in line: # 查找包含 'Description:' 的行\n", "                    os_version = line.split('Description:')[1].strip() # 提取描述信息作为版本\n", "                    break # 找到后退出循环\n", "                elif 'Release:' in line: # 查找包含 'Release:' 的行\n", "                     os_version = line.split('Release:')[1].strip() # 提取版本号\n", "                     # 尝试获取 codename\n", "                     try:\n", "                         codename_info = subprocess.run(['lsb_release', '-c'], capture_output=True, text=True)\n", "                         if codename_info.returncode == 0:\n", "                             os_version += f\" ({codename_info.stdout.split(':')[1].strip()})\" # 将 codename 添加到版本信息中\n", "                     except:\n", "                         pass # 如果获取 codename 失败则忽略\n", "\n", "    except FileNotFoundError:\n", "        pass # lsb_release 可能未安装，忽略错误\n", "\n", "full_os_info = f\"{os_name} {os_version}\" # 组合完整的操作系统信息\n", "cpu_info = get_cpu_info() # 调用函数获取 CPU 信息和核心数量\n", "memory_info = get_memory_info() # 调用函数获取内存信息\n", "gpu_info = get_gpu_info() # 调用函数获取 GPU 信息和显存\n", "cuda_version = get_cuda_version() # 调用函数获取 CUDA 版本\n", "python_version = get_python_version() # 调用函数获取 Python 版本\n", "conda_version = get_conda_version() # 调用函数获取 Conda 版本\n", "disk_info = get_disk_space() # 调用函数获取物理磁盘空间信息\n", "\n", "\n", "# 创建用于存储数据的字典\n", "env_data = {\n", "    \"项目\": [ # 项目名称列表\n", "        \"操作系统\",\n", "        \"CPU 信息\",\n", "        \"内存信息\",\n", "        \"GPU 信息\",\n", "        \"CUDA 信息\",\n", "        \"Python 版本\",\n", "        \"Conda 版本\",\n", "        \"物理磁盘空间\" # 添加物理磁盘空间\n", "    ],\n", "    \"信息\": [ # 对应的信息列表\n", "        full_os_info,\n", "        cpu_info,\n", "        memory_info,\n", "        gpu_info,\n", "        cuda_version,\n", "        python_version,\n", "        conda_version,\n", "        disk_info # 添加物理磁盘空间信息\n", "    ]\n", "}\n", "\n", "# 创建一个 pandas DataFrame\n", "df = pd.DataFrame(env_data)\n", "\n", "# 打印表格\n", "print(\"### 环境信息\") # 打印标题\n", "print(df.to_markdown(index=False)) # 将 DataFrame 转换为 Markdown 格式并打印，不包含索引"], "metadata": {"id": "LCDKeD2Oi6f8", "outputId": "f5d02626-cfc0-44e6-b0d5-13d8dba7f992", "colab": {"base_uri": "https://localhost:8080/"}}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: pandas==2.2.2 in /usr/local/lib/python3.11/dist-packages (2.2.2)\n", "Requirement already satisfied: numpy>=1.23.2 in /usr/local/lib/python3.11/dist-packages (from pandas==2.2.2) (2.0.2)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.11/dist-packages (from pandas==2.2.2) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas==2.2.2) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas==2.2.2) (2025.2)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.8.2->pandas==2.2.2) (1.17.0)\n", "### 环境信息\n", "| 项目         | 信息                                                               |\n", "|:-------------|:-------------------------------------------------------------------|\n", "| 操作系统     | Linux Ubuntu 22.04.4 LTS                                           |\n", "| CPU 信息     | Intel(R) Xeon(R) CPU @ 2.20GHz (1 physical cores, 2 logical cores) |\n", "| 内存信息     | 12.67 GB (Available: 10.44 GB)                                     |\n", "| GPU 信息     | No GPU found (nvidia-smi not found)                                |\n", "| CUDA 信息    | 12.5                                                               |\n", "| Python 版本  | 3.11.13                                                            |\n", "| Conda 版本   | Conda not found                                                    |\n", "| 物理磁盘空间 | Total: 107.72 GB, Used: 37.81 GB, Free: 69.88 GB                   |\n"]}]}, {"cell_type": "markdown", "source": ["## 📦 第二步：安装依赖包\n", "\n", "现在我们需要安装运行模型所需的关键 Python 包："], "metadata": {"id": "fZ8QQTREjOkH"}}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "dY6Yd0lxhTFU", "outputId": "7d5abdae-dfaf-4ca3-bc40-16cdc79aa116", "colab": {"base_uri": "https://localhost:8080/"}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: transformers in /usr/local/lib/python3.11/dist-packages (4.53.2)\n", "Requirement already satisfied: datasets in /usr/local/lib/python3.11/dist-packages (2.14.4)\n", "Collecting evaluate\n", "  Downloading evaluate-0.4.5-py3-none-any.whl.metadata (9.5 kB)\n", "Requirement already satisfied: accelerate in /usr/local/lib/python3.11/dist-packages (1.8.1)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from transformers) (3.18.0)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.30.0 in /usr/local/lib/python3.11/dist-packages (from transformers) (0.33.4)\n", "Requirement already satisfied: numpy>=1.17 in /usr/local/lib/python3.11/dist-packages (from transformers) (2.0.2)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.11/dist-packages (from transformers) (25.0)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.11/dist-packages (from transformers) (6.0.2)\n", "Requirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.11/dist-packages (from transformers) (2024.11.6)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from transformers) (2.32.3)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in /usr/local/lib/python3.11/dist-packages (from transformers) (0.21.2)\n", "Requirement already satisfied: safetensors>=0.4.3 in /usr/local/lib/python3.11/dist-packages (from transformers) (0.5.3)\n", "Requirement already satisfied: tqdm>=4.27 in /usr/local/lib/python3.11/dist-packages (from transformers) (4.67.1)\n", "Requirement already satisfied: pyarrow>=8.0.0 in /usr/local/lib/python3.11/dist-packages (from datasets) (18.1.0)\n", "Requirement already satisfied: dill<0.3.8,>=0.3.0 in /usr/local/lib/python3.11/dist-packages (from datasets) (0.3.7)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.11/dist-packages (from datasets) (2.2.2)\n", "Requirement already satisfied: xxhash in /usr/local/lib/python3.11/dist-packages (from datasets) (3.5.0)\n", "Requirement already satisfied: multiprocess in /usr/local/lib/python3.11/dist-packages (from datasets) (0.70.15)\n", "Requirement already satisfied: fsspec>=2021.11.1 in /usr/local/lib/python3.11/dist-packages (from fsspec[http]>=2021.11.1->datasets) (2025.3.2)\n", "Requirement already satisfied: aiohttp in /usr/local/lib/python3.11/dist-packages (from datasets) (3.11.15)\n", "Requirement already satisfied: psutil in /usr/local/lib/python3.11/dist-packages (from accelerate) (5.9.5)\n", "Requirement already satisfied: torch>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from accelerate) (2.6.0+cu124)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->datasets) (2.6.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from aiohttp->datasets) (1.4.0)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->datasets) (25.3.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.11/dist-packages (from aiohttp->datasets) (1.7.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.11/dist-packages (from aiohttp->datasets) (6.6.3)\n", "Requirement already satisfied: propcache>=0.2.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->datasets) (0.3.2)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->datasets) (1.20.1)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.30.0->transformers) (4.14.1)\n", "Requirement already satisfied: hf-xet<2.0.0,>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.30.0->transformers) (1.1.5)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (2025.7.14)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (3.5)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (3.1.6)\n", "Collecting nvidia-cuda-nvrtc-cu12==12.4.127 (from torch>=2.0.0->accelerate)\n", "  Downloading nvidia_cuda_nvrtc_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-runtime-cu12==12.4.127 (from torch>=2.0.0->accelerate)\n", "  Downloading nvidia_cuda_runtime_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-cupti-cu12==12.4.127 (from torch>=2.0.0->accelerate)\n", "  Downloading nvidia_cuda_cupti_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cudnn-cu12==******** (from torch>=2.0.0->accelerate)\n", "  Downloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cublas-cu12==******** (from torch>=2.0.0->accelerate)\n", "  Downloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cufft-cu12==******** (from torch>=2.0.0->accelerate)\n", "  Downloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-curand-cu12==********** (from torch>=2.0.0->accelerate)\n", "  Downloading nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cusolver-cu12==******** (from torch>=2.0.0->accelerate)\n", "  Downloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cusparse-cu12==********** (from torch>=2.0.0->accelerate)\n", "  Downloading nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Requirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (0.6.2)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (2.21.5)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (12.4.127)\n", "Collecting nvidia-nvjitlink-cu12==12.4.127 (from torch>=2.0.0->accelerate)\n", "  Downloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Requirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (3.2.0)\n", "Requirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (1.13.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch>=2.0.0->accelerate) (1.3.0)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.11/dist-packages (from pandas->datasets) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas->datasets) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas->datasets) (2025.2)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.8.2->pandas->datasets) (1.17.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch>=2.0.0->accelerate) (3.0.2)\n", "Downloading evaluate-0.4.5-py3-none-any.whl (84 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m84.1/84.1 kB\u001b[0m \u001b[31m3.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl (363.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m363.4/363.4 MB\u001b[0m \u001b[31m3.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_cupti_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (13.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.8/13.8 MB\u001b[0m \u001b[31m89.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_nvrtc_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (24.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m24.6/24.6 MB\u001b[0m \u001b[31m65.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_runtime_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (883 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m883.7/883.7 kB\u001b[0m \u001b[31m53.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl (664.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m664.8/664.8 MB\u001b[0m \u001b[31m2.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl (211.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m211.5/211.5 MB\u001b[0m \u001b[31m5.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl (56.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m15.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl (127.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.9/127.9 MB\u001b[0m \u001b[31m7.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl (207.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m207.5/207.5 MB\u001b[0m \u001b[31m7.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.1/21.1 MB\u001b[0m \u001b[31m75.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: nvidia-nvjitlink-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, nvidia-cusparse-cu12, nvidia-cudnn-cu12, nvidia-cusolver-cu12, evaluate\n", "  Attempting uninstall: nvidia-nvjitlink-cu12\n", "    Found existing installation: nvidia-nvjitlink-cu12 12.5.82\n", "    Uninstalling nvidia-nvjitlink-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-nvjitlink-cu12-12.5.82\n", "  Attempting uninstall: nvidia-curand-cu12\n", "    Found existing installation: nvidia-curand-cu12 10.3.6.82\n", "    Uninstalling nvidia-curand-cu12-10.3.6.82:\n", "      Successfully uninstalled nvidia-curand-cu12-10.3.6.82\n", "  Attempting uninstall: nvidia-cufft-cu12\n", "    Found existing installation: nvidia-cufft-cu12 11.2.3.61\n", "    Uninstalling nvidia-cufft-cu12-11.2.3.61:\n", "      Successfully uninstalled nvidia-cufft-cu12-11.2.3.61\n", "  Attempting uninstall: nvidia-cuda-runtime-cu12\n", "    Found existing installation: nvidia-cuda-runtime-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-runtime-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-runtime-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cuda-nvrtc-cu12\n", "    Found existing installation: nvidia-cuda-nvrtc-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-nvrtc-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-nvrtc-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cuda-cupti-cu12\n", "    Found existing installation: nvidia-cuda-cupti-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-cupti-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-cupti-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cublas-cu12\n", "    Found existing installation: nvidia-cublas-cu12 12.5.3.2\n", "    Uninstalling nvidia-cublas-cu12-12.5.3.2:\n", "      Successfully uninstalled nvidia-cublas-cu12-12.5.3.2\n", "  Attempting uninstall: nvidia-cusparse-cu12\n", "    Found existing installation: nvidia-cusparse-cu12 12.5.1.3\n", "    Uninstalling nvidia-cusparse-cu12-12.5.1.3:\n", "      Successfully uninstalled nvidia-cusparse-cu12-12.5.1.3\n", "  Attempting uninstall: nvidia-cudnn-cu12\n", "    Found existing installation: nvidia-cudnn-cu12 9.3.0.75\n", "    Uninstalling nvidia-cudnn-cu12-9.3.0.75:\n", "      Successfully uninstalled nvidia-cudnn-cu12-9.3.0.75\n", "  Attempting uninstall: nvidia-cusolver-cu12\n", "    Found existing installation: nvidia-cusolver-cu12 *********\n", "    Uninstalling nvidia-cusolver-cu12-*********:\n", "      Successfully uninstalled nvidia-cusolver-cu12-*********\n", "Successfully installed evaluate-0.4.5 nvidia-cublas-cu12-******** nvidia-cuda-cupti-cu12-12.4.127 nvidia-cuda-nvrtc-cu12-12.4.127 nvidia-cuda-runtime-cu12-12.4.127 nvidia-cudnn-cu12-******** nvidia-cufft-cu12-******** nvidia-curand-cu12-********** nvidia-cusolver-cu12-******** nvidia-cusparse-cu12-********** nvidia-nvjitlink-cu12-12.4.127\n"]}], "source": ["# Transformers 安装\n", "! pip install transformers datasets evaluate accelerate\n", "# 如果要从源码安装而不是最新发布版本，请注释上面的命令并取消注释下面的命令。\n", "# ! pip install git+https://github.com/huggingface/transformers.git"]}, {"cell_type": "markdown", "metadata": {"id": "ZOOKsqLzhTFV"}, "source": ["# 文本生成"]}, {"cell_type": "markdown", "metadata": {"id": "tQGWT9pchTFW"}, "source": ["文本生成是大语言模型（LLMs）最受欢迎的应用。大语言模型经过训练，能够根据给定的初始文本（提示词）以及其自身生成的输出，生成下一个词（token），直到达到预定义的长度或遇到序列结束（`EOS`）token。\n", "\n", "在 Transformers 中，[generate()](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationMixin.generate) API 负责处理文本生成，它适用于所有具有生成能力的模型。本指南将向您展示使用 [generate()](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationMixin.generate) 进行文本生成的基础知识以及一些需要避免的常见陷阱。\n", "\n", "\n", "> [!TIP] 您也可以直接从命令行与模型对话。([参考文档](https://huggingface.co/docs/transformers/main/en/./conversations.md#transformers-cli)) ``` transformers chat Qwen/Qwen2.5-0.5B-Instruct```"]}, {"cell_type": "markdown", "metadata": {"id": "DRAZwtVOhTFW"}, "source": ["## 默认生成"]}, {"cell_type": "markdown", "metadata": {"id": "ezzEjK1KhTFW"}, "source": ["在开始之前，建议安装 [bitsandbytes](https://hf.co/docs/bitsandbytes/index) 来量化超大模型以减少内存使用。\\n\\n```bash\\n!pip install -U transformers bitsandbytes\\n```\\n除了基于 CUDA 的 GPU 外，Bitsandbytes 还支持多种后端。请参考多后端安装[指南](https://huggingface.co/docs/bitsandbytes/main/en/installation#multi-backend)了解更多信息。\\n\\n使用 [from_pretrained()](https://huggingface.co/docs/transformers/main/en/main_classes/model#transformers.PreTrainedModel.from_pretrained) 加载大语言模型，并添加以下两个参数来减少内存需求。\\n\\n- `device_map=\\\"auto\\\"` 启用 Accelerate 的[大模型推理](https://huggingface.co/docs/transformers/main/en/./models#big-model-inference)功能，用于自动初始化模型骨架并在所有可用设备上加载和分发模型权重，从最快的设备（GPU）开始。\\n- `quantization_config` 是定义量化设置的配置对象。此示例使用 bitsandbytes 作为量化后端（有关更多可用后端，请参见[量化](https://huggingface.co/docs/transformers/main/en/./quantization/overview)部分），并以 [4位](https://huggingface.co/docs/transformers/main/en/./quantization/bitsandbytes)精度加载模型。"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "lyoAz8pQhTFX"}, "outputs": [], "source": ["from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig\n", "\n", "quantization_config = BitsAndBytesConfig(load_in_4bit=True)\n", "model = AutoModelForCausalLM.from_pretrained(\"mistralai/Mistral-7B-v0.1\",device_map=\"auto\",quantization_config=quantization_config)"]}, {"cell_type": "markdown", "metadata": {"id": "T6n5hOS-hTFX"}, "source": ["对输入进行分词，并将 `padding_side()` 参数设置为 `\\\"left\\\"`，因为大语言模型没有被训练从填充token继续生成。分词器返回输入ID和注意力掩码。\\n\\n> [!TIP]\\n> 通过向分词器传递字符串列表，可以一次处理多个提示词。批量处理输入可以提高吞吐量，但会略微增加延迟和内存消耗。"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "fmbojuWOhTFX"}, "outputs": [], "source": ["tokenizer = AutoTokenizer.from_pretrained(\\\"mistralai/Mistral-7B-v0.1\\\", padding_side=\\\"left\\\")\\nmodel_inputs = tokenizer([\\\"A list of colors: red, blue\\\"], return_tensors=\\\"pt\\\").to(\\\"cuda\\\")"]}, {"cell_type": "markdown", "metadata": {"id": "vj8TOHW6hTFX"}, "source": ["将输入传递给 [generate()](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationMixin.generate) 来生成token，并使用 [batch_decode()](https://huggingface.co/docs/transformers/main/en/internal/tokenization_utils#transformers.PreTrainedTokenizerBase.batch_decode) 将生成的token解码回文本。"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "6ePzVeehhTFX"}, "outputs": [], "source": ["generated_ids = model.generate(**model_inputs)\\ntokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]\\n\\\"A list of colors: red, blue, green, yellow, orange, purple, pink,\\\""]}, {"cell_type": "markdown", "metadata": {"id": "sKQTWvSXhTFY"}, "source": ["## 生成配置"]}, {"cell_type": "markdown", "metadata": {"id": "ZSd7zTyXhTFY"}, "source": ["所有生成设置都包含在 [GenerationConfig](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationConfig) 中。在上面的示例中，生成设置来自 [mistralai/Mistral-7B-v0.1](https://huggingface.co/mistralai/Mistral-7B-v0.1) 的 `generation_config.json` 文件。当模型没有保存配置时，会使用默认的解码策略。\\n\\n通过 `generation_config` 属性检查配置。它只显示与默认配置不同的值，在这种情况下是 `bos_token_id` 和 `eos_token_id`。"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "LSRH2qGNhTFY"}, "outputs": [], "source": ["from transformers import AutoModelForCausalLM\\n\\nmodel = AutoModelForCausalLM.from_pretrained(\\\"mistralai/Mistral-7B-v0.1\\\", device_map=\\\"auto\\\")\\nmodel.generation_config\\nGenerationConfig {\\n  \\\"bos_token_id\\\": 1,\\n  \\\"eos_token_id\\\": 2\\n}"]}, {"cell_type": "markdown", "metadata": {"id": "fQZRgvI_hTFY"}, "source": ["您可以通过覆盖 [GenerationConfig](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationConfig) 中的参数和值来自定义 [generate()](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationMixin.generate)。有关常用调整参数，请参见[下面的部分](#common-options)。"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "tIREFqj3hTFY"}, "outputs": [], "source": ["# 启用束搜索采样策略\\nmodel.generate(**model_inputs, num_beams=4, do_sample=True)"]}, {"cell_type": "markdown", "metadata": {"id": "w1DIN4VlhTFY"}, "source": ["[generate()](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationMixin.generate) 也可以通过外部库或自定义代码进行扩展：\\n1. `logits_processor` 参数接受自定义的 [LogitsProcessor](https://huggingface.co/docs/transformers/main/en/internal/generation_utils#transformers.LogitsProcessor) 实例，用于操作下一个token的概率分布；\\n2. `stopping_criteria` 参数支持自定义的 [StoppingCriteria](https://huggingface.co/docs/transformers/main/en/internal/generation_utils#transformers.StoppingCriteria) 来停止文本生成；\\n3. 其他自定义生成方法可以通过 `custom_generate` 标志加载（[文档](https://huggingface.co/docs/transformers/main/en/generation_strategies.md/#custom-decoding-methods)）。\\n\\n请参考[生成策略](https://huggingface.co/docs/transformers/main/en/./generation_strategies)指南，了解更多关于搜索、采样和解码策略的信息。"]}, {"cell_type": "markdown", "metadata": {"id": "IsqZn2HFhTFY"}, "source": ["### 保存"]}, {"cell_type": "markdown", "metadata": {"id": "thvnic79hTFY"}, "source": ["创建一个 [GenerationConfig](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationConfig) 实例并指定您想要的解码参数。"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "e3vNHZ6xhTFY"}, "outputs": [], "source": ["from transformers import AutoModelForCausalLM, GenerationConfig\\n\\nmodel = AutoModelForCausalLM.from_pretrained(\\\"my_account/my_model\\\")\\ngeneration_config = GenerationConfig(\\n    max_new_tokens=50, do_sample=True, top_k=50, eos_token_id=model.config.eos_token_id\\n)"]}, {"cell_type": "markdown", "metadata": {"id": "L5kz2I_KhTFY"}, "source": ["使用 [save_pretrained()](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationConfig.save_pretrained) 保存特定的生成配置，并将 `push_to_hub` 参数设置为 `True` 以将其上传到 Hub。"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "KJUtKshPhTFZ"}, "outputs": [], "source": ["generation_config.save_pretrained(\\\"my_account/my_model\\\", push_to_hub=True)"]}, {"cell_type": "markdown", "metadata": {"id": "umN_PKAkhTFZ"}, "source": ["将 `config_file_name` 参数留空。当在单个目录中存储多个生成配置时，应使用此参数。它为您提供了一种指定要加载哪个生成配置的方法。您可以为不同的生成任务创建不同的配置（使用采样的创意文本生成、使用束搜索的摘要生成），以便与单个模型一起使用。"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "pHEV8GfDhTFZ"}, "outputs": [], "source": ["from transformers import AutoModelForSeq2SeqLM, AutoTokenizer, GenerationConfig\\n\\ntokenizer = AutoTokenizer.from_pretrained(\\\"google-t5/t5-small\\\")\\nmodel = AutoModelForSeq2SeqLM.from_pretrained(\\\"google-t5/t5-small\\\")\\n\\ntranslation_generation_config = GenerationConfig(\\n    num_beams=4,\\n    early_stopping=True,\\n    decoder_start_token_id=0,\\n    eos_token_id=model.config.eos_token_id,\\n    pad_token=model.config.pad_token_id,\\n)\\n\\ntranslation_generation_config.save_pretrained(\\\"/tmp\\\", config_file_name=\\\"translation_generation_config.json\\\", push_to_hub=True)\\n\\ngeneration_config = GenerationConfig.from_pretrained(\\\"/tmp\\\", config_file_name=\\\"translation_generation_config.json\\\")\\ninputs = tokenizer(\\\"translate English to French: Configuration files are easy to use!\\\", return_tensors=\\\"pt\\\")\\noutputs = model.generate(**inputs, generation_config=generation_config)\\nprint(tokenizer.batch_decode(outputs, skip_special_tokens=True))"]}, {"cell_type": "markdown", "metadata": {"id": "qS7-zU8fhTFZ"}, "source": ["## 常用选项"]}, {"cell_type": "markdown", "metadata": {"id": "lVdlq5F9hTFZ"}, "source": ["[generate()](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationMixin.generate) 是一个功能强大且可高度自定义的工具。这对新用户来说可能会感到困惑。本节包含了您可以在 Transformers 的大多数文本生成工具中定义的常用生成选项列表：[generate()](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationMixin.generate)、[GenerationConfig](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationConfig)、`pipelines`、`chat` CLI 等...\\n\\n| 选项名称 | 类型 | 简化描述 |\\n|---|---|---|\\n| `max_new_tokens` | `int` | 控制最大生成长度。请务必定义它，因为它通常默认为一个较小的值。 |\\n| `do_sample` | `bool` | 定义生成是否会采样下一个token（`True`），或者使用贪心策略（`False`）。大多数用例应将此标志设置为 `True`。查看[此指南](https://huggingface.co/docs/transformers/main/en/./generation_strategies.md)了解更多信息。 |\\n| `temperature` | `float` | 下一个选择的token的不可预测性。高值（`>0.8`）适合创意任务，低值（例如 `<0.4`）适合需要\\\"思考\\\"的任务。需要 `do_sample=True`。 |\\n| `num_beams` | `int` | 当设置为 `>1` 时，激活束搜索算法。束搜索在基于输入的任务上表现良好。查看[此指南](https://huggingface.co/docs/transformers/main/en/./generation_strategies.md)了解更多信息。 |\\n| `repetition_penalty` | `float` | 如果您发现模型经常重复自己，请将其设置为 `>1.0`。较大的值会施加更大的惩罚。 |\\n| `eos_token_id` | `list[int]` | 将导致生成停止的token。默认值通常是好的，但您可以指定不同的token。 |"]}, {"cell_type": "markdown", "metadata": {"id": "RmcK3zZehTFZ"}, "source": ["## 常见陷阱"]}, {"cell_type": "markdown", "metadata": {"id": "5G3b3M2AhTFZ"}, "source": ["下面的部分涵盖了您在文本生成过程中可能遇到的一些常见问题以及如何解决它们。"]}, {"cell_type": "markdown", "metadata": {"id": "Z3qbYlxahTFZ"}, "source": ["### 输出长度"]}, {"cell_type": "markdown", "metadata": {"id": "GsR5oEAGhTFZ"}, "source": ["[generate()](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationMixin.generate) 默认返回最多20个token，除非在模型的 [GenerationConfig](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationConfig) 中另有指定。强烈建议使用 `max_new_tokens` 参数手动设置生成的token数量来控制输出长度。[仅解码器](https://hf.co/learn/nlp-course/chapter1/6?fw=pt)模型返回初始提示词以及生成的token。"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "steCJMfbhTFZ"}, "outputs": [], "source": ["model_inputs = tokenizer([\\\"A sequence of numbers: 1, 2\\\"], return_tensors=\\\"pt\\\").to(\\\"cuda\\\")"]}, {"cell_type": "markdown", "metadata": {"id": "QNK2w_nchTFa"}, "source": ["**默认长度：**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "RSqGLQWlhTFa"}, "outputs": [], "source": ["generated_ids = model.generate(**model_inputs)\\ntokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]\\n# 输出: 'A sequence of numbers: 1, 2, 3, 4, 5'"]}, {"cell_type": "markdown", "metadata": {"id": "6iS-AG4AhTFa"}, "source": ["**使用 max_new_tokens：**"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "QxF1No3phTFa"}, "outputs": [], "source": ["generated_ids = model.generate(**model_inputs, max_new_tokens=50)\\ntokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]\\n# 输出: 'A sequence of numbers: 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16,'"]}, {"cell_type": "markdown", "metadata": {"id": "IeSAhsfmhTFa"}, "source": ["### 解码策略"]}, {"cell_type": "markdown", "metadata": {"id": "QFPftKG-hTFa"}, "source": ["[generate()](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationMixin.generate) 中的默认解码策略是*贪心搜索*，它选择下一个最可能的token，除非在模型的 [GenerationConfig](https://huggingface.co/docs/transformers/main/en/main_classes/text_generation#transformers.GenerationConfig) 中另有指定。虽然这种解码策略在基于输入的任务（转录、翻译）上表现良好，但对于更具创意的用例（故事写作、聊天应用）来说并不是最优的。\\n\\n例如，启用[多项式采样](https://huggingface.co/docs/transformers/main/en/./generation_strategies#multinomial-sampling)策略来生成更多样化的输出。请参考[生成策略](https://huggingface.co/docs/transformers/main/en/./generation_strategies)指南了解更多解码策略。"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "zRJq2jRihTFa"}, "outputs": [], "source": ["model_inputs = tokenizer([\\\"I am a cat.\\\"], return_tensors=\\\"pt\\\").to(\\\"cuda\\\")\\n\\n# 贪心搜索\\ngenerated_ids = model.generate(**model_inputs)\\nprint(\\\"贪心搜索:\\\", tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0])\\n\\n# 多项式采样\\ngenerated_ids = model.generate(**model_inputs, do_sample=True)\\nprint(\\\"多项式采样:\\\", tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0])"]}, {"cell_type": "markdown", "metadata": {"id": "k1Ga1v9LhTFa"}, "source": ["### 填充方向"]}, {"cell_type": "markdown", "metadata": {"id": "LhduPBS2hTFa"}, "source": ["如果输入长度不同，需要进行填充。但是大语言模型没有被训练从填充token继续生成，这意味着 `padding_side()` 参数需要设置为输入的左侧。"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "S_yzSmBChTFf"}, "outputs": [], "source": ["# 右填充（错误）\\ntokenizer_right = AutoTokenizer.from_pretrained(\\\"mistralai/Mistral-7B-v0.1\\\", padding_side=\\\"right\\\")\\nmodel_inputs = tokenizer_right([\\\"1, 2, 3\\\", \\\"A, B, C, D, E\\\"], padding=True, return_tensors=\\\"pt\\\").to(\\\"cuda\\\")\\ngenerated_ids = model.generate(**model_inputs)\\nprint(\\\"右填充结果:\\\", tokenizer_right.batch_decode(generated_ids, skip_special_tokens=True)[0])\\n\\n# 左填充（正确）\\ntokenizer_left = AutoTokenizer.from_pretrained(\\\"mistralai/Mistral-7B-v0.1\\\", padding_side=\\\"left\\\")\\ntokenizer_left.pad_token = tokenizer_left.eos_token\\nmodel_inputs = tokenizer_left([\\\"1, 2, 3\\\", \\\"A, B, C, D, E\\\"], padding=True, return_tensors=\\\"pt\\\").to(\\\"cuda\\\")\\ngenerated_ids = model.generate(**model_inputs)\\nprint(\\\"左填充结果:\\\", tokenizer_left.batch_decode(generated_ids, skip_special_tokens=True)[0])"]}, {"cell_type": "markdown", "metadata": {"id": "fiV3XjZ1hTFf"}, "source": ["### 提示词格式"]}, {"cell_type": "markdown", "metadata": {"id": "cy4q8PXohTFf"}, "source": ["某些模型和任务期望特定的输入提示词格式，如果格式不正确，模型会返回次优的输出。您可以在[提示词工程](https://huggingface.co/docs/transformers/main/en/./tasks/prompting)指南中了解更多关于提示词的信息。\\n\\n例如，聊天模型期望输入为[聊天模板](https://huggingface.co/docs/transformers/main/en/./chat_templating)。您的提示词应包含 `role` 和 `content` 来指示谁在参与对话。如果您尝试将提示词作为单个字符串传递，模型并不总是返回预期的输出。"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "iprxRuBlhTFg"}, "outputs": [], "source": ["from transformers import AutoTokenizer, AutoModelForCausalLM\\n\\ntokenizer = AutoTokenizer.from_pretrained(\\\"HuggingFaceH4/zephyr-7b-alpha\\\")\\nmodel = AutoModelForCausalLM.from_pretrained(\\\"HuggingFaceH4/zephyr-7b-alpha\\\", device_map=\\\"auto\\\", load_in_4bit=True)\\n\\n# 无格式\\nprompt = \\\"How many cats does it take to change a light bulb? Reply as a pirate.\\\"\\nmodel_inputs = tokenizer([prompt], return_tensors=\\\"pt\\\").to(\\\"cuda\\\")\\ninput_length = model_inputs.input_ids.shape[1]\\ngenerated_ids = model.generate(**model_inputs, max_new_tokens=50)\\nprint(\\\"无格式:\\\", tokenizer.batch_decode(generated_ids[:, input_length:], skip_special_tokens=True)[0])\\n\\n# 聊天模板\\nmessages = [\\n    {\\\"role\\\": \\\"system\\\", \\\"content\\\": \\\"You are a friendly chatbot who always responds in the style of a pirate\\\"},\\n    {\\\"role\\\": \\\"user\\\", \\\"content\\\": \\\"How many cats does it take to change a light bulb?\\\"}\\n]\\nmodel_inputs = tokenizer.apply_chat_template(messages, add_generation_prompt=True, return_tensors=\\\"pt\\\").to(\\\"cuda\\\")\\ninput_length = model_inputs.shape[1]\\ngenerated_ids = model.generate(model_inputs, do_sample=True, max_new_tokens=50)\\nprint(\\\"聊天模板:\\\", tokenizer.batch_decode(generated_ids[:, input_length:], skip_special_tokens=True)[0])"]}, {"cell_type": "markdown", "metadata": {"id": "MkEGxFpKhTFg"}, "source": ["## 资源"]}, {"cell_type": "markdown", "metadata": {"id": "beYlBJjKhTFg"}, "source": ["请查看下面一些更具体和专业的文本生成库。\\n\\n- [Optimum](https://github.com/huggingface/optimum)：Transformers 的扩展，专注于在特定硬件设备上优化训练和推理\\n- [Outlines](https://github.com/dottxt-ai/outlines)：用于约束文本生成的库（例如生成 JSON 文件）。\\n- [SynCode](https://github.com/uiuc-focal-lab/syncode)：用于上下文无关语法引导生成的库（JSON、SQL、Python）。\\n- [Text Generation Inference](https://github.com/huggingface/text-generation-inference)：用于大语言模型的生产就绪服务器。\\n- [Text generation web UI](https://github.com/oobabooga/text-generation-webui)：用于文本生成的 Gradio Web UI。\\n- [logits-processor-zoo](https://github.com/NVIDIA/logits-processor-zoo)：用于控制文本生成的额外 logits 处理器。"]}], "metadata": {"colab": {"provenance": []}, "language_info": {"name": "python"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}}, "nbformat": 4, "nbformat_minor": 0}
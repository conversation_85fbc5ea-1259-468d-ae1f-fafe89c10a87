{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "collapsed_sections": ["mLVXJRQ7tiBe", "Ao78SDEp1Xbf", "j0-Zs6KZ6Dgi", "L1nWOkGvCStj", "NySCEE0-tKw-", "7gGjGMljB24P", "dhZGQ72NODIZ", "CVApiSmm8ocW", "3bUGvgAqJfkt", "5QVu6_HVJTlW", "00kLaQZWS-Jq", "2dh9Zxt6NqAY", "dOIICEUZvjR0"], "toc_visible": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["文章已开源，欢迎star\n", "https://github.com/liaokongVFX/LangChain-Chinese-Getting-Started-Guide"], "metadata": {"id": "PQSLwy9FqC66"}}, {"cell_type": "markdown", "source": ["#装包以及初始化"], "metadata": {"id": "mLVXJRQ7tiBe"}}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "yH_o5pZM0pUj", "outputId": "838e65eb-a401-4e17-a2af-3b0eb9d01ec8"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Looking in indexes: https://pypi.org/simple, https://us-python.pkg.dev/colab-wheels/public/simple/\n", "Collecting langchain\n", "  Downloading langchain-0.0.163-py3-none-any.whl (781 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m782.0/782.0 kB\u001b[0m \u001b[31m12.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: PyYAML>=5.4.1 in /usr/local/lib/python3.10/dist-packages (from langchain) (6.0)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.0.10)\n", "Collecting aiohttp<4.0.0,>=3.8.3 (from langchain)\n", "  Downloading aiohttp-3.8.4-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.0/1.0 MB\u001b[0m \u001b[31m26.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting async-timeout<5.0.0,>=4.0.0 (from langchain)\n", "  Downloading async_timeout-4.0.2-py3-none-any.whl (5.8 kB)\n", "Collecting dataclasses-json<0.6.0,>=0.5.7 (from langchain)\n", "  Downloading dataclasses_json-0.5.7-py3-none-any.whl (25 kB)\n", "Requirement already satisfied: numexpr<3.0.0,>=2.8.4 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.8.4)\n", "Requirement already satisfied: numpy<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (1.22.4)\n", "Collecting openapi-schema-pydantic<2.0,>=1.2 (from langchain)\n", "  Downloading openapi_schema_pydantic-1.2.4-py3-none-any.whl (90 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m90.0/90.0 kB\u001b[0m \u001b[31m4.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pydantic<2,>=1 in /usr/local/lib/python3.10/dist-packages (from langchain) (1.10.7)\n", "Requirement already satisfied: requests<3,>=2 in /usr/local/lib/python3.10/dist-packages (from langchain) (2.27.1)\n", "Requirement already satisfied: tenacity<9.0.0,>=8.1.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (8.2.2)\n", "Requirement already satisfied: tqdm>=4.48.0 in /usr/local/lib/python3.10/dist-packages (from langchain) (4.65.0)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (23.1.0)\n", "Requirement already satisfied: charset-normalizer<4.0,>=2.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (2.0.12)\n", "Collecting multidict<7.0,>=4.5 (from aiohttp<4.0.0,>=3.8.3->langchain)\n", "  Downloading multidict-6.0.4-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (114 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m114.5/114.5 kB\u001b[0m \u001b[31m4.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting yarl<2.0,>=1.0 (from aiohttp<4.0.0,>=3.8.3->langchain)\n", "  Downloading yarl-1.9.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (268 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m268.8/268.8 kB\u001b[0m \u001b[31m5.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting frozenlist>=1.1.1 (from aiohttp<4.0.0,>=3.8.3->langchain)\n", "  Downloading frozenlist-1.3.3-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (149 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m149.6/149.6 kB\u001b[0m \u001b[31m7.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting aiosignal>=1.1.2 (from aiohttp<4.0.0,>=3.8.3->langchain)\n", "  Downloading aiosignal-1.3.1-py3-none-any.whl (7.6 kB)\n", "Collecting marshmallow<4.0.0,>=3.3.0 (from dataclasses-json<0.6.0,>=0.5.7->langchain)\n", "  Downloading marshmallow-3.19.0-py3-none-any.whl (49 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.1/49.1 kB\u001b[0m \u001b[31m2.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting marshmallow-enum<2.0.0,>=1.5.1 (from dataclasses-json<0.6.0,>=0.5.7->langchain)\n", "  Downloading marshmallow_enum-1.5.1-py2.py3-none-any.whl (4.2 kB)\n", "Collecting typing-inspect>=0.4.0 (from dataclasses-json<0.6.0,>=0.5.7->langchain)\n", "  Downloading typing_inspect-0.8.0-py3-none-any.whl (8.7 kB)\n", "Requirement already satisfied: typing-extensions>=4.2.0 in /usr/local/lib/python3.10/dist-packages (from pydantic<2,>=1->langchain) (4.5.0)\n", "Requirement already satisfied: urllib3<1.27,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (1.26.15)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (2022.12.7)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests<3,>=2->langchain) (3.4)\n", "Requirement already satisfied: greenlet!=0.4.17 in /usr/local/lib/python3.10/dist-packages (from SQLAlchemy<3,>=1.4->langchain) (2.0.2)\n", "Requirement already satisfied: packaging>=17.0 in /usr/local/lib/python3.10/dist-packages (from marshmallow<4.0.0,>=3.3.0->dataclasses-json<0.6.0,>=0.5.7->langchain) (23.1)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect>=0.4.0->dataclasses-json<0.6.0,>=0.5.7->langchain)\n", "  Downloading mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Installing collected packages: mypy-extensions, multidict, marshmallow, frozenlist, async-timeout, yarl, typing-inspect, openapi-schema-pydantic, marshmallow-enum, aiosignal, dataclasses-json, aiohttp, langchain\n", "Successfully installed aiohttp-3.8.4 aiosignal-1.3.1 async-timeout-4.0.2 dataclasses-json-0.5.7 frozenlist-1.3.3 langchain-0.0.163 marshmallow-3.19.0 marshmallow-enum-1.5.1 multidict-6.0.4 mypy-extensions-1.0.0 openapi-schema-pydantic-1.2.4 typing-inspect-0.8.0 yarl-1.9.2\n", "Looking in indexes: https://pypi.org/simple, https://us-python.pkg.dev/colab-wheels/public/simple/\n", "Collecting openai\n", "  Downloading openai-0.27.6-py3-none-any.whl (71 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m71.9/71.9 kB\u001b[0m \u001b[31m4.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: requests>=2.20 in /usr/local/lib/python3.10/dist-packages (from openai) (2.27.1)\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from openai) (4.65.0)\n", "Requirement already satisfied: aiohttp in /usr/local/lib/python3.10/dist-packages (from openai) (3.8.4)\n", "Requirement already satisfied: urllib3<1.27,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests>=2.20->openai) (1.26.15)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests>=2.20->openai) (2022.12.7)\n", "Requirement already satisfied: charset-normalizer~=2.0.0 in /usr/local/lib/python3.10/dist-packages (from requests>=2.20->openai) (2.0.12)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests>=2.20->openai) (3.4)\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai) (23.1.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai) (6.0.4)\n", "Requirement already satisfied: async-timeout<5.0,>=4.0.0a3 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai) (4.0.2)\n", "Requirement already satisfied: yarl<2.0,>=1.0 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai) (1.9.2)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai) (1.3.3)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.10/dist-packages (from aiohttp->openai) (1.3.1)\n", "Installing collected packages: openai\n", "Successfully installed openai-0.27.6\n", "Looking in indexes: https://pypi.org/simple, https://us-python.pkg.dev/colab-wheels/public/simple/\n", "Collecting google-search-results\n", "  Downloading google_search_results-2.4.2.tar.gz (18 kB)\n", "  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from google-search-results) (2.27.1)\n", "Requirement already satisfied: urllib3<1.27,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->google-search-results) (1.26.15)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->google-search-results) (2022.12.7)\n", "Requirement already satisfied: charset-normalizer~=2.0.0 in /usr/local/lib/python3.10/dist-packages (from requests->google-search-results) (2.0.12)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->google-search-results) (3.4)\n", "Building wheels for collected packages: google-search-results\n", "  Building wheel for google-search-results (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for google-search-results: filename=google_search_results-2.4.2-py3-none-any.whl size=32002 sha256=9aff49327108586304bd67365fe3897492d7436c1d09f6b5bb9af53648df53b2\n", "  Stored in directory: /root/.cache/pip/wheels/d3/b2/c3/03302d12bb44a2cdff3c9371f31b72c0c4e84b8d2285eeac53\n", "Successfully built google-search-results\n", "Installing collected packages: google-search-results\n", "Successfully installed google-search-results-2.4.2\n", "Looking in indexes: https://pypi.org/simple, https://us-python.pkg.dev/colab-wheels/public/simple/\n", "Collecting unstructured\n", "  Downloading unstructured-0.6.5-py3-none-any.whl (1.3 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.3/1.3 MB\u001b[0m \u001b[31m36.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting argilla (from unstructured)\n", "  Downloading argilla-1.7.0-py3-none-any.whl (2.2 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.2/2.2 MB\u001b[0m \u001b[31m62.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: lxml in /usr/local/lib/python3.10/dist-packages (from unstructured) (4.9.2)\n", "Collecting msg-parser (from unstructured)\n", "  Downloading msg_parser-1.2.0-py2.py3-none-any.whl (101 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m101.8/101.8 kB\u001b[0m \u001b[31m8.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: nltk in /usr/local/lib/python3.10/dist-packages (from unstructured) (3.8.1)\n", "Requirement already satisfied: openpyxl in /usr/local/lib/python3.10/dist-packages (from unstructured) (3.0.10)\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.10/dist-packages (from unstructured) (1.5.3)\n", "Collecting pdfminer.six (from unstructured)\n", "  Downloading pdfminer.six-20221105-py3-none-any.whl (5.6 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m5.6/5.6 MB\u001b[0m \u001b[31m69.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pillow in /usr/local/lib/python3.10/dist-packages (from unstructured) (8.4.0)\n", "Collecting pypandoc (from unstructured)\n", "  Downloading pypandoc-1.11-py3-none-any.whl (20 kB)\n", "Collecting python-docx (from unstructured)\n", "  Downloading python-docx-0.8.11.tar.gz (5.6 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m5.6/5.6 MB\u001b[0m \u001b[31m68.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Collecting python-pptx (from unstructured)\n", "  Downloading python-pptx-0.6.21.tar.gz (10.1 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m10.1/10.1 MB\u001b[0m \u001b[31m99.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Collecting python-magic (from unstructured)\n", "  Downloading python_magic-0.4.27-py2.py3-none-any.whl (13 kB)\n", "Requirement already satisfied: markdown in /usr/local/lib/python3.10/dist-packages (from unstructured) (3.4.3)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from unstructured) (2.27.1)\n", "Requirement already satisfied: certifi>=2022.12.07 in /usr/local/lib/python3.10/dist-packages (from unstructured) (2022.12.7)\n", "Collecting httpx<0.24,>=0.15 (from argilla->unstructured)\n", "  Downloading httpx-0.23.3-py3-none-any.whl (71 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m71.5/71.5 kB\u001b[0m \u001b[31m8.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting deprecated~=1.2.0 (from argilla->unstructured)\n", "  Downloading Deprecated-1.2.13-py2.py3-none-any.whl (9.6 kB)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.10/dist-packages (from argilla->unstructured) (23.1)\n", "Requirement already satisfied: pydantic>=1.7.1 in /usr/local/lib/python3.10/dist-packages (from argilla->unstructured) (1.10.7)\n", "Requirement already satisfied: wrapt<1.15,>=1.13 in /usr/local/lib/python3.10/dist-packages (from argilla->unstructured) (1.14.1)\n", "Requirement already satisfied: numpy<1.24.0 in /usr/local/lib/python3.10/dist-packages (from argilla->unstructured) (1.22.4)\n", "Requirement already satisfied: tqdm>=4.27.0 in /usr/local/lib/python3.10/dist-packages (from argilla->unstructured) (4.65.0)\n", "Collecting backoff (from argilla->unstructured)\n", "  Downloading backoff-2.2.1-py3-none-any.whl (15 kB)\n", "Collecting monotonic (from argilla->unstructured)\n", "  Downloading monotonic-1.6-py2.py3-none-any.whl (8.2 kB)\n", "Collecting rich<=13.0.1 (from argilla->unstructured)\n", "  Downloading rich-13.0.1-py3-none-any.whl (238 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m238.1/238.1 kB\u001b[0m \u001b[31m25.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: typer<1.0.0,>=0.6.0 in /usr/local/lib/python3.10/dist-packages (from argilla->unstructured) (0.7.0)\n", "Requirement already satisfied: python-dateutil>=2.8.1 in /usr/local/lib/python3.10/dist-packages (from pandas->unstructured) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.10/dist-packages (from pandas->unstructured) (2022.7.1)\n", "Collecting olefile>=0.46 (from msg-parser->unstructured)\n", "  Downloading olefile-0.46.zip (112 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m112.2/112.2 kB\u001b[0m \u001b[31m13.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: click in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured) (8.1.3)\n", "Requirement already satisfied: joblib in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured) (1.2.0)\n", "Requirement already satisfied: regex>=2021.8.3 in /usr/local/lib/python3.10/dist-packages (from nltk->unstructured) (2022.10.31)\n", "Requirement already satisfied: et-xmlfile in /usr/local/lib/python3.10/dist-packages (from openpyxl->unstructured) (1.1.0)\n", "Requirement already satisfied: charset-normalizer>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from pdfminer.six->unstructured) (2.0.12)\n", "Requirement already satisfied: cryptography>=36.0.0 in /usr/local/lib/python3.10/dist-packages (from pdfminer.six->unstructured) (40.0.2)\n", "Collecting XlsxWriter>=0.5.7 (from python-pptx->unstructured)\n", "  Downloading XlsxWriter-3.1.0-py3-none-any.whl (152 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m152.7/152.7 kB\u001b[0m \u001b[31m18.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: urllib3<1.27,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured) (1.26.15)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->unstructured) (3.4)\n", "Requirement already satisfied: cffi>=1.12 in /usr/local/lib/python3.10/dist-packages (from cryptography>=36.0.0->pdfminer.six->unstructured) (1.15.1)\n", "Collecting httpcore<0.17.0,>=0.15.0 (from httpx<0.24,>=0.15->argilla->unstructured)\n", "  Downloading httpcore-0.16.3-py3-none-any.whl (69 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m69.6/69.6 kB\u001b[0m \u001b[31m8.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting rfc3986[idna2008]<2,>=1.3 (from httpx<0.24,>=0.15->argilla->unstructured)\n", "  Downloading rfc3986-1.5.0-py2.py3-none-any.whl (31 kB)\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.10/dist-packages (from httpx<0.24,>=0.15->argilla->unstructured) (1.3.0)\n", "Requirement already satisfied: typing-extensions>=4.2.0 in /usr/local/lib/python3.10/dist-packages (from pydantic>=1.7.1->argilla->unstructured) (4.5.0)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.8.1->pandas->unstructured) (1.16.0)\n", "Collecting commonmark<0.10.0,>=0.9.0 (from rich<=13.0.1->argilla->unstructured)\n", "  Downloading commonmark-0.9.1-py2.py3-none-any.whl (51 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m51.1/51.1 kB\u001b[0m \u001b[31m6.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pygments<3.0.0,>=2.6.0 in /usr/local/lib/python3.10/dist-packages (from rich<=13.0.1->argilla->unstructured) (2.14.0)\n", "Requirement already satisfied: pycparser in /usr/local/lib/python3.10/dist-packages (from cffi>=1.12->cryptography>=36.0.0->pdfminer.six->unstructured) (2.21)\n", "Collecting h11<0.15,>=0.13 (from httpcore<0.17.0,>=0.15.0->httpx<0.24,>=0.15->argilla->unstructured)\n", "  Downloading h11-0.14.0-py3-none-any.whl (58 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m7.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: anyio<5.0,>=3.0 in /usr/local/lib/python3.10/dist-packages (from httpcore<0.17.0,>=0.15.0->httpx<0.24,>=0.15->argilla->unstructured) (3.6.2)\n", "Building wheels for collected packages: python-docx, python-pptx, olefile\n", "  Building wheel for python-docx (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for python-docx: filename=python_docx-0.8.11-py3-none-any.whl size=184491 sha256=7e4b7996e6cb62130f6bb857fc048ffb008b5e110f0a809c15ec1fbb53805405\n", "  Stored in directory: /root/.cache/pip/wheels/80/27/06/837436d4c3bd989b957a91679966f207bfd71d358d63a8194d\n", "  Building wheel for python-pptx (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for python-pptx: filename=python_pptx-0.6.21-py3-none-any.whl size=470935 sha256=de4a505e652d42d07194e6690fd7f1b3fef4f99ea9d3b0c9225b90ee98ee6f78\n", "  Stored in directory: /root/.cache/pip/wheels/ea/dd/74/01b3ec7256a0800b99384e9a0f7620e358afc3a51a59bf9b49\n", "  Building wheel for olefile (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for olefile: filename=olefile-0.46-py2.py3-none-any.whl size=35417 sha256=09c351f5b1e5b0c44cc7c395f60e850fd8c14107656e15b4318fc692bada38f9\n", "  Stored in directory: /root/.cache/pip/wheels/02/39/c0/9eb1f7a42b4b38f6f333b6314d4ed11c46f12a0f7b78194f0d\n", "Successfully built python-docx python-pptx olefile\n", "Installing collected packages: rfc3986, monotonic, commonmark, XlsxWriter, rich, python-magic, python-docx, pypandoc, olefile, h11, deprecated, backoff, python-pptx, msg-parser, httpcore, pdfminer.six, httpx, argilla, unstructured\n", "  Attempting uninstall: rich\n", "    Found existing installation: rich 13.3.4\n", "    Uninstalling rich-13.3.4:\n", "      Successfully uninstalled rich-13.3.4\n", "Successfully installed XlsxWriter-3.1.0 argilla-1.7.0 backoff-2.2.1 commonmark-0.9.1 deprecated-1.2.13 h11-0.14.0 httpcore-0.16.3 httpx-0.23.3 monotonic-1.6 msg-parser-1.2.0 olefile-0.46 pdfminer.six-20221105 pypandoc-1.11 python-docx-0.8.11 python-magic-0.4.27 python-pptx-0.6.21 rfc3986-1.5.0 rich-13.0.1 unstructured-0.6.5\n", "Looking in indexes: https://pypi.org/simple, https://us-python.pkg.dev/colab-wheels/public/simple/\n", "Collecting chromadb\n", "  Downloading chromadb-0.3.22-py3-none-any.whl (69 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m69.2/69.2 kB\u001b[0m \u001b[31m5.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pandas>=1.3 in /usr/local/lib/python3.10/dist-packages (from chromadb) (1.5.3)\n", "Collecting requests>=2.28 (from chromadb)\n", "  Downloading requests-2.30.0-py3-none-any.whl (62 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m62.5/62.5 kB\u001b[0m \u001b[31m7.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: pydantic>=1.9 in /usr/local/lib/python3.10/dist-packages (from chromadb) (1.10.7)\n", "Collecting hnswlib>=0.7 (from chromadb)\n", "  Downloading hnswlib-0.7.0.tar.gz (33 kB)\n", "  Installing build dependencies ... \u001b[?25l\u001b[?25hdone\n", "  Getting requirements to build wheel ... \u001b[?25l\u001b[?25hdone\n", "  Preparing metadata (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "Collecting clickhouse-connect>=0.5.7 (from chromadb)\n", "  Downloading clickhouse_connect-0.5.23-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (922 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m922.4/922.4 kB\u001b[0m \u001b[31m56.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting sentence-transformers>=2.2.2 (from chromadb)\n", "  Downloading sentence-transformers-2.2.2.tar.gz (85 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.0/86.0 kB\u001b[0m \u001b[31m10.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Requirement already satisfied: duckdb>=0.7.1 in /usr/local/lib/python3.10/dist-packages (from chromadb) (0.7.1)\n", "Collecting fastapi>=0.85.1 (from chromadb)\n", "  Downloading fastapi-0.95.1-py3-none-any.whl (56 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m57.0/57.0 kB\u001b[0m \u001b[31m6.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting uvicorn[standard]>=0.18.3 (from chromadb)\n", "  Downloading uvicorn-0.22.0-py3-none-any.whl (58 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m58.3/58.3 kB\u001b[0m \u001b[31m6.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: numpy>=1.21.6 in /usr/local/lib/python3.10/dist-packages (from chromadb) (1.22.4)\n", "Collecting posthog>=2.4.0 (from chromadb)\n", "  Downloading posthog-3.0.1-py2.py3-none-any.whl (37 kB)\n", "Requirement already satisfied: typing-extensions>=4.5.0 in /usr/local/lib/python3.10/dist-packages (from chromadb) (4.5.0)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.10/dist-packages (from clickhouse-connect>=0.5.7->chromadb) (2022.12.7)\n", "Requirement already satisfied: urllib3>=1.26 in /usr/local/lib/python3.10/dist-packages (from clickhouse-connect>=0.5.7->chromadb) (1.26.15)\n", "Requirement already satisfied: pytz in /usr/local/lib/python3.10/dist-packages (from clickhouse-connect>=0.5.7->chromadb) (2022.7.1)\n", "Collecting zstandard (from clickhouse-connect>=0.5.7->chromadb)\n", "  Downloading zstandard-0.21.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.7 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.7/2.7 MB\u001b[0m \u001b[31m85.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting lz4 (from clickhouse-connect>=0.5.7->chromadb)\n", "  Downloading lz4-4.3.2-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.3 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.3/1.3 MB\u001b[0m \u001b[31m66.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting starlette<0.27.0,>=0.26.1 (from fastapi>=0.85.1->chromadb)\n", "  Downloading starlette-0.26.1-py3-none-any.whl (66 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m66.9/66.9 kB\u001b[0m \u001b[31m8.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: python-dateutil>=2.8.1 in /usr/local/lib/python3.10/dist-packages (from pandas>=1.3->chromadb) (2.8.2)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from posthog>=2.4.0->chromadb) (1.16.0)\n", "Requirement already satisfied: monotonic>=1.5 in /usr/local/lib/python3.10/dist-packages (from posthog>=2.4.0->chromadb) (1.6)\n", "Requirement already satisfied: backoff>=1.10.0 in /usr/local/lib/python3.10/dist-packages (from posthog>=2.4.0->chromadb) (2.2.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.28->chromadb) (2.0.12)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests>=2.28->chromadb) (3.4)\n", "Collecting transformers<5.0.0,>=4.6.0 (from sentence-transformers>=2.2.2->chromadb)\n", "  Downloading transformers-4.28.1-py3-none-any.whl (7.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m7.0/7.0 MB\u001b[0m \u001b[31m89.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: tqdm in /usr/local/lib/python3.10/dist-packages (from sentence-transformers>=2.2.2->chromadb) (4.65.0)\n", "Requirement already satisfied: torch>=1.6.0 in /usr/local/lib/python3.10/dist-packages (from sentence-transformers>=2.2.2->chromadb) (2.0.0+cu118)\n", "Requirement already satisfied: torchvision in /usr/local/lib/python3.10/dist-packages (from sentence-transformers>=2.2.2->chromadb) (0.15.1+cu118)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.10/dist-packages (from sentence-transformers>=2.2.2->chromadb) (1.2.2)\n", "Requirement already satisfied: scipy in /usr/local/lib/python3.10/dist-packages (from sentence-transformers>=2.2.2->chromadb) (1.10.1)\n", "Requirement already satisfied: nltk in /usr/local/lib/python3.10/dist-packages (from sentence-transformers>=2.2.2->chromadb) (3.8.1)\n", "Collecting sentencepiece (from sentence-transformers>=2.2.2->chromadb)\n", "  Downloading sentencepiece-0.1.99-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.3 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.3/1.3 MB\u001b[0m \u001b[31m61.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting huggingface-hub>=0.4.0 (from sentence-transformers>=2.2.2->chromadb)\n", "  Downloading huggingface_hub-0.14.1-py3-none-any.whl (224 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m224.5/224.5 kB\u001b[0m \u001b[31m23.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: click>=7.0 in /usr/local/lib/python3.10/dist-packages (from uvicorn[standard]>=0.18.3->chromadb) (8.1.3)\n", "Requirement already satisfied: h11>=0.8 in /usr/local/lib/python3.10/dist-packages (from uvicorn[standard]>=0.18.3->chromadb) (0.14.0)\n", "Collecting httptools>=0.5.0 (from uvicorn[standard]>=0.18.3->chromadb)\n", "  Downloading httptools-0.5.0-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (414 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m414.1/414.1 kB\u001b[0m \u001b[31m37.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting python-dotenv>=0.13 (from uvicorn[standard]>=0.18.3->chromadb)\n", "  Downloading python_dotenv-1.0.0-py3-none-any.whl (19 kB)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.10/dist-packages (from uvicorn[standard]>=0.18.3->chromadb) (6.0)\n", "Collecting uvloop!=0.15.0,!=0.15.1,>=0.14.0 (from uvicorn[standard]>=0.18.3->chromadb)\n", "  Downloading uvloop-0.17.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (4.1 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4.1/4.1 MB\u001b[0m \u001b[31m65.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting watchfiles>=0.13 (from uvicorn[standard]>=0.18.3->chromadb)\n", "  Downloading watchfiles-0.19.0-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (1.3 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.3/1.3 MB\u001b[0m \u001b[31m51.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting websockets>=10.4 (from uvicorn[standard]>=0.18.3->chromadb)\n", "  Downloading websockets-11.0.3-cp310-cp310-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (129 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m129.9/129.9 kB\u001b[0m \u001b[31m14.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: filelock in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.4.0->sentence-transformers>=2.2.2->chromadb) (3.12.0)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.4.0->sentence-transformers>=2.2.2->chromadb) (2023.4.0)\n", "Requirement already satisfied: packaging>=20.9 in /usr/local/lib/python3.10/dist-packages (from huggingface-hub>=0.4.0->sentence-transformers>=2.2.2->chromadb) (23.1)\n", "Requirement already satisfied: anyio<5,>=3.4.0 in /usr/local/lib/python3.10/dist-packages (from starlette<0.27.0,>=0.26.1->fastapi>=0.85.1->chromadb) (3.6.2)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.10/dist-packages (from torch>=1.6.0->sentence-transformers>=2.2.2->chromadb) (1.11.1)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.10/dist-packages (from torch>=1.6.0->sentence-transformers>=2.2.2->chromadb) (3.1)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.10/dist-packages (from torch>=1.6.0->sentence-transformers>=2.2.2->chromadb) (3.1.2)\n", "Requirement already satisfied: triton==2.0.0 in /usr/local/lib/python3.10/dist-packages (from torch>=1.6.0->sentence-transformers>=2.2.2->chromadb) (2.0.0)\n", "Requirement already satisfied: cmake in /usr/local/lib/python3.10/dist-packages (from triton==2.0.0->torch>=1.6.0->sentence-transformers>=2.2.2->chromadb) (3.25.2)\n", "Requirement already satisfied: lit in /usr/local/lib/python3.10/dist-packages (from triton==2.0.0->torch>=1.6.0->sentence-transformers>=2.2.2->chromadb) (16.0.3)\n", "Requirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.10/dist-packages (from transformers<5.0.0,>=4.6.0->sentence-transformers>=2.2.2->chromadb) (2022.10.31)\n", "Collecting tokenizers!=0.11.3,<0.14,>=0.11.1 (from transformers<5.0.0,>=4.6.0->sentence-transformers>=2.2.2->chromadb)\n", "  Downloading tokenizers-0.13.3-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (7.8 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m7.8/7.8 MB\u001b[0m \u001b[31m63.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: joblib in /usr/local/lib/python3.10/dist-packages (from nltk->sentence-transformers>=2.2.2->chromadb) (1.2.0)\n", "Requirement already satisfied: threadpoolctl>=2.0.0 in /usr/local/lib/python3.10/dist-packages (from scikit-learn->sentence-transformers>=2.2.2->chromadb) (3.1.0)\n", "Requirement already satisfied: pillow!=8.3.*,>=5.3.0 in /usr/local/lib/python3.10/dist-packages (from torchvision->sentence-transformers>=2.2.2->chromadb) (8.4.0)\n", "Requirement already satisfied: sniffio>=1.1 in /usr/local/lib/python3.10/dist-packages (from anyio<5,>=3.4.0->starlette<0.27.0,>=0.26.1->fastapi>=0.85.1->chromadb) (1.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.10/dist-packages (from jinja2->torch>=1.6.0->sentence-transformers>=2.2.2->chromadb) (2.1.2)\n", "Requirement already satisfied: mpmath>=0.19 in /usr/local/lib/python3.10/dist-packages (from sympy->torch>=1.6.0->sentence-transformers>=2.2.2->chromadb) (1.3.0)\n", "Building wheels for collected packages: hnswlib, sentence-transformers\n", "  Building wheel for hnswlib (pyproject.toml) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for hnswlib: filename=hnswlib-0.7.0-cp310-cp310-linux_x86_64.whl size=2119854 sha256=00361ca278f63761c51501d7c5f84b36c2bd3d1499d26515503255e11c5d4057\n", "  Stored in directory: /root/.cache/pip/wheels/8a/ae/ec/235a682e0041fbaeee389843670581ec6c66872db856dfa9a4\n", "  Building wheel for sentence-transformers (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "  Created wheel for sentence-transformers: filename=sentence_transformers-2.2.2-py3-none-any.whl size=125926 sha256=f23c29e8d68d088443c785bfcd6e53cc912b3a54964f10c7fef1a1084759c39a\n", "  Stored in directory: /root/.cache/pip/wheels/62/f2/10/1e606fd5f02395388f74e7462910fe851042f97238cbbd902f\n", "Successfully built hnswlib sentence-transformers\n", "Installing collected packages: tokenizers, sentencepiece, zstandard, websockets, uvloop, uvicorn, requests, python-dotenv, lz4, httptools, hnswlib, watchfiles, starlette, posthog, huggingface-hub, clickhouse-connect, transformers, fastapi, sentence-transformers, chromadb\n", "  Attempting uninstall: requests\n", "    Found existing installation: requests 2.27.1\n", "    Uninstalling requests-2.27.1:\n", "      Successfully uninstalled requests-2.27.1\n", "Successfully installed chromadb-0.3.22 clickhouse-connect-0.5.23 fastapi-0.95.1 hnswlib-0.7.0 httptools-0.5.0 huggingface-hub-0.14.1 lz4-4.3.2 posthog-3.0.1 python-dotenv-1.0.0 requests-2.30.0 sentence-transformers-2.2.2 sentencepiece-0.1.99 starlette-0.26.1 tokenizers-0.13.3 transformers-4.28.1 uvicorn-0.22.0 uvloop-0.17.0 watchfiles-0.19.0 websockets-11.0.3 zstandard-0.21.0\n", "Looking in indexes: https://pypi.org/simple, https://us-python.pkg.dev/colab-wheels/public/simple/\n", "Collecting pinecone-client\n", "  Downloading pinecone_client-2.2.1-py3-none-any.whl (177 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m177.2/177.2 kB\u001b[0m \u001b[31m7.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: requests>=2.19.0 in /usr/local/lib/python3.10/dist-packages (from pinecone-client) (2.30.0)\n", "Requirement already satisfied: pyyaml>=5.4 in /usr/local/lib/python3.10/dist-packages (from pinecone-client) (6.0)\n", "Collecting loguru>=0.5.0 (from pinecone-client)\n", "  Downloading loguru-0.7.0-py3-none-any.whl (59 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m60.0/60.0 kB\u001b[0m \u001b[31m1.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: typing-extensions>=3.7.4 in /usr/local/lib/python3.10/dist-packages (from pinecone-client) (4.5.0)\n", "Collecting dnspython>=2.0.0 (from pinecone-client)\n", "  Downloading dnspython-2.3.0-py3-none-any.whl (283 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m283.7/283.7 kB\u001b[0m \u001b[31m23.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: python-dateutil>=2.5.3 in /usr/local/lib/python3.10/dist-packages (from pinecone-client) (2.8.2)\n", "Requirement already satisfied: urllib3>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from pinecone-client) (1.26.15)\n", "Requirement already satisfied: tqdm>=4.64.1 in /usr/local/lib/python3.10/dist-packages (from pinecone-client) (4.65.0)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.10/dist-packages (from pinecone-client) (1.22.4)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.10/dist-packages (from python-dateutil>=2.5.3->pinecone-client) (1.16.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests>=2.19.0->pinecone-client) (2.0.12)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests>=2.19.0->pinecone-client) (3.4)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests>=2.19.0->pinecone-client) (2022.12.7)\n", "Installing collected packages: loguru, dnspython, pinecone-client\n", "Successfully installed dnspython-2.3.0 loguru-0.7.0 pinecone-client-2.2.1\n", "Looking in indexes: https://pypi.org/simple, https://us-python.pkg.dev/colab-wheels/public/simple/\n", "Collecting youtube-transcript-api\n", "  Downloading youtube_transcript_api-0.6.0-py3-none-any.whl (23 kB)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.10/dist-packages (from youtube-transcript-api) (2.30.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.10/dist-packages (from requests->youtube-transcript-api) (2.0.12)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.10/dist-packages (from requests->youtube-transcript-api) (3.4)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.10/dist-packages (from requests->youtube-transcript-api) (1.26.15)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.10/dist-packages (from requests->youtube-transcript-api) (2022.12.7)\n", "Installing collected packages: youtube-transcript-api\n", "Successfully installed youtube-transcript-api-0.6.0\n", "Looking in indexes: https://pypi.org/simple, https://us-python.pkg.dev/colab-wheels/public/simple/\n", "Collecting pytube\n", "  Downloading pytube-15.0.0-py3-none-any.whl (57 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m57.6/57.6 kB\u001b[0m \u001b[31m3.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: pytube\n", "Successfully installed pytube-15.0.0\n"]}], "source": ["!pip install langchain\n", "!pip install openai\n", "!pip install google-search-results\n", "!pip install unstructured\n", "!pip install chromadb\n", "!pip install pinecone-client\n", "!pip install youtube-transcript-api\n", "!pip install pytube"]}, {"cell_type": "code", "source": ["import os\n", "os.environ[\"OPENAI_API_KEY\"] = ''"], "metadata": {"id": "Kk01CyyA1GwX"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["#使用 LangChain 完成一次问答"], "metadata": {"id": "Ao78SDEp1Xbf"}}, {"cell_type": "code", "source": ["from langchain.llms import OpenAI\n", "\n", "llm = OpenAI(model_name=\"text-davinci-003\",max_tokens=1024)\n", "llm(\"怎么评价人工智能\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 54}, "id": "TSeOZmGJ1aL_", "outputId": "c1913fda-3416-4e93-b3e1-88eabb35684d"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["'\\n\\n人工智能是一个极具潜力的领域，它在推动着未来科技发展和社会进步。人工智能已经取得了一定的成就，但仍有很多需要改进的地方，其中包括提高技术的精确度和可靠性，提升人工智能的难度和复杂度，以及更好地理解人类的行为和心理。这些都将需要更多的研究和实践，才能实现人工智能的最终目标。'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 16}]}, {"cell_type": "markdown", "source": ["#通过 Google 搜索并返回答案"], "metadata": {"id": "j0-Zs6KZ6Dgi"}}, {"cell_type": "code", "source": ["import os\n", "os.environ[\"SERPAPI_API_KEY\"] = ''"], "metadata": {"id": "W3VCMBux6Iuq"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["from langchain.agents import load_tools\n", "from langchain.agents import initialize_agent\n", "from langchain.llms import OpenAI\n", "from langchain.agents import AgentType\n", "\n", "# 加载 OpenAI 模型\n", "llm = OpenAI(temperature=0,max_tokens=2048)\n", "\n", " # 加载 serpapi 工具\n", "tools = load_tools([\"serpapi\"])\n", "# 工具加载后都需要初始化，verbose 参数为 True，会打印全部的执行详情\n", "agent = initialize_agent(tools, llm, agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION, verbose=True)\n", "\n", "# 运行 agent\n", "agent.run(\"What's the date today? What great events have taken place today in history?\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 478}, "id": "GfyZF-aW6RJa", "outputId": "cb696980-98f9-4518-975a-2024370c3caa"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3m I should look up the current date and then search for historical events that have taken place on that day.\n", "Action: Search\n", "Action Input: Current date\u001b[0m\n", "Observation: \u001b[36;1m\u001b[1;3m<PERSON><PERSON><PERSON>'s current date and time with time zone and date picker: Select locale. en-US. Monday, April 3, 2023. 11:00:05 PM. PST8PDT (GMT-7).\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3m I should now search for historical events that have taken place on this day.\n", "Action: Search\n", "Action Input: Historical events April 3\u001b[0m\n", "Observation: \u001b[36;1m\u001b[1;3mNo good search result found\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3m I should narrow down my search to a specific year.\n", "Action: Search\n", "Action Input: Historical events April 3 2023\u001b[0m\n", "Observation: \u001b[36;1m\u001b[1;3mNo good search result found\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3m I should try searching for a specific event.\n", "Action: Search\n", "Action Input: Apollo 16 launch April 3 1972\u001b[0m\n", "Observation: \u001b[36;1m\u001b[1;3mThe fifth Moon landing mission began with the April 16, 1972 launch of Apollo 16. The giant Saturn V rocket lifted off from Launch Pad 39A at NASA's Kennedy Space Center (KSC) in Florida with Commander <PERSON>, Command Module Pilot <PERSON>, and Lunar Module Pilot <PERSON>\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3m I now know the answer to the original question.\n", "Final Answer: Today is Monday, April 3, 2023. On this day in 1972, the Apollo 16 mission was launched, the fifth Moon landing mission.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'Today is Monday, April 3, 2023. On this day in 1972, the Apollo 16 mission was launched, the fifth Moon landing mission.'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 21}]}, {"cell_type": "markdown", "source": ["#对超长文本进行总结"], "metadata": {"id": "L1nWOkGvCStj"}}, {"cell_type": "code", "source": ["from langchain.document_loaders import UnstructuredFileLoader\n", "from langchain.chains.summarize import load_summarize_chain\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain import OpenAI\n", "\n", "# 导入文本\n", "loader = UnstructuredFileLoader(\"/content/sample_data/data/lg_test.txt\")\n", "# 将文本转成 Document 对象\n", "document = loader.load()\n", "print(f'documents:{len(document)}')\n", "\n", "# 初始化文本分割器\n", "text_splitter = RecursiveCharacterTextSplitter(\n", "    chunk_size = 500,\n", "    chunk_overlap = 0\n", ")\n", "\n", "# 切分文本\n", "split_documents = text_splitter.split_documents(document)\n", "print(f'documents:{len(split_documents)}')\n", "\n", "# 加载 llm 模型\n", "llm = OpenAI(max_tokens=1500)\n", "\n", "# 创建总结链\n", "chain = load_summarize_chain(llm, chain_type=\"refine\", verbose=True)\n", "\n", "# 执行总结链，（为了快速演示，只总结前5段）\n", "chain.run(split_documents[:5])"], "metadata": {"id": "8WriJqBU6RDa", "colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "outputId": "b45cd864-a2fb-4c36-b19d-f53c04a9b3b6"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["documents:1\n", "documents:317\n", "\n", "\n", "\u001b[1m> Entering new RefineDocumentsChain chain...\u001b[0m\n", "\n", "\n", "\u001b[1m> Entering new LLMChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mWrite a concise summary of the following:\n", "\n", "\n", "\"声明：本书为爱奇电子书(www.i7wu.cn)的用户上传至其在本站的存储空间，本站只提供TXT全集电子书存储服务以及免费下载服务，以下作品内容之版权与本站无任何关系。\n", "\n", "--------------------------用户上传之内容开始--------------------------------\n", "\n", "《地藏心经》\n", "\n", "作者：铸剑师无名\n", "\n", "正文\n", "\n", "第一第十五章 天下势，渡江（一）\n", "\n", "“渝州陆家？！”\n", "\n", "虽然原本的那个秦逸，每日只知道苦读诗书，从未与商贾们打过交道，但是渝州陆家的名声，他还是知道。\n", "\n", "陆家三代为官，官至两江总督，五代经商，百年经营，家私何止千万，直至今朝，俨然已是江南一等士族大户。渝州陆氏以皮货起家，乃是西北之地数得上号的商户，西北之地所产的皮货，有三成经他们之手卖往江南。\n", "\n", "若只是如此，陆氏也不过是一头肥硕的羔羊，只待他人宰杀。\n", "\n", "陆氏三代家主都极具雄韬伟略，以千金买官，以万金开路，更是在蛮夷南侵之时，倾尽家资招兵买马，拒十万蛮夷铁骑于侯关外，短短三年间，便一手扶持起了都护大将军——苏和，抗夷大将军——邓昌。\"\n", "\n", "\n", "CONCISE SUMMARY:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\n", "\n", "\u001b[1m> Entering new LLMChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mYour job is to produce a final summary\n", "We have provided an existing summary up to a certain point: \n", "《地藏心经》是由铸剑师无名创作的作品，本书讲述了渝州陆家三代家主的传奇故事，他们以皮货起家，在蛮夷南侵时招兵买马，一手扶持起了都护大将军和抗夷大将军，让渝州陆家走向了巅峰。\n", "We have the opportunity to refine the existing summary(only if needed) with some more context below.\n", "------------\n", "以姻亲握住兵权后，陆氏子弟一路仕途平坦，百年来，人才辈出，更有陆云，陆羽等良将贤才。\n", "\n", "而今，已是雄踞渝、豫两地的世家阀门，这江南数万水军，便是掌握在这一代的陆家族长手中。\n", "\n", "朝廷无权，皇帝无兵，短短十年，南朝便形同虚设，各地封疆大使，世家阀门手握重兵，除了京都三省还在南朝皇族手中，其他俨然已经分地而治。\n", "\n", "西北，邓、李、苏、何、公孙五家世家阀门割据一方，联手共抗蛮夷合并后的金国。\n", "\n", "南方，陆、熊、刘、郑四家百年士族据守江南，与中山国相持已然数十载。\n", "\n", "东方，京都三省雄兵三十万，黑甲铁骑八千，时刻防范着秦国有所异动。（备注：黑甲铁骑配备长枪，马刀，黑铁重甲，所乘骑的乃是西域宛马，是南朝立国时便赫赫有名的百战铁骑。曾以八千黑甲铁骑破中山国十万雄兵而名动天下。）\n", "\n", "这些，便是张狂融合完原本那个‘秦逸’的记忆，而整理出的天下大势。\n", "\n", "*************************************************************************\n", "\n", "“少爷。这船都被陆家车行的人包下了。”\n", "------------\n", "Given the new context, refine the original summaryIf the context isn't useful, return the original summary.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\n", "\n", "\u001b[1m> Entering new LLMChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mYour job is to produce a final summary\n", "We have provided an existing summary up to a certain point: \n", "《地藏心经》是由铸剑师无名创作的作品，本书讲述了渝州陆家三代家主的传奇故事，他们以皮货起家，在蛮夷南侵时招兵买马，一手扶持起了都护大将军和抗夷大将军，姻亲握住兵权，让渝州陆家一路仕途平坦，百年来，人才辈出，他们雄踞渝、豫两地，掌握在这一代的陆家族长手中，控制着江南数万水军，朝廷无权，皇帝无兵，西北世家阀门割据一方，南方百年士族据守，东方京都三省雄兵三十万，最终让渝州陆家走向了巅峰。\n", "We have the opportunity to refine the existing summary(only if needed) with some more context below.\n", "------------\n", "不过一会儿，秦汉便略显沮丧地走了回来。渝州陆家势大，而今就连附属下面的陆家车行，身份也是水涨船高。自从秦逸父亲病逝后，秦家家道中落，与陆家比不得，况且此地也并非西北所属，秦家纵然还有些人脉，却也用不上。\n", "\n", "所以，为了避免麻烦，他也没敢去与陆家争船。\n", "\n", "“嗯。”秦逸默然，脸色平静，对着秦汉点点头，也未多说些什么。虽然他心中也想早点赶往渝州，在年关前，布置些家业，好早些安定下来。“我知道了。”\n", "\n", "“敢问公子贵姓？”\n", "\n", "这时，秦逸身旁的中年商人，突然出口问道。原来他见秦逸，wｗ W.l6Ｋ .cN面容俊逸，又是一身锦衣华服，虽然风尘仆仆，但是谈吐举止中，无一不带着士族风范，不由得起了巴结之心。\n", "\n", "南朝商人地位虽然要略高于前朝列代，但是依旧排在最后。“士农工商”，商人自古就有着“不劳而获”之名。\n", "\n", "“姓秦。”秦逸面色淡然，转头看了中年商人一眼，出声道。\n", "\n", "他来于后世，对商人并无轻视之意，所以也没有摆什么士族的架子。\n", "\n", "中年商人闻言微微一愣，随即动容，隐隐带着喜悦，他躬腰低头，对着秦逸恭恭敬敬地行了一个大礼，而后出声询问道：“敢问可是晋中秦家？！”\n", "------------\n", "Given the new context, refine the original summaryIf the context isn't useful, return the original summary.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\n", "\n", "\u001b[1m> Entering new LLMChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mYour job is to produce a final summary\n", "We have provided an existing summary up to a certain point: \n", "《地藏心经》是由铸剑师无名创作的作品，本书讲述了渝州陆家三代家主的传奇故事，他们以皮货起家，在蛮夷南侵时招兵买马，一手扶持起了都护大将军和抗夷大将军，姻亲握住兵权，让渝州陆家一路仕途平坦，百年来，人才辈出，他们雄踞渝、豫两地，掌握在这一代的陆家族长手中，控制着江南数万水军，朝廷无权，皇帝无兵，西北世家阀门割据一方，南方百年士族据守，东方京都三省雄兵三十万，最终让渝州陆家走向了巅峰，在外人看来，他们尊贵至极，但在商人眼中，他们也是不可小觑的贵族。\n", "We have the opportunity to refine the existing summary(only if needed) with some more context below.\n", "------------\n", "“正是！”说话的确是秦汉，秦家在西北之地声名远播，善名百里，虽然手中无兵无权，但是在西北士族中还是举足轻重，俨然已成精神领袖。\n", "\n", "“敢问，可是秦逸公子？！”中年商人对着秦逸又是一个大礼，声音颇为颤抖地说道。此番回程，他便听说了秦家少爷要前往渝州，却想不到自己居然正好遇上！\n", "\n", "“五代行善，何其不易！夫天下之人，独晋中秦家也！”……\n", "\n", "秦家善名，至今已然百年有余。\n", "\n", "“嗯。”秦逸点头，并未多说。一路行来，他已经陆续感受到了秦家在这个世界上的声望。\n", "\n", "一世行善容易，但是五代行善，中原数千年来，独此一家。就连数十年前，蛮夷赫连氏族入侵中原，都刻意避开了晋中秦家。在草原蛮族的教义中，屠戮真正的善人，会被狼神抛弃，灵魂永世不得安息。\n", "\n", "***********************************************************************\n", "\n", "就在秦逸准备寻一处清净地，安安静静的等待陆家车行的人先走时，远处，一团人簇拥着一个青衫老者往这边走来。而为首的，正是昨日在路上遇到的那个满脸扎须的壮年汉子。\n", "------------\n", "Given the new context, refine the original summaryIf the context isn't useful, return the original summary.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\n", "\n", "\u001b[1m> Entering new LLMChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mYour job is to produce a final summary\n", "We have provided an existing summary up to a certain point: \n", "《地藏心经》是由铸剑师无名创作的作品，本书讲述了渝州陆家三代家主的传奇故事，他们以皮货起家，在蛮夷南侵时招兵买马，一手扶持起了都护大将军和抗夷大将军，姻亲握住兵权，让渝州陆家一路仕途平坦，百年来，人才辈出，他们雄踞渝、豫两地，掌握在这一代的陆家族长手中，控制着江南数万水军，朝廷无权，皇帝无兵，西北世家阀门割据一方，南方百年士族据守，东方京都三省雄兵三十万，最终让渝州陆家走向了巅峰，在外人看来，他们尊贵至极，但在商人眼中，他们也是不可小觑的贵族。秦家在西北之地声名远播，善名百里，虽然手中无兵无权，但是在西北士族中还是举足轻重，俨然已成精神领袖。秦家善名，至今已然百年有余，一世行善容易，但是五代行善，中原数千年来，独此一家，就连数十年前，蛮夷赫连氏族入侵中原，都刻意避开了晋中秦家，在草原蛮族的教义中，屠戮真正的善人，会被狼神抛弃，灵魂永世不得安息。\n", "\n", "《地藏心经》讲述了渝州陆家三代家主的传奇故事，他们以皮货起家，招兵买马，扶持起都护大将军和抗夷大将军，握住兵权，一路仕途平坦，百年来，人才辈出，雄踞渝、豫两地，掌握江南数万水军，走向巅峰，尊贵至极，不可小觑的贵族。秦家在西北之地声名远播，善名百里，五代行善，在中原数千年来，独此一家，就连蛮夷赫连氏族入侵中原时，都刻意避开了晋中秦家，屠戮真正善人，会被狼神抛弃。\n", "We have the opportunity to refine the existing summary(only if needed) with some more context below.\n", "------------\n", "“那便是陆家车行的管事。”一旁的中年商人适时的报出了那位青衫老者的身份。\n", "\n", "“陆氏车行？管事？”\n", "\n", "秦逸眉头一挑，不由得心头一动。若是等到陆家车行货物运完，这一来一去，天怕是已经摸黑了，想来渡江只能等到明晚。既然面前，就是陆家车行的管事，何不找他试试，看看能不能一并登船渡江。\n", "\n", "想到这，秦逸略微整了整衣衫，脸上挂着一副淡定的笑容，迎了上去。\n", "\n", "“长者有礼了！”秦逸走到人群前，对着为首的青衫老者微微一拱手，行礼道。\n", "\n", "“你他……”眼前突然多出了一个人来，宋老虎不由一愣，他心底正火着呢~。对于这个突然蹦出来的拦路者，他可是一点好感都没有。可是待他看清来人的模样，又不由微微一愣神。面容俊逸，星目剑眉，身形略显消瘦，一身褐色长衫，虽然沾染风尘，可是也遮不住那一骨子的儒雅正气！\n", "\n", "“是个读书人。”宋老虎心中不由的想到。他与其他的武林中人不同，他这个人佩服读书人，当然，是那种一身正气的读书人。\n", "\n", "所以，他在看清楚秦逸的模样后，又不由的将最后那个“妈”字，生生地咽了下去。\n", "\n", "青衫老者闻言一愣，待他抬头看清秦逸的模样，觉察出他身上那一股子士族风范后，这才拱手回礼道：“公子何事？”\n", "------------\n", "Given the new context, refine the original summaryIf the context isn't useful, return the original summary.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["'\\n《地藏心经》讲述了渝州陆家三代家主的传奇故事，他们以皮货起家，招兵买马，扶持起都护大将军和抗夷大将军，握住兵权，一路仕途平坦，百年来，人才辈出，雄踞渝、豫两地，掌握江南数万水军，走向巅峰，尊贵至极，不可小觑的贵族。秦家在西北之地声名远播，善名百里，五代行善，在中原数千年来，独此一家，就连蛮夷赫连氏族入侵中原时，都刻意避开了晋中秦家，屠戮真正善人，会被狼神抛弃。陆家车行也声名远扬，由管事宋老虎管理，秦逸受其邀请，一同登船渡江，令渝州陆家走向了巅峰，并受到众多贵族的尊敬。'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 24}]}, {"cell_type": "markdown", "source": ["#构建本地知识库问答机器人"], "metadata": {"id": "NySCEE0-tKw-"}}, {"cell_type": "code", "source": ["from langchain.embeddings.openai import OpenAIEmbeddings\n", "from langchain.vectorstores import Chroma\n", "from langchain.text_splitter import CharacterTextSplitter\n", "from langchain import OpenAI,VectorDBQA\n", "from langchain.document_loaders import DirectoryLoader\n", "from langchain.chains import RetrievalQA\n", "\n", "# 加载文件夹中的所有txt类型的文件\n", "loader = DirectoryLoader('/content/sample_data/data/', glob='**/*.txt')\n", "# 将数据转成 document 对象，每个文件会作为一个 document\n", "documents = loader.load()\n", "\n", "# 初始化加载器\n", "text_splitter = CharacterTextSplitter(chunk_size=100, chunk_overlap=0)\n", "# 切割加载的 document\n", "split_docs = text_splitter.split_documents(documents)\n", "\n", "# 初始化 openai 的 embeddings 对象\n", "embeddings = OpenAIEmbeddings()\n", "# 将 document 通过 openai 的 embeddings 对象计算 embedding向量信息并临时存入 Chroma 向量数据库，用于后续匹配查询\n", "docsearch = Chroma.from_documents(split_docs, embeddings)\n", "\n", "# 创建问答对象\n", "qa = VectorDBQA.from_chain_type(llm=OpenAI(), chain_type=\"stuff\", vectorstore=docsearch,return_source_documents=True)\n", "# 进行问答\n", "result = qa({\"query\": \"科大讯飞今年第一季度收入是多少？\"})\n", "print(result)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lGoTRXA5tUbW", "outputId": "b9523b8e-b170-4dd7-ef77-a04b07b0e66c"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["WARNING:root:Created a chunk of size 122, which is longer than the specified 100\n", "WARNING:root:Created a chunk of size 216, which is longer than the specified 100\n", "WARNING:root:Created a chunk of size 118, which is longer than the specified 100\n", "WARNING:root:Created a chunk of size 122, which is longer than the specified 100\n", "WARNING:root:Created a chunk of size 229, which is longer than the specified 100\n", "WARNING:root:Created a chunk of size 134, which is longer than the specified 100\n", "WARNING:root:Created a chunk of size 121, which is longer than the specified 100\n", "WARNING:root:Created a chunk of size 202, which is longer than the specified 100\n", "WARNING:chromadb:Using embedded DuckDB without persistence: data will be transient\n"]}, {"output_type": "stream", "name": "stdout", "text": ["{'query': '科大讯飞今年第一季度收入是多少？', 'result': ' 根据报告，科大讯飞今年第一季度的收入为126.5亿人民币。', 'source_documents': [Document(page_content='虽然收入增加了不少，但在这一点上，科大讯飞的高层也坦言，与过去几个财季相比，第一个财季的收入增加速度明显慢了下来，这其中最重要的一个因素，便是由于第一个财季受到了病毒的冲击，导致了国内不少工程进度推迟。在讯飞的智能教育商业计划中，第三季度有10个左右的延期，合约金额接近16个。另外，安徽省方面，为强化对省内规划工程的协调，在第三季度内，大部分大型工程的投标都已经停止，这也是造成今年第三季度，科大讯飞收入的增幅低于预计的主要因素。', metadata={'source': '/content/sample_data/data/t1.txt'}), Document(page_content='科大讯飞前三季营收曝光，用AI赋能企业转型推出了多款SaaS产品', metadata={'source': '/content/sample_data/data/t1.txt'}), Document(page_content='而在十月二十八日晚上，在一份关于科技的报告中，复旦讯飞公布了2022年第三季度的财报。据财报，今年第一季度，科技讯飞的收入为126.5亿人民币，较上年同期增加16.5%。该公司在应对新冠肺炎疫情的过程中，在其核心产业领域，持续取得了较好的发展。', metadata={'source': '/content/sample_data/data/t1.txt'}), Document(page_content='据该份财报，截至今年第三季度，科技讯飞的营运资金净流入率较去年增加百分之三十二，虽然受到了严峻的经济形势影响，但科技讯飞的营运资金和营业收入仍维持着强劲的发展势头。', metadata={'source': '/content/sample_data/data/t1.txt'})]}\n"]}]}, {"cell_type": "markdown", "source": ["# 构建向量索引数据库"], "metadata": {"id": "7gGjGMljB24P"}}, {"cell_type": "code", "source": ["from langchain.text_splitter import CharacterTextSplitter\n", "from langchain.document_loaders import DirectoryLoader\n", "from langchain.vectorstores import Chroma, Pinecone\n", "from langchain.embeddings.openai import OpenAIEmbeddings\n", "from langchain.llms import OpenAI\n", "from langchain.chains.question_answering import load_qa_chain\n", "\n", "import pinecone\n", "\n", "# 初始化 pinecone\n", "pinecone.init(\n", "  api_key=\"你的api key\",\n", "  environment=\"你的Environment\"\n", ")\n", "\n", "loader = DirectoryLoader('/content/sample_data/data/', glob='**/*.txt')\n", "# 将数据转成 document 对象，每个文件会作为一个 document\n", "documents = loader.load()\n", "\n", "# 初始化加载器\n", "text_splitter = CharacterTextSplitter(chunk_size=500, chunk_overlap=0)\n", "# 切割加载的 document\n", "split_docs = text_splitter.split_documents(documents)\n", "\n", "# 初始化 openai 的 embeddings 对象\n", "embeddings = OpenAIEmbeddings()\n", "\n", "index_name=\"liaokong-test\"\n", "# 持久化数据\n", "# docsearch = Pinecone.from_texts([t.page_content for t in split_docs], embeddings, index_name=index_name)\n", "\n", "# 加载数据\n", "docsearch = Pinecone.from_existing_index(index_name,embeddings)\n", "\n", "query = \"科大讯飞今年第一季度收入是多少？\"\n", "docs = docsearch.similarity_search(query, include_metadata=True)\n", "\n", "llm = OpenAI(temperature=0)\n", "chain = load_qa_chain(llm, chain_type=\"stuff\", verbose=True)\n", "chain.run(input_documents=docs, question=query)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 588}, "id": "hSKnlX1AB-__", "outputId": "d4b5fa66-68fa-4fc1-dc92-81fdd764efa2"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new StuffDocumentsChain chain...\u001b[0m\n", "\n", "\n", "\u001b[1m> Entering new LLMChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mUse the following pieces of context to answer the question at the end. If you don't know the answer, just say that you don't know, don't try to make up an answer.\n", "\n", "科大讯飞前三季营收曝光，用AI赋能企业转型推出了多款SaaS产品\n", "\n", "而在十月二十八日晚上，在一份关于科技的报告中，复旦讯飞公布了2022年第三季度的财报。据财报，今年第一季度，科技讯飞的收入为126.5亿人民币，较上年同期增加16.5%。该公司在应对新冠肺炎疫情的过程中，在其核心产业领域，持续取得了较好的发展。\n", "\n", "据该份财报，截至今年第三季度，科技讯飞的营运资金净流入率较去年增加百分之三十二，虽然受到了严峻的经济形势影响，但科技讯飞的营运资金和营业收入仍维持着强劲的发展势头。\n", "\n", "虽然收入增加了不少，但在这一点上，科大讯飞的高层也坦言，与过去几个财季相比，第一个财季的收入增加速度明显慢了下来，这其中最重要的一个因素，便是由于第一个财季受到了病毒的冲击，导致了国内不少工程进度推迟。在讯飞的智能教育商业计划中，第三季度有10个左右的延期，合约金额接近16个。另外，安徽省方面，为强化对省内规划工程的协调，在第三季度内，大部分大型工程的投标都已经停止，这也是造成今年第三季度，科大讯飞收入的增幅低于预计的主要因素。\n", "\n", "虽然收入增加了不少，但在这一点上，科大讯飞的高层也坦言，与过去几个财季相比，第一个财季的收入增加速度明显慢了下来，这其中最重要的一个因素，便是由于第一个财季受到了病毒的冲击，导致了国内不少工程进度推迟。在讯飞的智能教育商业计划中，第三季度有10个左右的延期，合约金额接近16个。另外，安徽省方面，为强化对省内规划工程的协调，在第三季度内，大部分大型工程的投标都已经停止，这也是造成今年第三季度，科大讯飞收入的增幅低于预计的主要因素。\n", "\n", "虽然收入增加了不少，但在这一点上，科大讯飞的高层也坦言，与过去几个财季相比，第一个财季的收入增加速度明显慢了下来，这其中最重要的一个因素，便是由于第一个财季受到了病毒的冲击，导致了国内不少工程进度推迟。在讯飞的智能教育商业计划中，第三季度有10个左右的延期，合约金额接近16个。另外，安徽省方面，为强化对省内规划工程的协调，在第三季度内，大部分大型工程的投标都已经停止，这也是造成今年第三季度，科大讯飞收入的增幅低于预计的主要因素。\n", "\n", "科大讯飞前三季营收曝光，用AI赋能企业转型推出了多款SaaS产品\n", "\n", "Question: 科大讯飞今年第一季度收入是多少？\n", "Helpful Answer:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["' 根据财报，今年第一季度，科技讯飞的收入为126.5亿人民币。'"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 64}]}, {"cell_type": "markdown", "source": ["#使用GPT3.5模型构建油管频道问答机器人\n"], "metadata": {"id": "dhZGQ72NODIZ"}}, {"cell_type": "code", "source": ["import os\n", "\n", "from langchain.document_loaders import YoutubeLoader\n", "from langchain.embeddings.openai import OpenAIEmbeddings\n", "from langchain.vectorstores import Chroma\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain.chains import ChatVectorDBChain, ConversationalRetrievalChain\n", "\n", "from langchain.chat_models import ChatOpenAI\n", "from langchain.prompts.chat import (\n", "  ChatPromptTemplate,\n", "  SystemMessagePromptTemplate,\n", "  HumanMessagePromptTemplate\n", ")\n", "\n", "# 加载 youtube 频道\n", "loader = YoutubeLoader.from_youtube_channel('https://www.youtube.com/watch?v=Dj60HHy-Kqk')\n", "# 将数据转成 document\n", "documents = loader.load()\n", "\n", "# 初始化文本分割器\n", "text_splitter = RecursiveCharacterTextSplitter(\n", "  chunk_size=1000,\n", "  chunk_overlap=20\n", ")\n", "\n", "# 分割 youtube documents\n", "documents = text_splitter.split_documents(documents)\n", "\n", "# 初始化 openai embeddings\n", "embeddings = OpenAIEmbeddings()\n", "\n", "# 将数据存入向量存储\n", "vector_store = Chroma.from_documents(documents, embeddings)\n", "# 通过向量存储初始化检索器\n", "retriever = vector_store.as_retriever()\n", "\n", "system_template = \"\"\"\n", "Use the following context to answer the user's question.\n", "If you don't know the answer, say you don't, don't try to make it up. And answer in Chinese.\n", "-----------\n", "{context}\n", "-----------\n", "{chat_history}\n", "\"\"\"\n", "\n", "# 构建初始 messages 列表，这里可以理解为是 openai 传入的 messages 参数\n", "messages = [\n", "  SystemMessagePromptTemplate.from_template(system_template),\n", "  HumanMessagePromptTemplate.from_template('{question}')\n", "]\n", "\n", "# 初始化 prompt 对象\n", "prompt = ChatPromptTemplate.from_messages(messages)\n", "\n", "\n", "# 初始化问答链\n", "qa = ConversationalRetrievalChain.from_llm(ChatOpenAI(temperature=0.1,max_tokens=2048),retriever,qa_prompt=prompt)\n", "\n", "\n", "chat_history = []\n", "while True:\n", "  question = input('问题：')\n", "  # 开始发送问题 chat_history 为必须参数,用于存储对话历史\n", "  result = qa({'question': question, 'chat_history': chat_history})\n", "  chat_history.append((question, result['answer']))\n", "  print(result['answer'])"], "metadata": {"id": "LRan7KZ-MgBv", "colab": {"base_uri": "https://localhost:8080/", "height": 493}, "outputId": "3ea5d830-7ad4-4dc9-9251-f91a886a297b"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["WARNING:chromadb:Using embedded DuckDB without persistence: data will be transient\n"]}, {"output_type": "stream", "name": "stdout", "text": ["问题：这个视频讲了什么？\n", "这个视频是关于 Unreal Engine 5.2 的新实验功能的展示。在视频中介绍了一些新特性，如植被渲染、物理模拟、流体模拟、材质框架等。同时，还展示了一些环境场景的探索和汽车驾驶的游戏体验。视频还介绍了一些可用于生成可编程内容的实验工具。\n", "问题：对环境场景的探索有哪些？\n", "视频中展示了一个可编程的环境场景，包括树木、岩石、雾、虫子、鸟类等，同时还有一个四平方公里的区域可以探索，漫游和越野驾驶。视频还展示了汽车驾驶的游戏体验和实验工具的使用。\n"]}, {"output_type": "error", "ename": "KeyboardInterrupt", "evalue": "ignored", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-5-c6bb568a9c8c>\u001b[0m in \u001b[0;36m<cell line: 62>\u001b[0;34m()\u001b[0m\n\u001b[1;32m     60\u001b[0m \u001b[0mchat_history\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     61\u001b[0m \u001b[0;32mwhile\u001b[0m \u001b[0;32mTrue\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 62\u001b[0;31m   \u001b[0mquestion\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0minput\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'问题：'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     63\u001b[0m   \u001b[0;31m# 开始发送问题 chat_history 为必须参数,用于存储对话历史\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     64\u001b[0m   \u001b[0mresult\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mqa\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m{\u001b[0m\u001b[0;34m'question'\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mquestion\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'chat_history'\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mchat_history\u001b[0m\u001b[0;34m}\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.9/dist-packages/ipykernel/kernelbase.py\u001b[0m in \u001b[0;36mraw_input\u001b[0;34m(self, prompt)\u001b[0m\n\u001b[1;32m    849\u001b[0m                 \u001b[0;34m\"raw_input was called, but this frontend does not support input requests.\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    850\u001b[0m             )\n\u001b[0;32m--> 851\u001b[0;31m         return self._input_request(str(prompt),\n\u001b[0m\u001b[1;32m    852\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_parent_ident\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    853\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_parent_header\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.9/dist-packages/ipykernel/kernelbase.py\u001b[0m in \u001b[0;36m_input_request\u001b[0;34m(self, prompt, ident, parent, password)\u001b[0m\n\u001b[1;32m    893\u001b[0m             \u001b[0;32mexcept\u001b[0m \u001b[0mKeyboardInterrupt\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    894\u001b[0m                 \u001b[0;31m# re-raise KeyboardInterrupt, to truncate traceback\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 895\u001b[0;31m                 \u001b[0;32mraise\u001b[0m \u001b[0mKeyboardInterrupt\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"Interrupted by user\"\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mfrom\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    896\u001b[0m             \u001b[0;32mexcept\u001b[0m \u001b[0mException\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0me\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    897\u001b[0m                 \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mlog\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mwarning\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"Invalid Message:\"\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mexc_info\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: Interrupted by user"]}]}, {"cell_type": "markdown", "source": [], "metadata": {"id": "kRcPOsNT8mE3"}}, {"cell_type": "markdown", "source": ["#用 OpenAI 连接万种工具"], "metadata": {"id": "CVApiSmm8ocW"}}, {"cell_type": "code", "source": ["import os\n", "os.environ[\"ZAPIER_NLA_API_KEY\"] = ''"], "metadata": {"id": "VMo9CZHr80nW"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["from langchain.llms import OpenAI\n", "from langchain.agents import initialize_agent\n", "from langchain.agents.agent_toolkits import ZapierToolkit\n", "from langchain.utilities.zapier import ZapierNLAWrapper\n", "\n", "\n", "llm = OpenAI(temperature=.3)\n", "\n", "zapier = ZapierNLAWrapper()\n", "toolkit = ZapierToolkit.from_zapier_nla_wrapper(zapier)\n", "agent = initialize_agent(toolkit.get_tools(), llm, agent=\"zero-shot-react-description\", verbose=True)\n", "\n", "# 我们可以通过打印的方式看到我们都在 Zapier 里面配置了哪些可以用的工具\n", "for tool in toolkit.get_tools():\n", "  print (tool.name)\n", "  print (tool.description)\n", "  print (\"\\n\\n\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "qJp8wx3Q8tDH", "outputId": "0bc1f87b-8074-4dd0-b11e-c51935ee18b0"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Gmail: <PERSON> <PERSON>ail\n", "A wrapper around Zapier NLA actions. The input to this tool is a natural language instruction, for example \"get the latest email from my bank\" or \"send a slack message to the #general channel\". Each tool will have params associated with it that are specified as a list. You MUST take into account the params when creating the instruction. For example, if the params are ['Message_Text', 'Channel'], your instruction should be something like 'send a slack message to the #general channel with the text hello world'. Another example: if the params are ['Calendar', 'Search_Term'], your instruction should be something like 'find the meeting in my personal calendar at 3pm'. Do not make up params, they will be explicitly specified in the tool description. If you do not have enough information to fill in the params, just say 'not enough information provided in the instruction, missing <param>'. If you get a none or null response, STOP EXECUTION, do not try to another tool!This tool specifically used for: Gmail: Find Email, and has params: ['Search_String']\n", "\n", "\n", "\n", "Gmail: Send Email\n", "A wrapper around Zapier NLA actions. The input to this tool is a natural language instruction, for example \"get the latest email from my bank\" or \"send a slack message to the #general channel\". Each tool will have params associated with it that are specified as a list. You MUST take into account the params when creating the instruction. For example, if the params are ['Message_Text', 'Channel'], your instruction should be something like 'send a slack message to the #general channel with the text hello world'. Another example: if the params are ['Calendar', 'Search_Term'], your instruction should be something like 'find the meeting in my personal calendar at 3pm'. Do not make up params, they will be explicitly specified in the tool description. If you do not have enough information to fill in the params, just say 'not enough information provided in the instruction, missing <param>'. If you get a none or null response, STOP EXECUTION, do not try to another tool!This tool specifically used for: Gmail: Send Email, and has params: ['Cc', 'Body', 'To', 'Subject']\n", "\n", "\n", "\n"]}]}, {"cell_type": "code", "source": ["agent.run('请用中文总结最后一封\"*********@qq.com\"发给我的邮件。并将总结发送给\"*********@qq.com\"')"], "metadata": {"id": "O-r5lPuV9j_1"}, "execution_count": null, "outputs": []}, {"cell_type": "markdown", "source": ["# 一些有意思的小Tip"], "metadata": {"id": "vDdWamdTIw4-"}}, {"cell_type": "markdown", "source": ["##执行多个chain"], "metadata": {"id": "3bUGvgAqJfkt"}}, {"cell_type": "code", "source": ["from langchain.llms import OpenAI\n", "from langchain.chains import LLMChain\n", "from langchain.prompts import PromptTemplate\n", "from langchain.chains import SimpleSequentialChain\n", "\n", "# location 链\n", "llm = OpenAI(temperature=1)\n", "template = \"\"\"Your job is to come up with a classic dish from the area that the users suggests.\n", "% USER LOCATION\n", "{user_location}\n", "\n", "YOUR RESPONSE:\n", "\"\"\"\n", "prompt_template = PromptTemplate(input_variables=[\"user_location\"], template=template)\n", "location_chain = LLMChain(llm=llm, prompt=prompt_template)\n", "\n", "# meal 链\n", "template = \"\"\"Given a meal, give a short and simple recipe on how to make that dish at home.\n", "% MEAL\n", "{user_meal}\n", "\n", "YOUR RESPONSE:\n", "\"\"\"\n", "prompt_template = PromptTemplate(input_variables=[\"user_meal\"], template=template)\n", "meal_chain = LLMChain(llm=llm, prompt=prompt_template)\n", "\n", "# 通过 SimpleSequentialChain 串联起来，第一个答案会被替换第二个中的user_meal，然后再进行询问\n", "overall_chain = SimpleSequentialChain(chains=[location_chain, meal_chain], verbose=True)\n", "review = overall_chain.run(\"Rome\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "NQFxSP3rJlHF", "outputId": "6ba4cef5-28ff-4b4c-8a36-69ad8c19d8cb"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new SimpleSequentialChain chain...\u001b[0m\n", "\u001b[36;1m\u001b[1;3mPasta alla Carbonara.\u001b[0m\n", "\u001b[33;1m\u001b[1;3m\n", "Ingredients:\n", "- 2 cloves of garlic, minced \n", "- 2 tablespoons of olive oil \n", "- 2 cups of cooked spaghetti pasta \n", "- 4 slices of thick-cut bacon, cooked and chopped \n", "- 2 eggs, lightly beaten \n", "- 1/2 cup of Parmesan cheese, grated \n", "- Salt and black pepper to taste \n", "\n", "Directions:\n", "1. In a large skillet, heat the olive oil over medium heat and add the garlic, stirring until it is fragrant (about 2 minutes).\n", "2. Push the garlic to the side of the pan and add the cooked spaghetti pasta, stirring it around until all of the strands are coated with garlic and oil.\n", "3. Turn off the heat, add the cooked bacon and Parmesan cheese, and stir until combined.\n", "4. Add the beaten eggs and mix until evenly combined.\n", "5. Season to taste with salt and pepper, and serve warm. Enjoy!\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}]}, {"cell_type": "markdown", "source": ["##结构化输出"], "metadata": {"id": "5QVu6_HVJTlW"}}, {"cell_type": "code", "source": ["from langchain.output_parsers import StructuredOutputParser, ResponseSchema\n", "from langchain.prompts import PromptTemplate\n", "from langchain.llms import OpenAI\n", "\n", "llm = OpenAI(model_name=\"text-davinci-003\")\n", "\n", "# 告诉他我们生成的内容需要哪些字段，每个字段类型式啥\n", "response_schemas = [\n", "    ResponseSchema(name=\"bad_string\", description=\"This a poorly formatted user input string\"),\n", "    ResponseSchema(name=\"good_string\", description=\"This is your response, a reformatted response\")\n", "]\n", "\n", "# 初始化解析器\n", "output_parser = StructuredOutputParser.from_response_schemas(response_schemas)\n", "\n", "# 生成的格式提示符\n", "# {\n", "#\t\"bad_string\": string  // This a poorly formatted user input string\n", "#\t\"good_string\": string  // This is your response, a reformatted response\n", "#}\n", "format_instructions = output_parser.get_format_instructions()\n", "\n", "template = \"\"\"\n", "You will be given a poorly formatted string from a user.\n", "Reformat it and make sure all the words are spelled correctly\n", "\n", "{format_instructions}\n", "\n", "% USER INPUT:\n", "{user_input}\n", "\n", "YOUR RESPONSE:\n", "\"\"\"\n", "\n", "# 讲我们的格式描述嵌入到 prompt 中去，告诉 llm 我们需要他输出什么样格式的内容\n", "prompt = PromptTemplate(\n", "    input_variables=[\"user_input\"],\n", "    partial_variables={\"format_instructions\": format_instructions},\n", "    template=template\n", ")\n", "\n", "promptValue = prompt.format(user_input=\"welcom to califonya!\")\n", "llm_output = llm(promptValue)\n", "\n", "# 使用解析器进行解析生成的内容\n", "output_parser.parse(llm_output)"], "metadata": {"id": "xwEocODtt5Tm", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "c7c36e6b-0532-47a0-fc5d-5c729eab2c55"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["{'bad_string': 'welcom to califonya!', 'good_string': 'Welcome to California!'}"]}, "metadata": {}, "execution_count": 8}]}, {"cell_type": "markdown", "source": ["##爬取网页并输出JSON数据"], "metadata": {"id": "00kLaQZWS-Jq"}}, {"cell_type": "code", "source": ["from langchain.prompts import PromptTemplate\n", "from langchain.llms import OpenAI\n", "from langchain.chains import LLMRequestsChain, LLMChain\n", "\n", "llm = OpenAI(model_name=\"gpt-3.5-turbo\", temperature=0)\n", "\n", "template = \"\"\"在 >>> 和 <<< 之间是网页的返回的HTML内容。\n", "网页是新浪财经A股上市公司的公司简介。\n", "请抽取参数请求的信息。\n", "\n", ">>> {requests_result} <<<\n", "请使用如下的JSON格式返回数据\n", "{{\n", "  \"company_name\":\"a\",\n", "  \"company_english_name\":\"b\",\n", "  \"issue_price\":\"c\",\n", "  \"date_of_establishment\":\"d\",\n", "  \"registered_capital\":\"e\",\n", "  \"office_address\":\"f\",\n", "  \"Company_profile\":\"g\"\n", "\n", "}}\n", "Extracted:\"\"\"\n", "\n", "prompt = PromptTemplate(\n", "    input_variables=[\"requests_result\"],\n", "    template=template\n", ")\n", "\n", "chain = LLMRequestsChain(llm_chain=LLMChain(llm=llm, prompt=prompt))\n", "inputs = {\n", "  \"url\": \"https://vip.stock.finance.sina.com.cn/corp/go.php/vCI_CorpInfo/stockid/600519.phtml\"\n", "}\n", "\n", "response = chain(inputs)\n", "print(response['output'])"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "SAkS_wX8IaA1", "outputId": "75653da4-6b2b-4d9a-c2b7-5ec5a7619ea6"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["{\n", "  \"company_name\":\"贵州茅台酒股份有限公司\",\n", "  \"company_english_name\":\"Kweichow Moutai Co.,Ltd.\",\n", "  \"issue_price\":\"31.39\",\n", "  \"date_of_establishment\":\"1999-11-20\",\n", "  \"registered_capital\":\"125620万元(CNY)\",\n", "  \"office_address\":\"贵州省仁怀市茅台镇\",\n", "  \"Company_profile\":\"公司是根据贵州省人民政府黔府函〔1999〕291号文,由中国贵州茅台酒厂有限责任公司作为主发起人,联合贵州茅台酒厂技术开发公司、贵州省轻纺集体工业联社、深圳清华大学研究院、中国食品发酵工业研究院、北京市糖业烟酒公司、江苏省糖烟酒总公司、上海捷强烟草糖酒(集团)有限公司于1999年11月20日共同发起设立的股份有限公司。经中国证监会证监发行字[2001]41号文核准并按照财政部企[2001]56号文件的批复,公司于2001年7月31日在上海证券交易所公开发行7,150万(其中,国有股存量发行650万股)A股股票。主营业务：贵州茅台酒系列产品的生产与销售,饮料、食品、包装材料的生产与销售,防伪技术开发;信息产业相关产品的研制和开发等。\"\n", "}\n"]}]}, {"cell_type": "markdown", "source": ["##自定义工具"], "metadata": {"id": "2dh9Zxt6NqAY"}}, {"cell_type": "code", "source": ["from langchain.agents import initialize_agent, Tool\n", "from langchain.agents import AgentType\n", "from langchain.tools import BaseTool\n", "from langchain.llms import OpenAI\n", "from langchain import LLMMathChain, SerpAPIWrapper\n", "\n", "llm = OpenAI(temperature=0)\n", "\n", "# 初始化搜索链和计算链\n", "search = SerpAPIWrapper()\n", "llm_math_chain = LLMMathChain(llm=llm, verbose=True)\n", "\n", "# 生成一个功能列表，指明这个 agent 里面都有哪些可用工具\n", "tools = [\n", "    Tool(\n", "        name = \"Search\",\n", "        func=search.run,\n", "        description=\"useful for when you need to answer questions about current events\"\n", "    ),\n", "    Tool(\n", "        name=\"Calculator\",\n", "        func=llm_math_chain.run,\n", "        description=\"useful for when you need to answer questions about math\"\n", "    )\n", "]\n", "\n", "# 初始化 agent\n", "agent = initialize_agent(tools, llm, agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION, verbose=True)\n", "\n", "# 执行 agent\n", "agent.run(\"Who is <PERSON>'s girlfriend? What is her current age raised to the 0.43 power?\")"], "metadata": {"id": "IHMHz8vfNs6w", "colab": {"base_uri": "https://localhost:8080/", "height": 623}, "outputId": "b6ce681a-3eed-4e69-95ad-6fa76ee7429e"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "\u001b[1m> Entering new AgentExecutor chain...\u001b[0m\n", "\u001b[32;1m\u001b[1;3m I need to find out who <PERSON>'s girlfriend is and then calculate her age raised to the 0.43 power.\n", "Action: Search\n", "Action Input: \"<PERSON> girlfriend\"\u001b[0m\n", "Observation: \u001b[36;1m\u001b[1;3m<PERSON><PERSON><PERSON><PERSON> has split from girlfriend <PERSON><PERSON>. <PERSON><PERSON>. The Titanic actor hasn't been in a relationship with a woman over the age of ...\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3m I need to find out <PERSON><PERSON>'s age\n", "Action: Search\n", "Action Input: \"Camila Morrone age\"\u001b[0m\n", "Observation: \u001b[36;1m\u001b[1;3m25 years\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3m I now know the age and can calculate the power\n", "Action: Calculator\n", "Action Input: 25^0.43\u001b[0m\n", "\n", "\u001b[1m> Entering new LLMMathChain chain...\u001b[0m\n", "25^0.43\u001b[32;1m\u001b[1;3m\n", "```python\n", "import math\n", "print(math.pow(25, 0.43))\n", "```\n", "\u001b[0m\n", "Answer: \u001b[33;1m\u001b[1;3m3.991298452658078\n", "\u001b[0m\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\n", "Observation: \u001b[33;1m\u001b[1;3mAnswer: 3.991298452658078\n", "\u001b[0m\n", "Thought:\u001b[32;1m\u001b[1;3m I now know the final answer\n", "Final Answer: <PERSON><PERSON>'s age raised to the 0.43 power is 3.991298452658078.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["\"<PERSON><PERSON>'s age raised to the 0.43 power is 3.991298452658078.\""], "application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}}, "metadata": {}, "execution_count": 11}]}, {"cell_type": "markdown", "source": ["##使用Memory实现一个带记忆的对话机器人"], "metadata": {"id": "dOIICEUZvjR0"}}, {"cell_type": "code", "source": ["from langchain.memory import ChatMessageHistory\n", "from langchain.chat_models import ChatOpenAI\n", "\n", "chat = ChatOpenAI(temperature=0)\n", "\n", "# 初始化 MessageHistory 对象\n", "history = ChatMessageHistory()\n", "\n", "# 给 MessageHistory 对象添加对话内容\n", "history.add_ai_message(\"你好！\")\n", "history.add_user_message(\"中国的首都是哪里？\")\n", "\n", "# 执行对话\n", "ai_response = chat(history.messages)\n", "print(ai_response)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9qG5EGbpviKl", "outputId": "48541499-78c9-40ca-ee94-6e6a4c9e414d"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["content='中国的首都是北京。' additional_kwargs={}\n"]}]}]}
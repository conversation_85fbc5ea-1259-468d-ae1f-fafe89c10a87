{"cells": [{"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["# 第五章：格式化输出和为GPT预设响应\n", "\n", "## 设置\n", "\n", "运行以下设置单元格来加载您的API密钥并建立`get_completion`辅助函数。\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Requirement already satisfied: openai==1.61.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (1.61.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (0.10.0)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (2.11.5)\n", "Requirement already satisfied: sniffio in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.67.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from openai==1.61.0) (4.14.0)\n", "Requirement already satisfied: idna>=2.8 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from anyio<5,>=3.5.0->openai==1.61.0) (3.10)\n", "Requirement already satisfied: certifi in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (2025.4.26)\n", "Requirement already satisfied: httpcore==1.* in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai==1.61.0) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai==1.61.0) (0.16.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /root/anaconda3/envs/deepseek/lib/python3.13/site-packages (from pydantic<3,>=1.9.0->openai==1.61.0) (0.4.1)\n", "\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.\u001b[0m\u001b[33m\n", "\u001b[0mNote: you may need to restart the kernel to use updated packages.\n", "✅ OpenAI环境设置完成!\n", "🔧 OpenAI API 配置信息:\n", "  📡 配置来源: 环境变量 + 自定义API地址: https://vip.apiyi.com/v1\n", "  🤖 模型: gpt-4o\n", "  🌐 API地址: https://vip.apiyi.com/v1\n", "  🔑 API密钥: sk-R2utG...B944\n", "\n", "✅ 使用统一配置管理成功！\n"]}], "source": ["# 安装OpenAI库\n", "%pip install openai==1.61.0\n", "\n", "# 🔧 OpenAI环境自动配置\n", "# 此设置会自动从环境变量或IPython存储中加载配置\n", "\n", "# 导入Python内置的正则表达式库\n", "import re\n", "\n", "# 🚀 使用统一配置管理系统\n", "from config import setup_notebook_environment, print_config_info\n", "\n", "# 自动设置OpenAI客户端和get_completion函数\n", "# 优先级：环境变量 > IPython存储 > 默认值\n", "try:\n", "    client, get_completion = setup_notebook_environment()\n", "    print(\"✅ 使用统一配置管理成功！\")\n", "except Exception as e:\n", "    print(f\"❌ 统一配置失败，回退到传统方式: {e}\")\n", "    \n", "    # 回退到传统的配置方式\n", "    import openai\n", "    \n", "    # 从IPython存储中检索API_KEY和MODEL_NAME变量\n", "    %store -r API_KEY\n", "    %store -r MODEL_NAME\n", "\n", "    # 如果没有设置MODEL_NAME，使用默认值\n", "    try:\n", "        MODEL_NAME\n", "    except NameError:\n", "        MODEL_NAME = \"gpt-4o\"  # 默认使用gpt-4o模型\n", "\n", "    # 创建OpenAI客户端\n", "    client = openai.OpenAI(api_key=API_KEY)\n", "\n", "    # 新增了prefill参数用于预填充文本，默认值为空字符串\n", "    def get_completion(prompt: str, system_prompt=\"\", prefill=\"\"):\n", "        \"\"\"\n", "        获取GPT的完成响应\n", "        \n", "        参数:\n", "            prompt (str): 用户提示内容\n", "            system_prompt (str): 系统提示（可选）\n", "            prefill (str): 预填充文本（可选），用于引导GPT的响应开始\n", "        \n", "        返回:\n", "            str: GPT的响应文本\n", "        \"\"\"\n", "        # 构建消息列表\n", "        messages = []\n", "        \n", "        # 如果有系统提示，添加系统消息\n", "        if system_prompt:\n", "            messages.append({\"role\": \"system\", \"content\": system_prompt})\n", "        \n", "        # 添加用户消息\n", "        messages.append({\"role\": \"user\", \"content\": prompt})\n", "        \n", "        # 如果有预填充内容，添加助手消息\n", "        if prefill:\n", "            messages.append({\"role\": \"assistant\", \"content\": prefill})\n", "        \n", "        # 调用OpenAI API\n", "        response = client.chat.completions.create(\n", "            model=MODEL_NAME,              # 使用的模型名称\n", "            messages=messages,             # 消息列表\n", "            max_tokens=2000,              # 最大生成的token数量\n", "            temperature=0.0               # 温度参数，0表示更确定性的回答\n", "        )\n", "        \n", "        # 如果有预填充内容，将其与生成的内容组合\n", "        if prefill:\n", "            return prefill + response.choices[0].message.content\n", "        else:\n", "            return response.choices[0].message.content\n", "    \n", "    print(\"⚠️  使用传统配置方式，建议配置环境变量以获得更好体验\")\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "\n", "## 课程\n", "\n", "**GPT可以以多种方式格式化其输出**。您只需要明确要求它这样做！\n", "\n", "其中一种方式是使用XML标签将响应与其他多余文本分离。您已经学会了可以使用XML标签使您的提示对GPT更清晰、更易解析。事实证明，您还可以要求GPT**使用XML标签使其输出对人类更清晰、更容易理解**。\n", "\n", "### 技术要点\n", "\n", "1. **XML标签格式化**：指导GPT使用结构化的XML标签来组织输出\n", "2. **预设响应（Prefill）**：通过在assistant角色中预填充部分内容来引导GPT的响应方向\n", "3. **结构化输出**：要求GPT按照特定的格式（如JSON、表格、列表等）输出内容\n", "4. **多段落组织**：使用标记来分离不同类型的信息\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### 示例 1：使用XML标签格式化输出\n", "\n", "要求GPT使用XML标签来组织其回答，使输出更加结构化和易于解析。\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🤖 GPT回答:\n", "<分析>\n", "人工智能正在改变教育行业的多个方面，包括个性化学习、教学效率和教育资源的获取。AI技术可以通过分析学生的学习行为和成绩数据，提供定制化的学习路径和资源，从而提高学习效果。此外，AI还可以自动化许多教学任务，如评分和课程规划，减轻教师的负担并提高教学效率。\n", "</分析>\n", "\n", "<优势>\n", "1. 个性化学习：AI可以根据学生的学习习惯和能力，提供量身定制的学习计划。\n", "2. 提高教学效率：自动化评分和课程规划减少了教师的重复性工作。\n", "3. 扩大教育资源的获取：AI可以帮助开发更丰富的在线学习资源，使更多学生能够获得优质教育。\n", "4. 数据驱动决策：通过分析大量教育数据，AI可以帮助教育机构做出更明智的决策。\n", "</优势>\n", "\n", "<挑战>\n", "1. 数据隐私和安全：学生数据的收集和使用可能引发隐私和安全问题。\n", "2. 技术依赖：过度依赖AI可能导致教师角色的弱化和教育质量的下降。\n", "3. 资源不均：在技术资源有限的地区，AI的应用可能加剧教育不平等。\n", "4. 伦理问题：AI在教育中的应用可能引发伦理争议，如算法偏见和公平性问题。\n", "</挑战>\n", "\n", "<建议>\n", "1. 加强数据保护：制定严格的数据隐私政策，确保学生信息的安全。\n", "2. 平衡技术与人文：在使用AI的同时，保持教师在教育中的核心作用。\n", "3. 提供技术支持：为资源匮乏地区提供必要的技术支持，确保AI的公平应用。\n", "4. 进行伦理审查：在AI系统开发和应用过程中，进行全面的伦理审查，确保公平和透明。\n", "</建议>\n"]}], "source": ["# 示例1：XML标签格式化输出\n", "prompt = \"\"\"\n", "请分析人工智能对教育行业的影响，并使用以下XML标签格式化你的回答：\n", "\n", "<分析>\n", "在这里提供主要分析内容\n", "</分析>\n", "\n", "<优势>\n", "列出AI在教育中的优势\n", "</优势>\n", "\n", "<挑战>\n", "列出AI在教育中面临的挑战\n", "</挑战>\n", "\n", "<建议>\n", "提供实施建议\n", "</建议>\n", "\"\"\"\n", "\n", "response = get_completion(prompt)\n", "print(\"🤖 GPT回答:\")\n", "print(response)\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🤖 使用预设响应的回答:\n", "## 🐍 Python装饰器详解\n", "\n", "### 概念理解\n", "装饰器是Python中的一个重要概念，装饰器是Python中的一种设计模式，允许开发者在不修改原有函数代码的情况下，动态地增加功能。它本质上是一个函数，接受另一个函数作为参数，并返回一个新的函数。装饰器通常用于日志记录、访问控制、性能计时、缓存等场景。\n", "\n", "### 装饰器的基本结构\n", "\n", "一个简单的装饰器通常包括以下几个部分：\n", "\n", "1. **定义装饰器函数**：接受一个函数作为参数。\n", "2. **定义内部函数**：在内部函数中实现增强功能。\n", "3. **返回内部函数**：将增强后的功能返回。\n", "\n", "### 实用示例\n", "\n", "下面是一个简单的示例，展示如何使用装饰器来记录函数的执行时间：\n", "\n", "```python\n", "import time\n", "\n", "def timer_decorator(func):\n", "    def wrapper(*args, **kwargs):\n", "        start_time = time.time()  # 记录开始时间\n", "        result = func(*args, **kwargs)  # 执行原函数\n", "        end_time = time.time()  # 记录结束时间\n", "        print(f\"Function '{func.__name__}' executed in {end_time - start_time:.4f} seconds\")\n", "        return result\n", "    return wrapper\n", "\n", "# 使用装饰器\n", "@timer_decorator\n", "def example_function(n):\n", "    \"\"\"一个简单的示例函数，计算从0到n的和\"\"\"\n", "    total = 0\n", "    for i in range(n):\n", "        total += i\n", "    return total\n", "\n", "# 调用函数\n", "result = example_function(1000000)\n", "print(f\"Result: {result}\")\n", "```\n", "\n", "### 解释\n", "\n", "- **`timer_decorator`**：这是一个装饰器函数，它接受一个函数`func`作为参数。\n", "- **`wrapper`**：这是内部函数，负责记录执行时间并调用原函数。\n", "- **`@timer_decorator`**：这是装饰器的使用方式，直接在函数定义前加上`@`符号和装饰器名称。\n", "\n", "### 使用场景\n", "\n", "装饰器在实际开发中非常有用，常见的使用场景包括：\n", "\n", "- **日志记录**：自动记录函数调用信息。\n", "- **权限验证**：在函数执行前验证用户权限。\n", "- **缓存**：缓存函数结果以提高性能。\n", "- **性能监控**：监控函数执行时间。\n", "\n", "通过装饰器，开发者可以轻松地为函数添加额外的功能，而无需修改函数的核心逻辑。\n", "\n", "============================================================\n", "\n", "🤖 普通回答:\n", "装饰器是Python中的一种设计模式，允许开发者在不修改原函数代码的情况下，动态地增加或修改函数的功能。装饰器本质上是一个可调用对象，它接受一个函数作为参数，并返回一个新的函数。装饰器通常用于日志记录、访问控制、性能计时、缓存等场景。\n", "\n", "装饰器的基本语法是使用`@decorator_name`语法糖，将装饰器应用于函数。下面是一个简单的装饰器示例，它用于记录函数的执行时间：\n", "\n", "```python\n", "import time\n", "\n", "def timing_decorator(func):\n", "    def wrapper(*args, **kwargs):\n", "        start_time = time.time()  # 记录开始时间\n", "        result = func(*args, **kwargs)  # 执行原函数\n", "        end_time = time.time()  # 记录结束时间\n", "        print(f\"Function '{func.__name__}' executed in {end_time - start_time:.4f} seconds\")\n", "        return result\n", "    return wrapper\n", "\n", "@timing_decorator\n", "def example_function(n):\n", "    \"\"\"一个简单的示例函数，计算从0到n的平方和\"\"\"\n", "    total = 0\n", "    for i in range(n):\n", "        total += i ** 2\n", "    return total\n", "\n", "# 使用装饰器的函数调用\n", "result = example_function(10000)\n", "print(f\"Result: {result}\")\n", "```\n", "\n", "在这个示例中，`timing_decorator`是一个装饰器，它接受一个函数`func`作为参数，并定义了一个内部函数`wrapper`。`wrapper`在执行原函数之前和之后记录时间，并打印出函数的执行时间。通过使用`@timing_decorator`语法糖，`example_function`被装饰器包装，调用`example_function`时会自动记录其执行时间。\n", "\n", "这种模式非常强大，因为它允许开发者在不改变原函数逻辑的情况下，轻松地添加额外的功能。\n"]}], "source": ["# 示例2：使用预设响应引导GPT风格\n", "prompt = \"\"\"\n", "作为一名资深的Python开发专家，请解释什么是装饰器，并提供一个实用的示例。\n", "\"\"\"\n", "\n", "# 使用预设响应来引导GPT以特定的风格和格式回答\n", "prefill = \"\"\"## 🐍 Python装饰器详解\n", "\n", "### 概念理解\n", "装饰器是Python中的一个重要概念，\"\"\"\n", "\n", "response = get_completion(prompt, prefill=prefill)\n", "print(\"🤖 使用预设响应的回答:\")\n", "print(response)\n", "print(\"\\n\" + \"=\"*60 + \"\\n\")\n", "\n", "# 对比：不使用预设响应的回答\n", "response_normal = get_completion(prompt)\n", "print(\"🤖 普通回答:\")\n", "print(response_normal)\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### 示例 2：JSON格式输出\n", "\n", "要求GPT输出标准的JSON格式，便于程序解析和处理。\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🤖 JSON格式输出:\n", "```json\n", "[\n", "    {\n", "        \"书名\": \"活着\",\n", "        \"作者\": \"余华\",\n", "        \"ISBN\": \"9787506365437\",\n", "        \"价格\": 39.00,\n", "        \"分类\": \"文学\",\n", "        \"出版日期\": \"2012-08-01\",\n", "        \"库存数量\": 120,\n", "        \"评分\": 4.8\n", "    },\n", "    {\n", "        \"书名\": \"三体\",\n", "        \"作者\": \"刘慈欣\",\n", "        \"ISBN\": \"9787536692930\",\n", "        \"价格\": 45.00,\n", "        \"分类\": \"科幻\",\n", "        \"出版日期\": \"2008-01-01\",\n", "        \"库存数量\": 200,\n", "        \"评分\": 4.9\n", "    },\n", "    {\n", "        \"书名\": \"解忧杂货店\",\n", "        \"作者\": \"东野圭吾\",\n", "        \"ISBN\": \"9787544270878\",\n", "        \"价格\": 38.00,\n", "        \"分类\": \"小说\",\n", "        \"出版日期\": \"2014-05-01\",\n", "        \"库存数量\": 150,\n", "        \"评分\": 4.7\n", "    }\n", "]\n", "```\n"]}], "source": ["# 示例3：JSON格式输出\n", "prompt = \"\"\"\n", "请为一个在线书店设计一个图书数据结构，包含以下信息：\n", "- 书名\n", "- 作者\n", "- ISBN\n", "- 价格\n", "- 分类\n", "- 出版日期\n", "- 库存数量\n", "- 评分\n", "\n", "请以标准JSON格式输出，并提供3本示例图书的数据。\n", "\n", "输出格式要求：\n", "1. 使用中文字段名\n", "2. 价格用数字类型\n", "3. 评分范围0-5\n", "4. 出版日期格式：YYYY-MM-DD\n", "\"\"\"\n", "\n", "response = get_completion(prompt)\n", "print(\"🤖 JSON格式输出:\")\n", "print(response)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💡 提示：修改上面的 custom_prompt 变量，然后取消注释运行代码\n"]}], "source": ["# 实验1：自定义XML标签格式\n", "# 在这里尝试你自己的格式化提示词\n", "\n", "custom_prompt = \"\"\"\n", "# 请在这里输入你的提示词，尝试使用自定义的XML标签格式\n", "# 例如：分析一个技术趋势、评估一个产品、制定一个计划等\n", "\"\"\"\n", "\n", "# 取消下面的注释来运行你的实验\n", "# response = get_completion(custom_prompt)\n", "# print(\"🔬 你的实验结果:\")\n", "# print(response)\n", "\n", "print(\"💡 提示：修改上面的 custom_prompt 变量，然后取消注释运行代码\")\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎨 不同风格的预设响应效果对比：\n", "============================================================\n", "\n", "📝 学术风格:\n", "------------------------------\n", "# 区块链技术原理分析\n", "\n", "## 摘要\n", "\n", "区块链作为一种分布式账本技术，区块链技术是一种去中心化的数字记录系统，旨在确保数据的透明性、安全性和不可篡改性。它通过一系列技术和机制来实现这些目标。以下是区块链技术的工作原理的详细解释：\n", "\n", "## 1. 数据结构\n", "\n", "区块链由一系列“区块”组成，每个区块包含若干交易记录。这些区块通过加密哈希函数链接在一起，形成一个链式结构。每个区块包含以下主要元素：\n", "- **...\n", "\n", "\n", "📝 科普风格:\n", "------------------------------\n", "👋 大家好！今天我们来聊聊区块链这个神奇的技术！\n", "\n", "想象一下，当然！区块链技术可以被视为一个去中心化的分布式账本系统，它通过一系列的区块来记录和存储信息。每个区块都包含若干条交易记录，并与前一个区块通过加密算法相连，形成一个链条，故称为“区块链”。\n", "\n", "以下是区块链技术的工作原理的几个关键点：\n", "\n", "1. **去中心化**：传统的数据库通常由一个中心化的机构来管理，而区块链是分布式的，数据存储在多个节点...\n", "\n", "\n", "📝 技术文档风格:\n", "------------------------------\n", "## 区块链技术文档\n", "\n", "### 1. 概述\n", "\n", "区块链（Blockchain）是### 2. 基本原理\n", "\n", "区块链是一种分布式账本技术，允许多个参与者在没有中央权威的情况下共同维护一个数据记录。其核心思想是通过密码学和共识机制来确保数据的安全性和一致性。\n", "\n", "### 3. 结构\n", "\n", "区块链由一系列区块组成，每个区块包含若干交易记录。区块之间通过加密哈希函数链接，形成一个链式结构。每个区块通常包括以下几个部...\n", "\n", "\n", "📝 问答风格:\n", "------------------------------\n", "Q: 什么是区块链技术？\n", "A: 区块链是一种区块链是一种去中心化的分布式账本技术，用于安全地记录和验证交易。它由一系列按时间顺序链接的“区块”组成，每个区块包含若干交易记录。以下是区块链技术的基本工作原理：\n", "\n", "1. **去中心化**：区块链网络由多个节点组成，每个节点都有一份完整的账本副本。这种去中心化的结构消除了对中央权威的需求，提高了系统的透明度和安全性。\n", "\n", "2. **数据结构**：区块链中的...\n", "\n", "\n", "🔧 尝试你自己的预设响应:\n"]}], "source": ["# 实验2：预设响应风格测试\n", "# 选择一个你感兴趣的主题\n", "your_prompt = \"请解释区块链技术的工作原理\"\n", "\n", "# 尝试不同的预设响应风格\n", "prefill_styles = {\n", "    \"学术风格\": \"# 区块链技术原理分析\\n\\n## 摘要\\n\\n区块链作为一种分布式账本技术，\",\n", "    \"科普风格\": \"👋 大家好！今天我们来聊聊区块链这个神奇的技术！\\n\\n想象一下，\",\n", "    \"技术文档风格\": \"## 区块链技术文档\\n\\n### 1. 概述\\n\\n区块链（Blockchain）是\",\n", "    \"问答风格\": \"Q: 什么是区块链技术？\\nA: 区块链是一种\",\n", "}\n", "\n", "print(\"🎨 不同风格的预设响应效果对比：\")\n", "print(\"=\"*60)\n", "\n", "for style_name, prefill in prefill_styles.items():\n", "    print(f\"\\n📝 {style_name}:\")\n", "    print(\"-\" * 30)\n", "    response = get_completion(your_prompt, prefill=prefill)\n", "    # 只显示前200个字符作为预览\n", "    preview = response[:200] + \"...\" if len(response) > 200 else response\n", "    print(preview)\n", "    print()\n", "\n", "# 你也可以尝试自己的预设响应\n", "print(\"\\n🔧 尝试你自己的预设响应:\")\n", "your_prefill = \"\"\"\n", "# 在这里输入你想要的预设响应开头\n", "\"\"\"\n", "\n", "# 取消注释来测试你的预设响应\n", "# your_response = get_completion(your_prompt, prefill=your_prefill)\n", "# print(\"🎯 你的预设响应效果:\")\n", "# print(your_response)\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["### 示例 3：复合格式输出\n", "\n", "结合多种格式化技术，创建复杂的结构化输出。\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 复合格式化输出:\n", "📱 **社交媒体应用产品规划**\n", "\n", "<产品概述>\n", "本产品是一个面向年轻用户的<产品概述>\n", "本产品是一个面向年轻用户的社交媒体应用，旨在提供一个互动性强、内容丰富的平台。核心功能包括实时聊天、动态分享、兴趣小组、以及个性化推荐系统。用户可以通过该平台分享生活点滴、参与话题讨论，并发现与自己兴趣相符的内容和群体。\n", "</产品概述>\n", "\n", "<目标用户>\n", "目标用户主要是18-35岁的年轻人，他们对社交互动有较高需求，喜欢分享生活、参与讨论，并希望通过社交平台发现新内容和结识新朋友。这些用户通常是大学生、年轻职场人士以及对新技术敏感的群体。\n", "</目标用户>\n", "\n", "<功能规划>\n", "| 功能名称       | 描述                     | 优先级   |\n", "|----------------|--------------------------|----------|\n", "| 实时聊天       | 用户之间可以进行实时聊天 | 高       |\n", "| 动态分享       | 用户可以发布动态和图片   | 高       |\n", "| 兴趣小组       | 用户可以加入或创建小组   | 中       |\n", "| 个性化推荐     | 根据用户兴趣推荐内容     | 中       |\n", "| 用户资料管理   | 用户可以编辑个人资料     | 低       |\n", "| 通知系统       | 提醒用户消息和活动       | 低       |\n", "</功能规划>\n", "\n", "<技术架构>\n", "| 技术组件       | 选型对比                 | 选择理由                     |\n", "|----------------|--------------------------|------------------------------|\n", "| 前端框架       | React vs Angular         | React，因其组件化和社区支持 |\n", "| 后端框架       | Node.js vs Django        | Node.js，因其异步处理能力   |\n", "| 数据库         | MongoDB vs PostgreSQL    | MongoDB，因其灵活的文档结构 |\n", "| 云服务         | AWS vs Google Cloud      | AWS，因其成熟的服务和支持   |\n", "</技术架构>\n", "\n", "<开发计划>\n", "```json\n", "[\n", "  {\n", "    \"阶段\": \"需求分析\",\n", "    \"任务\": \"确定核心功能和用户需求\",\n", "    \"时间\": \"2023-11-01 至 2023-11-15\"\n", "  },\n", "  {\n", "    \"阶段\": \"设计\",\n", "    \"任务\": \"UI/UX设计和技术架构设计\",\n", "    \"时间\": \"2023-11-16 至 2023-12-15\"\n", "  },\n", "  {\n", "    \"阶段\": \"开发\",\n", "    \"任务\": \"前端和后端开发\",\n", "    \"时间\": \"2023-12-16 至 2024-02-15\"\n", "  },\n", "  {\n", "    \"阶段\": \"测试\",\n", "    \"任务\": \"功能测试和用户测试\",\n", "    \"时间\": \"2024-02-16 至 2024-03-15\"\n", "  },\n", "  {\n", "    \"阶段\": \"上线准备\",\n", "    \"任务\": \"部署和市场推广准备\",\n", "    \"时间\": \"2024-03-16 至 2024-03-31\"\n", "  }\n", "]\n", "```\n", "</开发计划>\n", "\n", "<API示例>\n", "```python\n", "from flask import Flask, request, jsonify\n", "\n", "app = Flask(__name__)\n", "\n", "@app.route('/register', methods=['POST'])\n", "def register_user():\n", "    data = request.get_json()\n", "    username = data.get('username')\n", "    password = data.get('password')\n", "    \n", "    # 假设有一个函数 save_user_to_db 用于保存用户信息\n", "    if save_user_to_db(username, password):\n", "        return jsonify({\"message\": \"注册成功\"}), 201\n", "    else:\n", "        return jsonify({\"message\": \"注册失败\"}), 400\n", "\n", "def save_user_to_db(username, password):\n", "    # 这里是保存用户信息到数据库的逻辑\n", "    return True\n", "\n", "if __name__ == '__main__':\n", "    app.run(debug=True)\n", "```\n", "</API示例>\n"]}], "source": ["# 实验3：复合格式输出\n", "prompt = \"\"\"\n", "请为一个新的社交媒体应用制定产品规划，要求：\n", "\n", "1. 使用XML标签组织内容结构\n", "2. 在技术部分使用表格格式\n", "3. 在时间规划部分使用JSON格式\n", "4. 包含代码示例使用代码块格式\n", "\n", "具体要求：\n", "<产品概述>\n", "简要描述产品定位和核心功能\n", "</产品概述>\n", "\n", "<目标用户>\n", "用户画像分析\n", "</目标用户>\n", "\n", "<功能规划>\n", "核心功能列表和优先级（用表格格式）\n", "</功能规划>\n", "\n", "<技术架构>\n", "技术选型对比表格（包含前端、后端、数据库等）\n", "</技术架构>\n", "\n", "<开发计划>\n", "6个月的开发时间线（JSON格式，包含阶段、任务、时间等字段）\n", "</开发计划>\n", "\n", "<API示例>\n", "提供用户注册接口的代码示例\n", "</API示例>\n", "\"\"\"\n", "\n", "# 使用预设响应确保格式正确\n", "prefill = \"\"\"📱 **社交媒体应用产品规划**\n", "\n", "<产品概述>\n", "本产品是一个面向年轻用户的\"\"\"\n", "\n", "response = get_completion(prompt, prefill=prefill)\n", "print(\"🔄 复合格式化输出:\")\n", "print(response)\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "\n", "## 📚 总结与最佳实践\n", "\n", "### 格式化输出的关键要点：\n", "\n", "1. **明确性**：明确告诉GPT你期望的输出格式\n", "2. **一致性**：使用一致的标签和结构来组织信息\n", "3. **可解析性**：确保输出格式便于程序或人类解析\n", "4. **适用性**：根据用途选择合适的格式（JSON、XML、表格等）\n", "\n", "### 预设响应的最佳实践：\n", "\n", "1. **风格引导**：通过预设响应建立特定的回答风格\n", "2. **格式控制**：确保输出格式的一致性\n", "3. **专业角色**：让GPT扮演特定的专业角色\n", "4. **长度控制**：预设响应应该简洁但足够引导方向\n", "\n", "### 实际应用场景：\n", "\n", "- 📊 **数据分析报告**：使用XML标签组织分析结果\n", "- 🔧 **技术文档**：结合代码块和表格展示技术方案\n", "- 📋 **项目计划**：使用JSON格式输出结构化的项目信息\n", "- 🎯 **产品评估**：通过预设响应确保评估的专业性和一致性\n", "\n", "通过掌握这些格式化技术，你可以让GPT输出更加结构化、专业化的内容，大大提升工作效率！\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 4}